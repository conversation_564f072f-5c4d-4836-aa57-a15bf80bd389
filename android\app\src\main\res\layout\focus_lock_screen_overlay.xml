<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/focus_background_gradient"
    android:fitsSystemWindows="true">

    <!-- 主要专注界面内容 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="32dp">

        <!-- 专注状态指示器 -->
        <TextView
            android:id="@+id/focus_status_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="🧘 专注模式"
            android:textSize="16sp"
            android:textColor="#CCFFFFFF"
            android:layout_marginBottom="24dp"
            android:fontFamily="sans-serif-medium" />

        <!-- 专注倒计时显示 -->
        <TextView
            android:id="@+id/focus_timer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="24:35"
            android:textSize="72sp"
            android:textColor="#FFFFFF"
            android:fontFamily="monospace"
            android:textStyle="bold"
            android:letterSpacing="0.1"
            android:layout_marginBottom="16dp" />

        <!-- 时间单位说明 -->
        <TextView
            android:id="@+id/time_unit_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="分钟:秒钟"
            android:textSize="12sp"
            android:textColor="#99FFFFFF"
            android:layout_marginBottom="32dp" />

        <!-- 专注进度条容器 -->
        <LinearLayout
            android:layout_width="280dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="40dp">

            <!-- 进度条 -->
            <ProgressBar
                android:id="@+id/focus_progress"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="8dp"
                android:progressTint="#00BCD4"
                android:progressBackgroundTint="#33FFFFFF"
                android:progress="65"
                android:max="100" />

            <!-- 进度文字 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="8dp">

                <TextView
                    android:id="@+id/progress_start_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="开始"
                    android:textSize="10sp"
                    android:textColor="#66FFFFFF"
                    android:gravity="start" />

                <TextView
                    android:id="@+id/progress_center_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="65%"
                    android:textSize="10sp"
                    android:textColor="#99FFFFFF"
                    android:gravity="center" />

                <TextView
                    android:id="@+id/progress_end_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="完成"
                    android:textSize="10sp"
                    android:textColor="#66FFFFFF"
                    android:gravity="end" />

            </LinearLayout>

        </LinearLayout>

        <!-- 任务信息区域 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginBottom="32dp">

            <!-- 任务类型 -->
            <TextView
                android:id="@+id/task_type_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="深度工作进行中"
                android:textSize="18sp"
                android:textColor="#CCFFFFFF"
                android:fontFamily="sans-serif-medium"
                android:layout_marginBottom="8dp" />

            <!-- 任务描述 -->
            <TextView
                android:id="@+id/task_description_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="专注编程开发"
                android:textSize="14sp"
                android:textColor="#99FFFFFF"
                android:layout_marginBottom="16dp" />

        </LinearLayout>

        <!-- 激励文案区域 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginBottom="24dp">

            <!-- 主激励文案 -->
            <TextView
                android:id="@+id/motivation_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="保持专注，你正在做一件了不起的事"
                android:textSize="14sp"
                android:textColor="#99FFFFFF"
                android:gravity="center"
                android:lineSpacingExtra="4dp"
                android:layout_marginBottom="8dp" />

            <!-- 副激励文案 -->
            <TextView
                android:id="@+id/sub_motivation_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="每一分钟的专注都在塑造更好的自己"
                android:textSize="12sp"
                android:textColor="#66FFFFFF"
                android:gravity="center" />

        </LinearLayout>

        <!-- 专注统计信息 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <!-- 今日专注次数 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:layout_marginEnd="24dp">

                <TextView
                    android:id="@+id/daily_count_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="3"
                    android:textSize="20sp"
                    android:textColor="#00BCD4"
                    android:fontFamily="sans-serif-medium" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="今日次数"
                    android:textSize="10sp"
                    android:textColor="#66FFFFFF" />

            </LinearLayout>

            <!-- 累计专注时长 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:layout_marginStart="24dp">

                <TextView
                    android:id="@+id/total_time_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="125"
                    android:textSize="20sp"
                    android:textColor="#00BCD4"
                    android:fontFamily="sans-serif-medium" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="累计分钟"
                    android:textSize="10sp"
                    android:textColor="#66FFFFFF" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <!-- 隐藏的紧急退出触发区域 -->
    <View
        android:id="@+id/emergency_exit_trigger"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_gravity="top|start"
        android:layout_margin="16dp"
        android:background="@android:color/transparent"
        android:clickable="true"
        android:focusable="true" />

    <!-- 紧急退出提示（默认隐藏） -->
    <LinearLayout
        android:id="@+id/emergency_exit_hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="top|start"
        android:layout_margin="24dp"
        android:orientation="vertical"
        android:background="@drawable/emergency_hint_background"
        android:padding="12dp"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="🚨 紧急退出"
            android:textSize="12sp"
            android:textColor="#FF5722"
            android:fontFamily="sans-serif-medium" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="连续点击10次"
            android:textSize="10sp"
            android:textColor="#FF8A65"
            android:layout_marginTop="2dp" />

    </LinearLayout>

    <!-- 底部状态栏（显示连接状态等） -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:orientation="horizontal"
        android:gravity="center"
        android:padding="16dp"
        android:background="@drawable/bottom_status_background">

        <TextView
            android:id="@+id/lock_status_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="🔒 深度锁定模式"
            android:textSize="10sp"
            android:textColor="#66FFFFFF"
            android:layout_marginEnd="16dp" />

        <TextView
            android:id="@+id/connection_status_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="● 系统连接正常"
            android:textSize="10sp"
            android:textColor="#4CAF50" />

    </LinearLayout>

</FrameLayout>
