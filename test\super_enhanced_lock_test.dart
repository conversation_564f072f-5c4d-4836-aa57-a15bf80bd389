import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:lockphone/services/super_enhanced_lock.dart';

void main() {
  group('SuperEnhancedLock Tests', () {
    late SuperEnhancedLock lockService;

    setUp(() {
      lockService = SuperEnhancedLock.instance;
    });

    tearDown(() async {
      // 确保每次测试后都禁用锁定
      if (lockService.isLockActive) {
        await lockService.disableLock();
      }
      lockService.resetBackPressCount();
    });

    test('should be singleton', () {
      final instance1 = SuperEnhancedLock.instance;
      final instance2 = SuperEnhancedLock.instance;
      expect(instance1, same(instance2));
    });

    test('should start with lock inactive', () {
      expect(lockService.isLockActive, false);
      expect(lockService.backPressCount, 0);
      expect(lockService.escapeAttempts, 0);
    });

    test('should handle back press correctly when lock is inactive', () {
      expect(lockService.isLockActive, false);
      final result = lockService.handleBackPress();
      expect(result, false); // Should not intercept when inactive
      expect(lockService.backPressCount, 0);
    });

    test('should increment back press count when lock is active', () async {
      // 注意：在测试环境中，enableLock可能会失败，因为没有真实的Android环境
      // 我们可以通过反射或其他方式来模拟锁定状态

      // 模拟锁定状态（在实际实现中，你可能需要添加一个测试用的方法）
      // lockService._isLockActive = true; // 这需要在实际代码中添加测试支持

      // 由于无法直接访问私有变量，我们测试公共接口
      expect(lockService.backPressThreshold, 15);
    });

    test('should reset back press count correctly', () {
      lockService.resetBackPressCount();
      expect(lockService.backPressCount, 0);
    });

    test('should have correct threshold values', () {
      expect(lockService.backPressThreshold, 15);
    });

    test('should track escape attempts', () {
      // 初始状态
      expect(lockService.escapeAttempts, 0);

      // 在实际使用中，escapeAttempts会在handleBackPress中增加
      // 这里我们只能测试getter
    });
  });

  group('SuperEnhancedLockScreen Widget Tests', () {
    testWidgets('should display task name and remaining time',
        (WidgetTester tester) async {
      const taskName = 'Test Task';
      const remainingSeconds = 1500; // 25 minutes

      await tester.pumpWidget(
        const MaterialApp(
          home: SuperEnhancedLockScreen(
            taskName: taskName,
            remainingSeconds: remainingSeconds,
          ),
        ),
      );

      // 验证任务名称显示
      expect(find.text(taskName), findsOneWidget);

      // 验证时间格式显示（25:00）
      expect(find.text('25:00'), findsOneWidget);

      // 验证超级增强锁定标识
      expect(find.text('🔒 超级增强锁定'), findsOneWidget);

      // 验证专注提示
      expect(find.text('超级专注进行中...'), findsOneWidget);
    });

    testWidgets('should format time correctly', (WidgetTester tester) async {
      // 测试不同的时间格式
      const testCases = [
        {'seconds': 3661, 'expected': '01:01:01'}, // 1小时1分1秒
        {'seconds': 1500, 'expected': '25:00'}, // 25分钟
        {'seconds': 59, 'expected': '00:59'}, // 59秒
        {'seconds': 0, 'expected': '00:00'}, // 0秒
      ];

      for (final testCase in testCases) {
        await tester.pumpWidget(
          MaterialApp(
            home: SuperEnhancedLockScreen(
              taskName: 'Test',
              remainingSeconds: testCase['seconds'] as int,
            ),
          ),
        );

        expect(find.text(testCase['expected'] as String), findsOneWidget);
      }
    });

    testWidgets('should handle back press correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: SuperEnhancedLockScreen(
            taskName: 'Test Task',
            remainingSeconds: 1500,
          ),
        ),
      );

      // 验证widget渲染成功
      expect(find.byType(SuperEnhancedLockScreen), findsOneWidget);

      // 验证Scaffold存在
      expect(find.byType(Scaffold), findsOneWidget);
    });

    testWidgets('should display security icon and description',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: SuperEnhancedLockScreen(
            taskName: 'Test Task',
            remainingSeconds: 1500,
          ),
        ),
      );

      // 验证安全图标
      expect(find.byIcon(Icons.security), findsOneWidget);

      // 验证描述文本
      expect(find.textContaining('超级增强锁定模式'), findsOneWidget);
      expect(find.textContaining('多重防护阻止逃逸'), findsOneWidget);
    });
  });

  group('Integration Tests', () {
    test('should maintain state consistency', () {
      final lockService = SuperEnhancedLock.instance;

      // 初始状态检查
      expect(lockService.isLockActive, false);
      expect(lockService.backPressCount, 0);
      expect(lockService.escapeAttempts, 0);

      // 重置后状态检查
      lockService.resetBackPressCount();
      expect(lockService.backPressCount, 0);
    });
  });
}
