<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>锁手机MVP设计 - Android Flutter版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: #ffffff;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        h1 {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 30px;
            background: linear-gradient(45deg, #03DAC6, #6200EE);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: 700;
        }
        
        .mvp-principles {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 32px;
            border: 1px solid rgba(255, 255, 255, 0.12);
        }
        
        .section {
            margin-bottom: 48px;
        }
        
        .section h2 {
            color: #03DAC6;
            margin-bottom: 24px;
            font-size: 1.8em;
            font-weight: 600;
        }
        
        .section h3 {
            color: #BB86FC;
            margin-bottom: 16px;
            font-size: 1.4em;
            font-weight: 500;
        }
        
        .mockups-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 32px;
            margin-bottom: 48px;
        }
        
        .phone-mockup {
            background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
            border-radius: 24px;
            padding: 16px;
            position: relative;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(3, 218, 198, 0.2);
        }
        
        .phone-screen {
            background: #121212;
            border-radius: 20px;
            padding: 20px;
            height: 640px;
            position: relative;
            overflow: hidden;
            border: 2px solid #2a2a2a;
        }
        
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 24px;
            margin-bottom: 16px;
            font-size: 0.8em;
            color: #888;
        }
        
        .app-bar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
            margin-bottom: 24px;
        }
        
        .app-title {
            font-size: 1.4em;
            font-weight: 600;
            color: #03DAC6;
        }
        
        .main-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            height: calc(100% - 80px);
            justify-content: center;
        }
        
        .fab {
            position: absolute;
            bottom: 16px;
            right: 16px;
            width: 56px;
            height: 56px;
            background: #03DAC6;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(3, 218, 198, 0.4);
            transition: all 0.3s ease;
        }
        
        .fab:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 24px rgba(3, 218, 198, 0.6);
        }
        
        .timer-display {
            font-size: 4em;
            font-weight: 300;
            color: #03DAC6;
            font-family: 'Roboto Mono', monospace;
            margin-bottom: 16px;
            text-align: center;
        }
        
        .progress-indicator {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            border: 8px solid rgba(3, 218, 198, 0.2);
            border-top: 8px solid #03DAC6;
            animation: spin 2s linear infinite;
            margin-bottom: 32px;
            position: relative;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.2em;
            color: #fff;
            text-align: center;
        }
        
        .material-button {
            background: #6200EE;
            color: #ffffff;
            border: none;
            border-radius: 24px;
            padding: 16px 32px;
            font-size: 1.1em;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 16px rgba(98, 0, 238, 0.3);
        }
        
        .material-button:hover {
            background: #7C4DFF;
            transform: translateY(-2px);
            box-shadow: 0 6px 24px rgba(98, 0, 238, 0.4);
        }
        
        .outlined-button {
            background: transparent;
            color: #03DAC6;
            border: 2px solid #03DAC6;
            border-radius: 24px;
            padding: 14px 32px;
            font-size: 1.1em;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .outlined-button:hover {
            background: rgba(3, 218, 198, 0.1);
            transform: translateY(-2px);
        }
        
        .card {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            border: 1px solid rgba(255, 255, 255, 0.12);
        }
        
        .stat-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.08);
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: 600;
            color: #03DAC6;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 0.9em;
            color: #888;
        }
        
        .time-picker {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin: 24px 0;
        }
        
        .time-chip {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: 16px;
            padding: 8px 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9em;
        }
        
        .time-chip.selected {
            background: #03DAC6;
            color: #000;
            border-color: #03DAC6;
        }
        
        .feature-list {
            list-style: none;
            margin: 20px 0;
        }
        
        .feature-list li {
            padding: 12px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
            color: #e0e0e0;
            position: relative;
            padding-left: 24px;
        }
        
        .feature-list li::before {
            content: "✓";
            color: #03DAC6;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
        
        .mockup-title {
            text-align: center;
            margin-bottom: 16px;
            font-size: 1.2em;
            color: #03DAC6;
            font-weight: 500;
        }
        
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 56px;
            background: #1e1e1e;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid rgba(255, 255, 255, 0.08);
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #888;
            font-size: 0.8em;
        }
        
        .nav-item.active {
            color: #03DAC6;
        }
        
        .nav-icon {
            font-size: 1.2em;
            margin-bottom: 4px;
        }
        
        .permission-card {
            background: rgba(255, 183, 77, 0.1);
            border: 1px solid rgba(255, 183, 77, 0.3);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
        }
        
        .permission-title {
            color: #FFB74D;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .permission-desc {
            color: #e0e0e0;
            font-size: 0.9em;
            margin-bottom: 16px;
        }
        
        .emergency-button {
            background: #CF6679;
            color: #ffffff;
            border: none;
            border-radius: 24px;
            padding: 12px 24px;
            font-size: 1em;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .emergency-button:hover {
            background: #E57373;
            transform: translateY(-2px);
        }
        
        .toast {
            position: fixed;
            bottom: 80px;
            left: 50%;
            transform: translateX(-50%);
            background: #323232;
            color: #fff;
            padding: 12px 24px;
            border-radius: 24px;
            font-size: 0.9em;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            opacity: 0;
            animation: fadeInOut 3s ease-in-out;
        }
        
        @keyframes fadeInOut {
            0%, 100% { opacity: 0; }
            20%, 80% { opacity: 1; }
        }
        
        @media (max-width: 768px) {
            .mockups-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 16px;
            }
            
            h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 锁手机 MVP设计方案</h1>
        
        <div class="mvp-principles">
            <h2>MVP核心原则</h2>
            <ul class="feature-list">
                <li>极简功能 - 只保留核心锁机功能</li>
                <li>Android优先 - 利用Android权限和系统特性</li>
                <li>Flutter适配 - 考虑Flutter开发的技术特点</li>
                <li>用户体验 - 遵循Material Design设计规范</li>
                <li>快速迭代 - 可快速验证和优化的设计</li>
            </ul>
        </div>
        
        <div class="section">
            <h2>🎨 设计规范</h2>
            
            <div class="card">
                <h3>Material Design 3.0</h3>
                <p>采用Google最新的Material Design设计语言，确保Android用户的熟悉感：</p>
                <ul class="feature-list">
                    <li>Dynamic Color - 支持系统主题色彩</li>
                    <li>Typography - 使用Roboto字体系统</li>
                    <li>Elevation - 合理的阴影层级</li>
                    <li>Motion - 流畅的转场动画</li>
                </ul>
            </div>
            
            <div class="card">
                <h3>颜色系统</h3>
                <div style="display: flex; gap: 16px; margin-top: 16px; flex-wrap: wrap;">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <div style="width: 32px; height: 32px; background: #03DAC6; border-radius: 8px;"></div>
                        <span>Primary #03DAC6</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <div style="width: 32px; height: 32px; background: #6200EE; border-radius: 8px;"></div>
                        <span>Secondary #6200EE</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <div style="width: 32px; height: 32px; background: #121212; border-radius: 8px;"></div>
                        <span>Surface #121212</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <div style="width: 32px; height: 32px; background: #CF6679; border-radius: 8px;"></div>
                        <span>Error #CF6679</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mockups-grid">
            <!-- 启动页 -->
            <div class="phone-mockup">
                <div class="mockup-title">启动页</div>
                <div class="phone-screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋 100%</span>
                    </div>
                    <div class="main-content">
                        <div style="font-size: 4em; margin-bottom: 24px;">🔒</div>
                        <h2 style="color: #03DAC6; margin-bottom: 16px;">锁手机</h2>
                        <p style="color: #888; text-align: center; margin-bottom: 32px;">专注时刻，远离干扰</p>
                        <button class="material-button" style="width: 200px;">开始使用</button>
                    </div>
                </div>
            </div>
            
            <!-- 权限申请 -->
            <div class="phone-mockup">
                <div class="mockup-title">权限申请</div>
                <div class="phone-screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋 100%</span>
                    </div>
                    <div class="app-bar">
                        <span class="app-title">权限设置</span>
                        <span style="color: #888;">1/2</span>
                    </div>
                    <div style="padding: 20px;">
                        <div class="permission-card">
                            <div class="permission-title">📱 设备管理权限</div>
                            <div class="permission-desc">需要此权限来锁定屏幕和管理应用使用</div>
                            <button class="material-button">授权</button>
                        </div>
                        <div class="permission-card">
                            <div class="permission-title">⚠️ 应用使用权限</div>
                            <div class="permission-desc">用于监控其他应用的使用状态</div>
                            <button class="outlined-button">跳过</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 主界面 -->
            <div class="phone-mockup">
                <div class="mockup-title">主界面</div>
                <div class="phone-screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋 100%</span>
                    </div>
                    <div class="app-bar">
                        <span class="app-title">🔒 锁手机</span>
                        <span style="color: #888;">⚙️</span>
                    </div>
                    <div style="padding: 20px;">
                        <div class="stat-grid">
                            <div class="stat-card">
                                <div class="stat-value">2</div>
                                <div class="stat-label">今日锁机</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">45</div>
                                <div class="stat-label">分钟</div>
                            </div>
                        </div>
                        <div class="card">
                            <h3 style="margin-bottom: 16px;">⏰ 快速开始</h3>
                            <div class="time-picker">
                                <div class="time-chip selected">25分钟</div>
                                <div class="time-chip">45分钟</div>
                                <div class="time-chip">90分钟</div>
                                <div class="time-chip">自定义</div>
                            </div>
                            <button class="material-button" style="width: 100%;">开始锁机</button>
                        </div>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item active">
                            <div class="nav-icon">🏠</div>
                            <div>首页</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon">📊</div>
                            <div>统计</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon">⚙️</div>
                            <div>设置</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 锁机中 -->
            <div class="phone-mockup">
                <div class="mockup-title">锁机状态</div>
                <div class="phone-screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋 100%</span>
                    </div>
                    <div class="main-content">
                        <div class="progress-indicator">
                            <div class="progress-text">
                                <div class="timer-display" style="font-size: 2em;">24:35</div>
                                <div style="color: #888; font-size: 0.8em;">剩余时间</div>
                            </div>
                        </div>
                        <div style="text-align: center; margin-bottom: 32px;">
                            <div style="font-size: 1.2em; margin-bottom: 8px;">手机已锁定</div>
                            <div style="color: #888; font-size: 0.9em;">"专注当下，拒绝干扰"</div>
                        </div>
                        <div style="position: absolute; bottom: 100px; left: 50%; transform: translateX(-50%);">
                            <div style="color: #888; font-size: 0.8em; text-align: center; margin-bottom: 16px;">
                                紧急情况长按解锁
                            </div>
                            <button class="emergency-button">紧急解锁</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 完成页 -->
            <div class="phone-mockup">
                <div class="mockup-title">完成页</div>
                <div class="phone-screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋 100%</span>
                    </div>
                    <div class="main-content">
                        <div style="font-size: 4em; margin-bottom: 24px;">🎉</div>
                        <h2 style="color: #03DAC6; margin-bottom: 16px;">恭喜完成！</h2>
                        <p style="color: #888; text-align: center; margin-bottom: 32px;">你已成功专注25分钟</p>
                        <div class="stat-grid" style="width: 100%;">
                            <div class="stat-card">
                                <div class="stat-value">25:00</div>
                                <div class="stat-label">本次时长</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">3</div>
                                <div class="stat-label">今日完成</div>
                            </div>
                        </div>
                        <div style="display: flex; gap: 16px; margin-top: 32px;">
                            <button class="outlined-button">再来一次</button>
                            <button class="material-button">查看详情</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 统计页 -->
            <div class="phone-mockup">
                <div class="mockup-title">统计页</div>
                <div class="phone-screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋 100%</span>
                    </div>
                    <div class="app-bar">
                        <span class="app-title">专注统计</span>
                        <span style="color: #888;">📊</span>
                    </div>
                    <div style="padding: 20px;">
                        <div class="stat-grid">
                            <div class="stat-card">
                                <div class="stat-value">12</div>
                                <div class="stat-label">总次数</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">5.2</div>
                                <div class="stat-label">小时</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">7</div>
                                <div class="stat-label">连续天数</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">85%</div>
                                <div class="stat-label">完成率</div>
                            </div>
                        </div>
                        <div class="card">
                            <h3 style="margin-bottom: 16px;">本周趋势</h3>
                            <div style="height: 120px; background: rgba(255, 255, 255, 0.05); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #888;">
                                图表占位
                            </div>
                        </div>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div class="nav-icon">🏠</div>
                            <div>首页</div>
                        </div>
                        <div class="nav-item active">
                            <div class="nav-icon">📊</div>
                            <div>统计</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon">⚙️</div>
                            <div>设置</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>🚀 MVP功能清单</h2>
            
            <div class="card">
                <h3>核心功能（必须）</h3>
                <ul class="feature-list">
                    <li>基础锁机 - 锁定屏幕，限制应用使用</li>
                    <li>倒计时 - 可视化显示剩余时间</li>
                    <li>紧急解锁 - 安全机制防止意外情况</li>
                    <li>简单统计 - 记录使用次数和时长</li>
                </ul>
            </div>
            
            <div class="card">
                <h3>辅助功能（可选）</h3>
                <ul class="feature-list">
                    <li>快速时间选择 - 25/45/90分钟预设</li>
                    <li>完成庆祝 - 增强成就感</li>
                    <li>基础设置 - 声音、震动等</li>
                    <li>数据导出 - 用户数据备份</li>
                </ul>
            </div>
        </div>
        
        <div class="section">
            <h2>🔧 技术实现要点</h2>
            
            <div class="card">
                <h3>Android权限管理</h3>
                <ul class="feature-list">
                    <li>DEVICE_ADMIN - 设备管理权限，用于锁屏</li>
                    <li>USAGE_STATS - 应用使用统计权限</li>
                    <li>FOREGROUND_SERVICE - 前台服务权限</li>
                    <li>WAKE_LOCK - 保持屏幕唤醒权限</li>
                </ul>
            </div>
            
            <div class="card">
                <h3>Flutter开发考量</h3>
                <ul class="feature-list">
                    <li>使用 method_channel 与原生Android交互</li>
                    <li>material_design 包提供MD3组件</li>
                    <li>shared_preferences 本地数据存储</li>
                    <li>flutter_local_notifications 通知管理</li>
                </ul>
            </div>
            
            <div class="card">
                <h3>系统集成</h3>
                <ul class="feature-list">
                    <li>监听系统按键事件（返回键、Home键）</li>
                    <li>创建悬浮窗口覆盖其他应用</li>
                    <li>管理应用生命周期和内存</li>
                    <li>处理系统主题和暗黑模式</li>
                </ul>
            </div>
        </div>
        
        <div class="section">
            <h2>📱 用户体验优化</h2>
            
            <div class="card">
                <h3>Material Design原则</h3>
                <ul class="feature-list">
                    <li>遵循Android用户习惯的交互模式</li>
                    <li>使用系统级动画和转场效果</li>
                    <li>支持Android暗黑模式切换</li>
                    <li>适配不同屏幕尺寸和分辨率</li>
                </ul>
            </div>
            
            <div class="card">
                <h3>渐进式引导</h3>
                <ul class="feature-list">
                    <li>首次启动的权限申请流程</li>
                    <li>功能介绍的轻量级引导</li>
                    <li>错误状态的友好提示</li>
                    <li>离线状态的优雅降级</li>
                </ul>
            </div>
        </div>
        