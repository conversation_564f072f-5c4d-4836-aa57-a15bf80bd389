@echo off
echo ========================================
echo 专注锁屏应用 - 系统权限授予工具
echo ========================================
echo.

echo 正在检查ADB连接...
adb devices
if %ERRORLEVEL% neq 0 (
    echo 错误：ADB未找到或设备未连接
    echo 请确保：
    echo 1. 已安装Android SDK Platform Tools
    echo 2. 设备已连接并启用USB调试
    echo 3. 已授权此计算机的USB调试
    pause
    exit /b 1
)

echo.
echo 1. 授予WRITE_SECURE_SETTINGS权限（核心权限）
adb shell pm grant com.example.lockphone android.permission.WRITE_SECURE_SETTINGS
if %ERRORLEVEL% neq 0 (
    echo 警告：WRITE_SECURE_SETTINGS权限授予失败
    echo 尝试替代方法...
    adb shell "su -c 'pm grant com.example.lockphone android.permission.WRITE_SECURE_SETTINGS'"
)

echo.
echo 2. 授予WRITE_SETTINGS权限（辅助权限）
adb shell pm grant com.example.lockphone android.permission.WRITE_SETTINGS

echo.
echo 3. 设置应用为设备管理员（增强权限）
adb shell dpm set-device-owner com.example.lockphone/.DeviceAdminReceiver 2>nul

echo.
echo 4. 禁用电池优化（防止服务被杀）
adb shell dumpsys deviceidle whitelist +com.example.lockphone

echo.
echo 5. 检查权限授予状态
echo 检查WRITE_SECURE_SETTINGS权限：
adb shell dumpsys package com.example.lockphone | findstr "android.permission.WRITE_SECURE_SETTINGS"

echo.
echo 检查WRITE_SETTINGS权限：
adb shell dumpsys package com.example.lockphone | findstr "android.permission.WRITE_SETTINGS"

echo.
echo 6. 测试无障碍服务控制
echo 尝试启用无障碍服务...
adb shell settings put secure enabled_accessibility_services com.example.lockphone/.YoYoAccessibilityService
adb shell settings put secure accessibility_enabled 1

echo 等待2秒...
timeout /t 2 /nobreak >nul

echo 检查无障碍服务状态...
adb shell settings get secure enabled_accessibility_services | findstr "lockphone"
if %ERRORLEVEL% equ 0 (
    echo ✅ 无障碍服务控制测试成功
    echo 禁用测试服务...
    adb shell settings put secure enabled_accessibility_services ""
    adb shell settings put secure accessibility_enabled 0
) else (
    echo ❌ 无障碍服务控制测试失败
)

echo.
echo ========================================
echo 权限授予完成！
echo ========================================
echo.
echo 状态说明：
echo ✅ 成功：应用可以自动控制无障碍服务
echo ❌ 失败：需要手动启用无障碍服务
echo.
echo 如果仍然失败，请尝试：
echo 1. 确保设备已Root（某些设备需要）
echo 2. 关闭设备的安全策略限制
echo 3. 在开发者选项中允许"USB安装"
echo 4. 重启设备后重新运行此脚本
echo.
pause
