<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>锁手机 - 权限申请流程优化</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: #ffffff;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        h1 {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 30px;
            background: linear-gradient(45deg, #03DAC6, #6200EE);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: 700;
        }
        
        .mockups-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 32px;
            margin-bottom: 48px;
        }
        
        .phone-mockup {
            background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
            border-radius: 24px;
            padding: 16px;
            position: relative;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(3, 218, 198, 0.2);
        }
        
        .phone-screen {
            background: #121212;
            border-radius: 20px;
            padding: 20px;
            height: 640px;
            position: relative;
            overflow: hidden;
            border: 2px solid #2a2a2a;
        }
        
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 24px;
            margin-bottom: 16px;
            font-size: 0.8em;
            color: #888;
        }
        
        .app-bar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
            margin-bottom: 24px;
        }
        
        .app-title {
            font-size: 1.4em;
            font-weight: 600;
            color: #03DAC6;
        }
        
        .mockup-title {
            text-align: center;
            margin-bottom: 16px;
            font-size: 1.2em;
            color: #03DAC6;
            font-weight: 500;
        }
        
        .progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
            margin-bottom: 24px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #03DAC6, #6200EE);
            border-radius: 2px;
            transition: width 0.3s ease;
        }
        
        .permission-intro {
            text-align: center;
            margin-bottom: 32px;
        }
        
        .intro-icon {
            font-size: 4em;
            margin-bottom: 16px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .intro-title {
            font-size: 1.8em;
            color: #03DAC6;
            margin-bottom: 8px;
            font-weight: 600;
        }
        
        .intro-desc {
            color: #888;
            font-size: 1em;
            line-height: 1.4;
        }
        
        .permission-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 16px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .permission-card:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        }
        
        .permission-card.granted {
            border-color: rgba(3, 218, 198, 0.5);
            background: rgba(3, 218, 198, 0.1);
        }
        
        .permission-card.required {
            border-color: rgba(255, 183, 77, 0.5);
            background: rgba(255, 183, 77, 0.08);
        }
        
        .permission-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .permission-icon {
            font-size: 2.5em;
            margin-right: 16px;
            filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.3));
        }
        
        .permission-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #fff;
            margin-bottom: 4px;
        }
        
        .permission-subtitle {
            color: #888;
            font-size: 0.9em;
        }
        
        .permission-desc {
            color: #e0e0e0;
            font-size: 0.95em;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .permission-benefits {
            margin-bottom: 20px;
        }
        
        .benefit-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            color: #c0c0c0;
            font-size: 0.9em;
        }
        
        .benefit-icon {
            color: #03DAC6;
            margin-right: 8px;
            font-size: 1.1em;
        }
        
        .permission-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        
        .material-button {
            background: #6200EE;
            color: #ffffff;
            border: none;
            border-radius: 24px;
            padding: 12px 24px;
            font-size: 1em;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 16px rgba(98, 0, 238, 0.3);
        }
        
        .material-button:hover {
            background: #7C4DFF;
            transform: translateY(-2px);
            box-shadow: 0 6px 24px rgba(98, 0, 238, 0.4);
        }
        
        .material-button.primary {
            background: #03DAC6;
            color: #000;
            box-shadow: 0 4px 16px rgba(3, 218, 198, 0.3);
        }
        
        .material-button.primary:hover {
            background: #00BFA5;
            box-shadow: 0 6px 24px rgba(3, 218, 198, 0.4);
        }
        
        .text-button {
            background: transparent;
            color: #888;
            border: none;
            padding: 12px 16px;
            font-size: 0.9em;
            cursor: pointer;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }
        
        .text-button:hover {
            color: #03DAC6;
        }
        
        .status-indicator {
            position: absolute;
            top: 16px;
            right: 16px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .status-granted {
            background: #03DAC6;
            color: #000;
        }
        
        .status-required {
            background: #FFB74D;
            color: #000;
        }
        
        .status-optional {
            background: rgba(255, 255, 255, 0.1);
            color: #888;
        }
        
        .bottom-actions {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            display: flex;
            gap: 12px;
        }
        
        .skip-hint {
            text-align: center;
            color: #666;
            font-size: 0.8em;
            margin-top: 16px;
        }
        
        .tips-section {
            background: rgba(3, 218, 198, 0.1);
            border: 1px solid rgba(3, 218, 198, 0.2);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 20px;
        }
        
        .tips-title {
            display: flex;
            align-items: center;
            color: #03DAC6;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .tips-icon {
            margin-right: 8px;
            font-size: 1.2em;
        }
        
        .tips-text {
            color: #e0e0e0;
            font-size: 0.9em;
            line-height: 1.4;
        }
        
        .success-animation {
            animation: successPulse 1s ease-in-out;
        }
        
        @keyframes successPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin-bottom: 24px;
        }
        
        .step-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        
        .step-dot.active {
            background: #03DAC6;
            width: 24px;
            border-radius: 12px;
        }
        
        .step-dot.completed {
            background: #03DAC6;
        }
        
        .help-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 32px;
        }
        
        .help-title {
            font-size: 1.4em;
            color: #03DAC6;
            margin-bottom: 16px;
            font-weight: 600;
        }
        
        .help-item {
            margin-bottom: 16px;
            padding-bottom: 16px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
        }
        
        .help-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        
        .help-question {
            color: #fff;
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .help-answer {
            color: #c0c0c0;
            font-size: 0.9em;
            line-height: 1.4;
        }
        
        @media (max-width: 768px) {
            .mockups-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 16px;
            }
            
            h1 {
                font-size: 2em;
            }
            
            .permission-actions {
                flex-direction: column;
            }
            
            .material-button {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 权限申请流程优化</h1>
        
        <div class="mockups-grid">
            <!-- 权限介绍页 -->
            <div class="phone-mockup">
                <div class="mockup-title">权限介绍页</div>
                <div class="phone-screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋 100%</span>
                    </div>
                    <div class="app-bar">
                        <span class="app-title">设置权限</span>
                        <span style="color: #888;">1/3</span>
                    </div>
                    <div class="step-indicator">
                        <div class="step-dot active"></div>
                        <div class="step-dot"></div>
                        <div class="step-dot"></div>
                    </div>
                    <div class="permission-intro">
                        <div class="intro-icon">🛡️</div>
                        <div class="intro-title">为什么需要权限？</div>
                        <div class="intro-desc">
                            为了更好地帮助你专注，我们需要一些系统权限来锁定手机和记录使用情况
                        </div>
                    </div>
                    <div class="tips-section">
                        <div class="tips-title">
                            <span class="tips-icon">💡</span>
                            安全承诺
                        </div>
                        <div class="tips-text">
                            所有权限仅用于锁机功能，不会收集个人隐私信息，数据仅存储在本地
                        </div>
                    </div>
                    <div class="bottom-actions">
                        <button class="text-button">稍后设置</button>
                        <button class="material-button primary">开始设置</button>
                    </div>
                </div>
            </div>
            
            <!-- 核心权限申请 -->
            <div class="phone-mockup">
                <div class="mockup-title">核心权限申请</div>
                <div class="phone-screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋 100%</span>
                    </div>
                    <div class="app-bar">
                        <span class="app-title">设置权限</span>
                        <span style="color: #888;">2/3</span>
                    </div>
                    <div class="step-indicator">
                        <div class="step-dot completed"></div>
                        <div class="step-dot active"></div>
                        <div class="step-dot"></div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 33%;"></div>
                    </div>
                    <div style="padding: 0 4px; max-height: 480px; overflow-y: auto;">
                        <div class="permission-card required">
                            <div class="status-indicator status-required">!</div>
                            <div class="permission-header">
                                <div class="permission-icon">🔒</div>
                                <div>
                                    <div class="permission-title">设备管理权限</div>
                                    <div class="permission-subtitle">必需权限</div>
                                </div>
                            </div>
                            <div class="permission-desc">
                                此权限用于锁定屏幕和阻止应用启动，是核心功能的基础
                            </div>
                            <div class="permission-benefits">
                                <div class="benefit-item">
                                    <span class="benefit-icon">✓</span>
                                    锁定屏幕和应用
                                </div>
                                <div class="benefit-item">
                                    <span class="benefit-icon">✓</span>
                                    防止误操作解锁
                                </div>
                                <div class="benefit-item">
                                    <span class="benefit-icon">✓</span>
                                    紧急情况快速解锁
                                </div>
                            </div>
                            <div class="permission-actions">
                                <button class="material-button primary">立即授权</button>
                                <button class="text-button">了解详情</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 可选权限申请 -->
            <div class="phone-mockup">
                <div class="mockup-title">可选权限申请</div>
                <div class="phone-screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋 100%</span>
                    </div>
                    <div class="app-bar">
                        <span class="app-title">设置权限</span>
                        <span style="color: #888;">3/3</span>
                    </div>
                    <div class="step-indicator">
                        <div class="step-dot completed"></div>
                        <div class="step-dot completed"></div>
                        <div class="step-dot active"></div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 67%;"></div>
                    </div>
                    <div style="padding: 0 4px; max-height: 480px; overflow-y: auto;">
                        <div class="permission-card">
                            <div class="status-indicator status-optional">?</div>
                            <div class="permission-header">
                                <div class="permission-icon">📊</div>
                                <div>
                                    <div class="permission-title">应用使用统计</div>
                                    <div class="permission-subtitle">可选权限</div>
                                </div>
                            </div>
                            <div class="permission-desc">
                                用于统计应用使用情况，帮助生成专注报告
                            </div>
                            <div class="permission-benefits">
                                <div class="benefit-item">
                                    <span class="benefit-icon">✓</span>
                                    生成详细使用报告
                                </div>
                                <div class="benefit-item">
                                    <span class="benefit-icon">✓</span>
                                    分析专注效果
                                </div>
                                <div class="benefit-item">
                                    <span class="benefit-icon">✓</span>
                                    个性化建议
                                </div>
                            </div>
                            <div class="permission-actions">
                                <button class="material-button">授权</button>
                                <button class="text-button">跳过</button>
                            </div>
                        </div>
                    </div>
                    <div class="bottom-actions">
                        <button class="material-button primary" style="width: 100%;">完成设置</button>
                    </div>
                </div>
            </div>
            
            <!-- 权限已授权状态 -->
            <div class="phone-mockup">
                <div class="mockup-title">权限已授权</div>
                <div class="phone-screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋 100%</span>
                    </div>
                    <div class="app-bar">
                        <span class="app-title">权限管理</span>
                        <span style="color: #888;">⚙️</span>
                    </div>
                    <div style="padding: 0 4px; max-height: 520px; overflow-y: auto;">
                        <div class="permission-card granted success-animation">
                            <div class="status-indicator status-granted">✓</div>
                            <div class="permission-header">
                                <div class="permission-icon">🔒</div>
                                <div>
                                    <div class="permission-title">设备管理权限</div>
                                    <div class="permission-subtitle">已授权</div>
                                </div>
                            </div>
                            <div class="permission-desc">
                                权限已成功授权，现在可以正常使用锁机功能
                            </div>
                            <div class="permission-actions">
                                <button class="text-button">管理权限</button>
                                <button class="text-button">撤销授权</button>
                            </div>
                        </div>
                        
                        <div class="permission-card granted">
                            <div class="status-indicator status-granted">✓</div>
                            <div class="permission-header">
                                <div class="permission-icon">📊</div>
                                <div>
                                    <div class="permission-title">应用使用统计</div>
                                    <div class="permission-subtitle">已授权</div>
                                </div>
                            </div>
                            <div class="permission-desc">
                                可以生成详细的使用报告和专注统计
                            </div>
                            <div class="permission-actions">
                                <button class="text-button">查看统计</button>
                                <button class="text-button">撤销授权</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 权限问题帮助 -->
            <div class="phone-mockup">
                <div class="mockup-title">权限帮助</div>
                <div class="phone-screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋 100%</span>
                    </div>
                    <div class="app-bar">
                        <span class="app-title">权限帮助</span>
                        <span style="color: #888;">❓</span>
                    </div>
                    <div style="padding: 0 4px; max-height: 520px; overflow-y: auto;">
                        <div class="help-section">
                            <div class="help-title">常见问题</div>
                            
                            <div class="help-item">
                                <div class="help-question">为什么需要设备管理权限？</div>
                                <div class="help-answer">
                                    此权限用于锁定屏幕和阻止应用启动，是锁机功能的核心。没有此权限，应用无法正常工作。
                                </div>
                            </div>
                            
                            <div class="help-item">
                                <div class="help-question">如何手动授权权限？</div>
                                <div class="help-answer">
                                    前往 设置 → 应用管理 → 锁手机 → 权限管理，开启相关权限即可。
                                </div>
                            </div>
                            
                            <div class="help-item">
                                <div class="help-question">应用会收集我的隐私吗？</div>
                                <div class="help-answer">
                                    不会。所有数据都存储在本地，仅用于锁机功能和使用统计，不会上传到服务器。
                                </div>
                            </div>
                            
                            <div class="help-item">
                                <div class="help-question">可以撤销权限吗？</div>
                                <div class="help-answer">
                                    可以。在权限管理页面或系统设置中随时撤销权限，但会影响相关功能的使用。
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bottom-actions">
                        <button class="material-button primary" style="width: 100%;">返回设置</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="help-section">
            <div class="help-title">🚀 权限申请流程优化要点</div>
            
            <div class="help-item">
                <div class="help-question">1. 渐进式引导</div>
                <div class="help-answer">
                    通过引导页解释权限的必要性，降低用户的抗拒心理，提高授权成功率。
                </div>
            </div>
            
            <div class="help-item">
                <div class="help-question">2. 权限分类</div>
                <div class="help-answer">
                    区分必需权限和可选权限，让用户明确哪些是核心功能必需的，哪些是增强功能。
                </div>
            </div>
            
            <div class="help-item">
                <div class="help-question">3. 视觉反馈</div>
                <div class="help-answer">
                    使用进度条、状态指示器等元素，让用户清楚当前进度和权限状态。
                </div>
            </div>
            
            <div class="help-item">
                <div class="help-question">4. 透明沟通</div>
                <div class="help-answer">
                    详细说明每个权限的作用和好处，消除用户的疑虑，建立信任感。
                </div>
            </div>
            
            <div class="help-item">
                <div class="help-question">5. 灵活选择</div>
                <div class="help-answer">
                    提供"跳过"和"稍后设置"选项，不强制用户立即授权，尊重用户选择。
                </div>
            </div>
            
            <div class="help-item">
                <div class="help-question">6. 帮助支持</div>
                <div class="help-answer">
                    提供详细的帮助文档和FAQ，帮助用户解决权限相关问题。
                </div>
            </div>
        </div>
    </div>
</body>
</html>