import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:lockphone/services/streamlined_permission_flow.dart';
import 'package:lockphone/services/unified_lock_manager.dart';
import 'package:lockphone/services/enhanced_basic_lock.dart';
import 'package:lockphone/services/permission_cache_service.dart';
import 'package:lockphone/services/focus_manager.dart' as focus;

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('简化授权流程测试', () {
    late StreamlinedPermissionFlow permissionFlow;

    setUp(() async {
      SharedPreferences.setMockInitialValues({});
      permissionFlow = StreamlinedPermissionFlow.instance;

      await PermissionCacheService.instance.init();

      // 模拟方法通道
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        const MethodChannel('yoyo_lock_screen'),
        (MethodCall methodCall) async {
          switch (methodCall.method) {
            case 'checkPermission':
              final type = methodCall.arguments['type'];
              switch (type) {
                case 'storage':
                  return 'granted';
                case 'overlay':
                  return 'denied';
                case 'accessibility':
                  return 'denied';
                default:
                  return 'denied';
              }
            case 'requestPermission':
              return true;
            case 'enableLockScreen':
              return true;
            case 'disableLockScreen':
              return true;
            default:
              return null;
          }
        },
      );
    });

    tearDown(() async {
      await PermissionCacheService.instance.clearAllPermissionCache();
    });

    test('权限分级检查', () async {
      // 测试基础锁定权限检查
      final basicResult =
          await permissionFlow.checkLockLevelPermissions(focus.LockLevel.basic);
      expect(basicResult.allGranted, true); // 只需要存储权限
      expect(basicResult.canProceed, true);

      // 测试增强锁定权限检查
      final enhancedResult = await permissionFlow
          .checkLockLevelPermissions(focus.LockLevel.enhanced);
      expect(enhancedResult.allGranted, false); // 缺少悬浮窗权限
      expect(enhancedResult.canProceed, true); // 但可以降级

      // 测试深度锁定权限检查
      final deepResult =
          await permissionFlow.checkLockLevelPermissions(focus.LockLevel.deep);
      expect(deepResult.allGranted, false); // 缺少多个权限
      expect(deepResult.canProceed, true); // 但可以降级
    });

    test('智能降级机制', () async {
      final checkResult =
          await permissionFlow.checkLockLevelPermissions(focus.LockLevel.deep);

      // 测试降级逻辑
      final availableLevel = permissionFlow.getAvailableLockLevel(
        focus.LockLevel.deep,
        checkResult.permissionStatus,
      );

      expect(availableLevel, focus.LockLevel.basic); // 应该降级到基础锁定
    });

    testWidgets('权限请求流程', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              return Scaffold(
                body: ElevatedButton(
                  onPressed: () async {
                    final result =
                        await permissionFlow.requestPermissionsForLevel(
                      context,
                      focus.LockLevel.enhanced,
                    );

                    expect(result.success, true);
                    expect(result.finalLevel, focus.LockLevel.basic); // 降级到基础锁定
                  },
                  child: const Text('测试权限请求'),
                ),
              );
            },
          ),
        ),
      );

      await tester.tap(find.byType(ElevatedButton));
      await tester.pumpAndSettle();
    });
  });

  group('增强基础锁定测试', () {
    late EnhancedBasicLock basicLock;

    setUp(() {
      basicLock = EnhancedBasicLock.instance;
    });

    test('返回键拦截机制', () {
      // 启用锁定
      basicLock.enableLock();
      expect(basicLock.isLockActive, true);

      // 测试返回键拦截
      for (int i = 1; i < 10; i++) {
        final shouldBlock = basicLock.handleBackPress();
        expect(shouldBlock, true); // 前9次应该被拦截
        expect(basicLock.backPressCount, i);
      }

      // 第10次应该允许退出
      final shouldBlock = basicLock.handleBackPress();
      expect(shouldBlock, false); // 第10次应该允许退出
    });

    test('返回键时间窗口', () async {
      basicLock.enableLock();

      // 按下5次返回键
      for (int i = 0; i < 5; i++) {
        basicLock.handleBackPress();
      }
      expect(basicLock.backPressCount, 5);

      // 等待超过时间窗口
      await Future.delayed(const Duration(seconds: 4));

      // 再次按下返回键，计数应该重置
      basicLock.handleBackPress();
      expect(basicLock.backPressCount, 1); // 计数重置
    });

    test('锁定状态管理', () async {
      expect(basicLock.isLockActive, false);

      await basicLock.enableLock();
      expect(basicLock.isLockActive, true);

      await basicLock.disableLock();
      expect(basicLock.isLockActive, false);
      expect(basicLock.backPressCount, 0); // 计数重置
    });
  });

  group('统一锁定管理器测试', () {
    late UnifiedLockManager lockManager;

    setUp(() {
      lockManager = UnifiedLockManager.instance;
    });

    testWidgets('锁定级别自动选择', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              return Scaffold(
                body: ElevatedButton(
                  onPressed: () async {
                    // 请求深度锁定，但权限不足，应该自动降级
                    final result = await lockManager.enableLock(
                        context, focus.LockLevel.deep);

                    expect(result.success, true);
                    expect(result.actualLevel, focus.LockLevel.basic); // 自动降级
                    expect(result.requestedLevel, focus.LockLevel.deep);
                  },
                  child: const Text('测试锁定'),
                ),
              );
            },
          ),
        ),
      );

      await tester.tap(find.byType(ElevatedButton));
      await tester.pumpAndSettle();
    });

    test('锁定状态信息', () {
      final status = lockManager.getLockStatus();
      expect(status.isActive, false);
      expect(status.currentLevel, null);
    });

    test('锁定级别描述', () {
      final basicDesc =
          lockManager.getLockLevelDescription(focus.LockLevel.basic);
      expect(basicDesc.contains('基础锁定'), true);
      expect(basicDesc.contains('10次'), true);

      final enhancedDesc =
          lockManager.getLockLevelDescription(focus.LockLevel.enhanced);
      expect(enhancedDesc.contains('增强锁定'), true);

      final deepDesc =
          lockManager.getLockLevelDescription(focus.LockLevel.deep);
      expect(deepDesc.contains('深度锁定'), true);
    });

    test('锁定级别图标', () {
      expect(lockManager.getLockLevelIcon(focus.LockLevel.basic),
          Icons.lock_outline);
      expect(
          lockManager.getLockLevelIcon(focus.LockLevel.enhanced), Icons.lock);
      expect(
          lockManager.getLockLevelIcon(focus.LockLevel.deep), Icons.security);
    });
  });

  group('集成测试', () {
    testWidgets('完整的专注流程', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              return Scaffold(
                body: ElevatedButton(
                  onPressed: () async {
                    final focusManager = focus.FocusManager.instance;

                    // 使用新的统一锁定流程
                    final success =
                        await focusManager.startFocusWithUnifiedLock(
                      context,
                      taskType: '测试任务',
                      durationMinutes: 25,
                      lockLevel: focus.LockLevel.deep,
                    );

                    expect(success, true);
                    expect(focusManager.state, focus.FocusState.focusing);
                  },
                  child: const Text('开始专注'),
                ),
              );
            },
          ),
        ),
      );

      await tester.tap(find.byType(ElevatedButton));
      await tester.pumpAndSettle();
    });
  });

  group('错误处理测试', () {
    test('权限检查失败处理', () async {
      // 模拟权限检查失败
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        const MethodChannel('com.example.lockphone/permissions'),
        (MethodCall methodCall) async {
          throw PlatformException(code: 'ERROR', message: '权限检查失败');
        },
      );

      final result = await StreamlinedPermissionFlow.instance
          .checkLockLevelPermissions(focus.LockLevel.basic);

      // 应该有容错处理
      expect(result.canProceed, false);
    });

    test('锁定启用失败处理', () async {
      final basicLock = EnhancedBasicLock.instance;

      // 多次启用应该是安全的
      await basicLock.enableLock();
      await basicLock.enableLock(); // 重复启用

      expect(basicLock.isLockActive, true);

      await basicLock.disableLock();
      expect(basicLock.isLockActive, false);
    });
  });
}
