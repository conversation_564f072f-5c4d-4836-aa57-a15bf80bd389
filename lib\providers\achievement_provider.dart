import 'package:flutter/foundation.dart';
import '../models/achievement.dart';
import '../services/database_service.dart';

class AchievementProvider extends ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService.instance;

  List<Achievement> _achievements = [];
  final List<Achievement> _newlyUnlocked = [];
  bool _isLoading = false;

  // Getters
  List<Achievement> get achievements => _achievements;
  List<Achievement> get unlockedAchievements =>
      _achievements.where((a) => a.isUnlocked).toList();
  List<Achievement> get lockedAchievements =>
      _achievements.where((a) => !a.isUnlocked).toList();
  List<Achievement> get newlyUnlocked => _newlyUnlocked;
  bool get isLoading => _isLoading;

  int get totalAchievements => _achievements.length;
  int get unlockedCount => unlockedAchievements.length;
  double get completionPercentage =>
      totalAchievements > 0 ? (unlockedCount / totalAchievements) : 0.0;

  /// Initialize achievements from database
  Future<void> loadAchievements() async {
    _isLoading = true;
    notifyListeners();

    try {
      // Load saved achievements from database
      final savedAchievements = await _loadSavedAchievements();

      // Get all achievement definitions
      final allDefinitions = AchievementDefinitions.allAchievements;

      // Merge saved data with definitions
      _achievements = allDefinitions.map((definition) {
        final saved = savedAchievements.firstWhere(
          (s) => s.id == definition.id,
          orElse: () => definition,
        );
        return saved;
      }).toList();
    } catch (e) {
      debugPrint('加载成就失败: $e');
      // Fallback to default achievements
      _achievements = AchievementDefinitions.allAchievements;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Check for new achievements based on current statistics
  Future<List<Achievement>> checkNewAchievements(
      Map<String, dynamic> stats) async {
    _newlyUnlocked.clear();

    for (final achievement in _achievements) {
      if (achievement.isUnlocked) continue;

      bool shouldUnlock = false;

      switch (achievement.type) {
        case AchievementType.firstSession:
          shouldUnlock = (stats['totalSessions'] ?? 0) >= 1;
          break;

        case AchievementType.sessionCount:
          shouldUnlock =
              (stats['totalSessions'] ?? 0) >= achievement.targetValue;
          break;

        case AchievementType.totalTime:
          shouldUnlock =
              (stats['totalMinutes'] ?? 0) >= achievement.targetValue;
          break;

        case AchievementType.streak:
          shouldUnlock =
              (stats['currentStreak'] ?? 0) >= achievement.targetValue;
          break;

        case AchievementType.longSession:
          // Check if any session duration meets the target
          shouldUnlock = await _hasLongSession(achievement.targetValue);
          break;

        case AchievementType.completionRate:
          shouldUnlock =
              (stats['completionRate'] ?? 0) >= achievement.targetValue;
          break;

        case AchievementType.taskType:
          // TODO: Implement task type specific achievements
          break;
      }

      if (shouldUnlock) {
        await _unlockAchievement(achievement);
      }
    }

    return List.from(_newlyUnlocked);
  }

  /// Unlock a specific achievement
  Future<void> _unlockAchievement(Achievement achievement) async {
    final unlockedAchievement = achievement.copyWith(
      isUnlocked: true,
      unlockedAt: DateTime.now(),
    );

    // Update in memory
    final index = _achievements.indexWhere((a) => a.id == achievement.id);
    if (index != -1) {
      _achievements[index] = unlockedAchievement;
      _newlyUnlocked.add(unlockedAchievement);
    }

    // Save to database
    await _saveAchievement(unlockedAchievement);

    notifyListeners();
  }

  /// Clear newly unlocked achievements (after showing them to user)
  void clearNewlyUnlocked() {
    _newlyUnlocked.clear();
    notifyListeners();
  }

  /// Get achievements by category
  List<Achievement> getAchievementsByType(AchievementType type) {
    return _achievements.where((a) => a.type == type).toList();
  }

  /// Get progress for a specific achievement
  Future<double> getAchievementProgress(Achievement achievement) async {
    if (achievement.isUnlocked) return 1.0;

    final totalSessions = await _databaseService.getTotalCompletedSessions();
    final totalTime = await _databaseService.getTotalFocusTime();

    switch (achievement.type) {
      case AchievementType.firstSession:
        return totalSessions >= 1 ? 1.0 : 0.0;

      case AchievementType.sessionCount:
        return (totalSessions / achievement.targetValue).clamp(0.0, 1.0);

      case AchievementType.totalTime:
        return (totalTime / achievement.targetValue).clamp(0.0, 1.0);

      case AchievementType.streak:
        // For now, return 0.0 as we don't have streak calculation
        return 0.0;

      case AchievementType.longSession:
        final hasLong = await _hasLongSession(achievement.targetValue);
        return hasLong ? 1.0 : 0.0;

      case AchievementType.completionRate:
        // For now, return 0.0 as we don't have completion rate calculation
        return 0.0;

      case AchievementType.taskType:
        return 0.0; // TODO: Implement
    }
  }

  /// Check if user has completed a session of target duration
  Future<bool> _hasLongSession(int targetMinutes) async {
    final sessions = await _databaseService.getFocusSessions();
    return sessions.any((session) =>
        session.completed && session.durationMinutes >= targetMinutes);
  }

  /// Load saved achievements from database
  Future<List<Achievement>> _loadSavedAchievements() async {
    try {
      return await _databaseService.getAchievements();
    } catch (e) {
      debugPrint('加载成就数据失败: $e');
      return [];
    }
  }

  /// Save achievement to database
  Future<void> _saveAchievement(Achievement achievement) async {
    try {
      await _databaseService.saveAchievement(achievement);
      debugPrint('成就已保存: ${achievement.title}');
    } catch (e) {
      debugPrint('保存成就失败: $e');
    }
  }

  /// Get recent achievements (last 7 days)
  List<Achievement> getRecentAchievements() {
    final sevenDaysAgo = DateTime.now().subtract(const Duration(days: 7));

    return unlockedAchievements.where((achievement) {
      return achievement.unlockedAt != null &&
          achievement.unlockedAt!.isAfter(sevenDaysAgo);
    }).toList()
      ..sort((a, b) => b.unlockedAt!.compareTo(a.unlockedAt!));
  }

  /// Get achievement statistics
  Map<String, dynamic> getAchievementStats() {
    final byType = <AchievementType, int>{};

    for (final achievement in unlockedAchievements) {
      byType[achievement.type] = (byType[achievement.type] ?? 0) + 1;
    }

    return {
      'total': totalAchievements,
      'unlocked': unlockedCount,
      'completion': completionPercentage,
      'byType': byType,
      'recent': getRecentAchievements().length,
    };
  }
}
