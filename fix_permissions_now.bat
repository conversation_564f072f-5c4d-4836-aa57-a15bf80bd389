@echo off
echo ========================================
echo 专注锁屏应用 - 权限修复工具
echo ========================================
echo.
echo 设备信息: OnePlus PJZ110 (Android 15)
echo 问题: 缺少WRITE_SECURE_SETTINGS权限
echo.

echo 正在检查ADB连接...
adb devices
if %ERRORLEVEL% neq 0 (
    echo.
    echo ❌ 错误：ADB未找到或设备未连接
    echo.
    echo 解决方法：
    echo 1. 下载并安装 Android SDK Platform Tools
    echo 2. 确保设备已连接并启用USB调试
    echo 3. 在设备上授权此计算机的USB调试
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo 开始授予权限...
echo ========================================

echo.
echo [1/4] 授予核心权限 WRITE_SECURE_SETTINGS...
adb shell pm grant com.example.lockphone android.permission.WRITE_SECURE_SETTINGS
if %ERRORLEVEL% equ 0 (
    echo ✅ WRITE_SECURE_SETTINGS 权限授予成功
) else (
    echo ❌ WRITE_SECURE_SETTINGS 权限授予失败
    echo 这是最重要的权限，如果失败请尝试：
    echo 1. 重启设备后重试
    echo 2. 在开发者选项中启用"USB安装"
    echo 3. 关闭设备的安全模式
)

echo.
echo [2/4] 授予辅助权限 WRITE_SETTINGS...
adb shell pm grant com.example.lockphone android.permission.WRITE_SETTINGS
if %ERRORLEVEL% equ 0 (
    echo ✅ WRITE_SETTINGS 权限授予成功
) else (
    echo ❌ WRITE_SETTINGS 权限授予失败
)

echo.
echo [3/4] 添加到电池优化白名单...
adb shell dumpsys deviceidle whitelist +com.example.lockphone
if %ERRORLEVEL% equ 0 (
    echo ✅ 电池优化白名单添加成功
) else (
    echo ❌ 电池优化白名单添加失败
)

echo.
echo [4/4] 测试无障碍服务控制...
echo 测试启用无障碍服务...
adb shell settings put secure enabled_accessibility_services com.example.lockphone/.YoYoAccessibilityService
adb shell settings put secure accessibility_enabled 1

timeout /t 2 /nobreak >nul

echo 检查服务状态...
adb shell settings get secure enabled_accessibility_services | findstr "lockphone" >nul
if %ERRORLEVEL% equ 0 (
    echo ✅ 无障碍服务控制测试成功
    echo 恢复原始状态...
    adb shell settings put secure enabled_accessibility_services ""
    adb shell settings put secure accessibility_enabled 0
) else (
    echo ❌ 无障碍服务控制测试失败
)

echo.
echo ========================================
echo 权限修复完成！
echo ========================================

echo.
echo 最终检查权限状态...
adb shell dumpsys package com.example.lockphone | findstr "WRITE_SECURE_SETTINGS: granted=true" >nul
if %ERRORLEVEL% equ 0 (
    echo ✅ WRITE_SECURE_SETTINGS: 已授予
    set SECURE_OK=1
) else (
    echo ❌ WRITE_SECURE_SETTINGS: 未授予
    set SECURE_OK=0
)

adb shell dumpsys package com.example.lockphone | findstr "WRITE_SETTINGS: granted=true" >nul
if %ERRORLEVEL% equ 0 (
    echo ✅ WRITE_SETTINGS: 已授予
) else (
    echo ❌ WRITE_SETTINGS: 未授予
)

echo.
if %SECURE_OK%==1 (
    echo 🎉 权限修复成功！
    echo.
    echo 现在您可以：
    echo 1. 返回专注锁屏应用
    echo 2. 选择"深度锁定"级别
    echo 3. 应用将自动启用无障碍服务
    echo 4. 专注结束后自动关闭服务
) else (
    echo ⚠️  核心权限授予失败
    echo.
    echo 备用解决方案：
    echo 1. 重启设备后重新运行此脚本
    echo 2. 在应用中选择"手动设置"
    echo 3. 手动启用无障碍服务：
    echo    设置 → 无障碍 → 专注锁屏服务
)

echo.
echo 按任意键退出...
pause >nul
