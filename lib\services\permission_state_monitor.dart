import 'dart:async';
import 'package:flutter/foundation.dart';
import 'permission_cache_service.dart';
import 'permission_manager.dart';

/// 权限状态监听器
/// 定期检查权限状态变化并同步缓存
class PermissionStateMonitor {
  static PermissionStateMonitor? _instance;
  static PermissionStateMonitor get instance => _instance ??= PermissionStateMonitor._();
  
  PermissionStateMonitor._();

  Timer? _monitorTimer;
  bool _isMonitoring = false;
  
  // 监听间隔（秒）
  static const int _monitorInterval = 30; // 30秒检查一次
  
  // 权限状态变化回调
  final List<Function(PermissionType, PermissionStatus, PermissionStatus)> _stateChangeCallbacks = [];

  /// 开始监听权限状态
  void startMonitoring() {
    if (_isMonitoring) return;
    
    debugPrint('开始权限状态监听');
    _isMonitoring = true;
    
    _monitorTimer = Timer.periodic(
      const Duration(seconds: _monitorInterval),
      (_) => _checkPermissionStates(),
    );
  }

  /// 停止监听权限状态
  void stopMonitoring() {
    if (!_isMonitoring) return;
    
    debugPrint('停止权限状态监听');
    _isMonitoring = false;
    
    _monitorTimer?.cancel();
    _monitorTimer = null;
  }

  /// 添加状态变化回调
  void addStateChangeCallback(Function(PermissionType, PermissionStatus, PermissionStatus) callback) {
    _stateChangeCallbacks.add(callback);
  }

  /// 移除状态变化回调
  void removeStateChangeCallback(Function(PermissionType, PermissionStatus, PermissionStatus) callback) {
    _stateChangeCallbacks.remove(callback);
  }

  /// 检查所有权限状态
  Future<void> _checkPermissionStates() async {
    try {
      debugPrint('检查权限状态变化');
      
      // 检查所有权限类型
      for (final type in PermissionType.values) {
        await _checkSinglePermissionState(type);
      }
    } catch (e) {
      debugPrint('检查权限状态时发生错误: $e');
    }
  }

  /// 检查单个权限状态
  Future<void> _checkSinglePermissionState(PermissionType type) async {
    try {
      // 获取缓存状态
      final cachedStatus = await PermissionCacheService.instance
          .getCachedPermissionStatus(type);
      
      // 获取实际状态
      final actualStatus = await PermissionManager.instance
          .checkPermission(type);
      
      // 如果状态发生变化
      if (cachedStatus != null && cachedStatus != actualStatus) {
        debugPrint('权限状态变化: ${type.name} ${cachedStatus.name} -> ${actualStatus.name}');
        
        // 更新缓存
        await PermissionCacheService.instance
            .cachePermissionStatus(type, actualStatus);
        
        // 处理状态变化
        await _handlePermissionStateChange(type, cachedStatus, actualStatus);
        
        // 通知回调
        _notifyStateChangeCallbacks(type, cachedStatus, actualStatus);
      }
    } catch (e) {
      debugPrint('检查权限状态时发生错误: ${type.name} - $e');
    }
  }

  /// 处理权限状态变化
  Future<void> _handlePermissionStateChange(
    PermissionType type,
    PermissionStatus oldStatus,
    PermissionStatus newStatus,
  ) async {
    // 如果权限被撤销
    if (oldStatus == PermissionStatus.granted && newStatus != PermissionStatus.granted) {
      await _handlePermissionRevoked(type);
    }
    
    // 如果权限被授予
    if (oldStatus != PermissionStatus.granted && newStatus == PermissionStatus.granted) {
      await _handlePermissionGranted(type);
    }
  }

  /// 处理权限被撤销
  Future<void> _handlePermissionRevoked(PermissionType type) async {
    debugPrint('权限被撤销: ${type.name}');
    
    // 重置权限状态，清除相关记录
    await PermissionCacheService.instance.resetPermissionState(type);
    
    // 特殊处理无障碍服务权限
    if (type == PermissionType.accessibility) {
      // 可以在这里添加特殊处理逻辑
      // 比如通知用户权限被撤销，或者自动降级锁定级别
      debugPrint('无障碍服务权限被撤销，已重置相关状态');
    }
  }

  /// 处理权限被授予
  Future<void> _handlePermissionGranted(PermissionType type) async {
    debugPrint('权限被授予: ${type.name}');
    
    // 特殊处理无障碍服务权限
    if (type == PermissionType.accessibility) {
      // 可以在这里添加特殊处理逻辑
      // 比如通知用户权限已可用
      debugPrint('无障碍服务权限已授予');
    }
  }

  /// 通知状态变化回调
  void _notifyStateChangeCallbacks(
    PermissionType type,
    PermissionStatus oldStatus,
    PermissionStatus newStatus,
  ) {
    for (final callback in _stateChangeCallbacks) {
      try {
        callback(type, oldStatus, newStatus);
      } catch (e) {
        debugPrint('权限状态变化回调执行失败: $e');
      }
    }
  }

  /// 手动触发权限状态检查
  Future<void> manualCheck() async {
    debugPrint('手动触发权限状态检查');
    await _checkPermissionStates();
  }

  /// 检查特定权限状态
  Future<void> checkSpecificPermission(PermissionType type) async {
    debugPrint('检查特定权限状态: ${type.name}');
    await _checkSinglePermissionState(type);
  }

  /// 是否正在监听
  bool get isMonitoring => _isMonitoring;

  /// 获取监听间隔
  int get monitorInterval => _monitorInterval;

  /// 销毁监听器
  void dispose() {
    stopMonitoring();
    _stateChangeCallbacks.clear();
    debugPrint('权限状态监听器已销毁');
  }
}

/// 权限状态变化事件
class PermissionStateChangeEvent {
  final PermissionType type;
  final PermissionStatus oldStatus;
  final PermissionStatus newStatus;
  final DateTime timestamp;

  PermissionStateChangeEvent({
    required this.type,
    required this.oldStatus,
    required this.newStatus,
    required this.timestamp,
  });

  @override
  String toString() {
    return 'PermissionStateChangeEvent('
        'type: ${type.name}, '
        'oldStatus: ${oldStatus.name}, '
        'newStatus: ${newStatus.name}, '
        'timestamp: $timestamp'
        ')';
  }
}

/// 权限状态监听器扩展
/// 提供更高级的监听功能
extension PermissionStateMonitorExtension on PermissionStateMonitor {
  /// 监听特定权限的状态变化
  void listenToPermission(
    PermissionType type,
    Function(PermissionStatus oldStatus, PermissionStatus newStatus) callback,
  ) {
    addStateChangeCallback((permissionType, oldStatus, newStatus) {
      if (permissionType == type) {
        callback(oldStatus, newStatus);
      }
    });
  }

  /// 监听权限被撤销事件
  void listenToPermissionRevoked(
    PermissionType type,
    Function() callback,
  ) {
    addStateChangeCallback((permissionType, oldStatus, newStatus) {
      if (permissionType == type && 
          oldStatus == PermissionStatus.granted && 
          newStatus != PermissionStatus.granted) {
        callback();
      }
    });
  }

  /// 监听权限被授予事件
  void listenToPermissionGranted(
    PermissionType type,
    Function() callback,
  ) {
    addStateChangeCallback((permissionType, oldStatus, newStatus) {
      if (permissionType == type && 
          oldStatus != PermissionStatus.granted && 
          newStatus == PermissionStatus.granted) {
        callback();
      }
    });
  }
}
