import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

/// 集成专注锁定服务
/// 提供系统锁屏集成的专注模式功能
///
/// 主要功能：
/// 1. 启用/禁用集成专注锁定模式
/// 2. 获取锁定状态
/// 3. 控制系统锁屏集成
/// 4. 与现有Kiosk模式协调工作
class IntegratedFocusLockService {
  static const MethodChannel _channel = MethodChannel('yoyo_lock_screen');

  /// 单例实例
  static final IntegratedFocusLockService _instance =
      IntegratedFocusLockService._internal();
  factory IntegratedFocusLockService() => _instance;
  IntegratedFocusLockService._internal();

  /// 当前锁定状态
  bool _isLockActive = false;
  String _currentLevel = 'none';

  /// 状态变化回调
  Function(IntegratedLockStatus)? _onStatusChanged;

  /// 设置状态变化监听器
  void setStatusChangeListener(Function(IntegratedLockStatus) listener) {
    _onStatusChanged = listener;
  }

  /// 启用集成专注锁定模式
  ///
  /// [level] 锁定级别：
  /// - 'basic': 基础锁定（系统锁屏 + 基础覆盖层）
  /// - 'enhanced': 增强锁定（+ 系统UI控制 + Kiosk模式）
  /// - 'ultimate': 终极锁定（+ 无障碍服务）
  Future<bool> enableIntegratedFocusLock({String level = 'ultimate'}) async {
    try {
      debugPrint('🔒 IntegratedFocusLockService: 启用集成专注锁定 - 级别: $level');

      final bool result =
          await _channel.invokeMethod('enableIntegratedFocusLock', {
        'level': level,
      });

      if (result) {
        _isLockActive = true;
        _currentLevel = level;
        debugPrint('✅ IntegratedFocusLockService: 集成专注锁定启用成功');

        // 获取详细状态并通知监听器
        final status = await getIntegratedLockStatus();
        _onStatusChanged?.call(status);
      } else {
        debugPrint('❌ IntegratedFocusLockService: 集成专注锁定启用失败');
      }

      return result;
    } catch (e) {
      debugPrint('❌ IntegratedFocusLockService: 启用集成专注锁定异常: $e');
      return false;
    }
  }

  /// 禁用集成专注锁定模式
  Future<bool> disableIntegratedFocusLock() async {
    try {
      debugPrint('🔒 IntegratedFocusLockService: 禁用集成专注锁定');

      final bool result =
          await _channel.invokeMethod('disableIntegratedFocusLock');

      if (result) {
        _isLockActive = false;
        _currentLevel = 'none';
        debugPrint('✅ IntegratedFocusLockService: 集成专注锁定禁用成功');

        // 获取详细状态并通知监听器
        final status = await getIntegratedLockStatus();
        _onStatusChanged?.call(status);
      } else {
        debugPrint('❌ IntegratedFocusLockService: 集成专注锁定禁用失败');
      }

      return result;
    } catch (e) {
      debugPrint('❌ IntegratedFocusLockService: 禁用集成专注锁定异常: $e');
      return false;
    }
  }

  /// 获取集成锁定状态
  Future<IntegratedLockStatus> getIntegratedLockStatus() async {
    try {
      final Map<dynamic, dynamic> result =
          await _channel.invokeMethod('getIntegratedLockStatus');

      final status =
          IntegratedLockStatus.fromMap(Map<String, dynamic>.from(result));

      // 更新本地状态
      _isLockActive = status.isActive;
      _currentLevel = status.level;

      return status;
    } catch (e) {
      debugPrint('❌ IntegratedFocusLockService: 获取集成锁定状态异常: $e');
      return IntegratedLockStatus(
        isActive: false,
        level: 'none',
        systemLockActive: false,
        overlayActive: false,
        kioskActive: false,
        accessibilityActive: false,
        error: e.toString(),
      );
    }
  }

  /// 启用专注模式系统锁屏
  /// 仅启用系统锁屏功能，不包含其他组件
  Future<bool> enableFocusSystemLock() async {
    try {
      debugPrint('🔒 IntegratedFocusLockService: 启用专注模式系统锁屏');

      final bool result = await _channel.invokeMethod('enableFocusSystemLock');

      if (result) {
        debugPrint('✅ IntegratedFocusLockService: 专注模式系统锁屏启用成功');
      } else {
        debugPrint('❌ IntegratedFocusLockService: 专注模式系统锁屏启用失败');
      }

      return result;
    } catch (e) {
      debugPrint('❌ IntegratedFocusLockService: 启用专注模式系统锁屏异常: $e');
      return false;
    }
  }

  /// 禁用专注模式系统锁屏
  Future<bool> disableFocusSystemLock() async {
    try {
      debugPrint('🔒 IntegratedFocusLockService: 禁用专注模式系统锁屏');

      final bool result = await _channel.invokeMethod('disableFocusSystemLock');

      if (result) {
        debugPrint('✅ IntegratedFocusLockService: 专注模式系统锁屏禁用成功');
      } else {
        debugPrint('❌ IntegratedFocusLockService: 专注模式系统锁屏禁用失败');
      }

      return result;
    } catch (e) {
      debugPrint('❌ IntegratedFocusLockService: 禁用专注模式系统锁屏异常: $e');
      return false;
    }
  }

  /// 检查是否处于锁定状态
  bool get isLockActive => _isLockActive;

  /// 获取当前锁定级别
  String get currentLevel => _currentLevel;

  /// 启动专注会话（完整流程）
  /// 包含准备、倒计时、锁定的完整用户体验
  Future<bool> startFocusSession({
    required int durationMinutes,
    required String taskType,
    String lockLevel = 'ultimate',
  }) async {
    try {
      debugPrint('🚀 IntegratedFocusLockService: 启动专注会话');
      debugPrint('   - 时长: $durationMinutes分钟');
      debugPrint('   - 任务类型: $taskType');
      debugPrint('   - 锁定级别: $lockLevel');

      // 1. 启用集成专注锁定
      final lockResult = await enableIntegratedFocusLock(level: lockLevel);

      if (lockResult) {
        debugPrint('✅ IntegratedFocusLockService: 专注会话启动成功');
        return true;
      } else {
        debugPrint('❌ IntegratedFocusLockService: 专注会话启动失败');
        return false;
      }
    } catch (e) {
      debugPrint('❌ IntegratedFocusLockService: 启动专注会话异常: $e');
      return false;
    }
  }

  /// 结束专注会话
  Future<bool> endFocusSession() async {
    try {
      debugPrint('🏁 IntegratedFocusLockService: 结束专注会话');

      final result = await disableIntegratedFocusLock();

      if (result) {
        debugPrint('✅ IntegratedFocusLockService: 专注会话结束成功');
      } else {
        debugPrint('❌ IntegratedFocusLockService: 专注会话结束失败');
      }

      return result;
    } catch (e) {
      debugPrint('❌ IntegratedFocusLockService: 结束专注会话异常: $e');
      return false;
    }
  }
}

/// 集成锁定状态数据类
class IntegratedLockStatus {
  final bool isActive;
  final String level;
  final bool systemLockActive;
  final bool overlayActive;
  final bool kioskActive;
  final bool accessibilityActive;
  final String? error;

  const IntegratedLockStatus({
    required this.isActive,
    required this.level,
    required this.systemLockActive,
    required this.overlayActive,
    required this.kioskActive,
    required this.accessibilityActive,
    this.error,
  });

  factory IntegratedLockStatus.fromMap(Map<String, dynamic> map) {
    return IntegratedLockStatus(
      isActive: map['isActive'] ?? false,
      level: map['level'] ?? 'none',
      systemLockActive: map['systemLockActive'] ?? false,
      overlayActive: map['overlayActive'] ?? false,
      kioskActive: map['kioskActive'] ?? false,
      accessibilityActive: map['accessibilityActive'] ?? false,
      error: map['error'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'isActive': isActive,
      'level': level,
      'systemLockActive': systemLockActive,
      'overlayActive': overlayActive,
      'kioskActive': kioskActive,
      'accessibilityActive': accessibilityActive,
      'error': error,
    };
  }

  @override
  String toString() {
    return 'IntegratedLockStatus(isActive: $isActive, level: $level, '
        'systemLock: $systemLockActive, overlay: $overlayActive, '
        'kiosk: $kioskActive, accessibility: $accessibilityActive)';
  }
}
