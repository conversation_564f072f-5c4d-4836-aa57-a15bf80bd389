import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../services/focus_manager.dart' as focus_service;
import '../utils/constants.dart';
import 'completion_screen.dart';

/// YoYo日常风格的专注界面
/// 简洁、专注、用户友好
class YoYoFocusScreen extends StatefulWidget {
  final String taskType;
  final int durationMinutes;

  const YoYoFocusScreen({
    super.key,
    required this.taskType,
    required this.durationMinutes,
  });

  @override
  State<YoYoFocusScreen> createState() => _YoYoFocusScreenState();
}

class _YoYoFocusScreenState extends State<YoYoFocusScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  Timer? _motivationTimer;
  int _currentMotivationIndex = 0;

  // 激励文案
  final List<String> _motivationTexts = [
    "保持专注，你正在变得更好",
    "每一分钟的专注都是成长",
    "专注是通往成功的桥梁",
    "你的努力正在积累力量",
    "专注让梦想变为现实",
    "坚持下去，胜利就在前方",
  ];

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _startMotivationRotation();
    _startFocusSession();
  }

  void _initAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 0.95, end: 1.05).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _pulseController.repeat(reverse: true);
  }

  void _startMotivationRotation() {
    _motivationTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      if (mounted) {
        setState(() {
          _currentMotivationIndex =
              (_currentMotivationIndex + 1) % _motivationTexts.length;
        });
      }
    });
  }

  void _startFocusSession() async {
    debugPrint('🔥 YoYoFocusScreen: 开始启动专注会话');
    debugPrint(
        '🔥 YoYoFocusScreen: 任务类型=${widget.taskType}, 时长=${widget.durationMinutes}分钟');

    // 步骤1：权限检查和引导
    debugPrint('🔥 YoYoFocusScreen: 开始权限检查');
    final permissionCheckResult = await _checkAndRequestPermissions();

    if (!permissionCheckResult) {
      debugPrint('❌ YoYoFocusScreen: 权限检查失败，无法启动专注模式');
      return;
    }

    final focusManager = focus_service.FocusManager.instance;
    debugPrint('🔥 YoYoFocusScreen: 获取FocusManager实例成功');

    final success = await focusManager.startFocus(
      taskType: widget.taskType,
      durationMinutes: widget.durationMinutes,
    );

    debugPrint('🔥 YoYoFocusScreen: 专注会话启动结果: $success');

    if (!success) {
      debugPrint('❌ YoYoFocusScreen: 专注会话启动失败');
      _showErrorAndExit('启动专注会话失败');
    } else {
      debugPrint('✅ YoYoFocusScreen: 专注会话启动成功，锁定模式已激活');
    }
  }

  /// 处理手势拦截
  void _handleGestureInterception(
      DragUpdateDetails details, BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final bottomThreshold = screenHeight * 0.8; // 屏幕高度80%以下

    // 检测底部区域的上划手势
    if (details.globalPosition.dy > bottomThreshold) {
      debugPrint('🚫 YoYoFocusScreen: Flutter层检测到底部手势');
      debugPrint(
          '🚫 YoYoFocusScreen: 位置: ${details.globalPosition.dy}, 阈值: $bottomThreshold');

      // 触发手势拦截反馈
      _triggerGestureInterceptionFeedback();
    }
  }

  /// 处理手势结束
  void _handleGestureEnd(DragEndDetails details, BuildContext context) {
    final velocity = details.velocity.pixelsPerSecond;

    // 检测向上的快速滑动
    if (velocity.dy < -500) {
      // 向上滑动速度超过500像素/秒
      debugPrint('🚫 YoYoFocusScreen: 检测到向上快速滑动手势');
      debugPrint('🚫 YoYoFocusScreen: 速度: ${velocity.dy}');

      // 触发手势拦截反馈
      _triggerGestureInterceptionFeedback();
    }
  }

  /// 触发手势拦截反馈
  void _triggerGestureInterceptionFeedback() {
    try {
      // 震动反馈
      HapticFeedback.heavyImpact();

      // 显示提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('🚫 专注模式中，手势已被拦截'),
          duration: Duration(seconds: 1),
          backgroundColor: Colors.orange,
        ),
      );

      debugPrint('✅ YoYoFocusScreen: 手势拦截反馈已触发');
    } catch (e) {
      debugPrint('❌ YoYoFocusScreen: 触发手势拦截反馈失败: $e');
    }
  }

  /// 检查和请求权限
  Future<bool> _checkAndRequestPermissions() async {
    try {
      debugPrint('🔥 YoYoFocusScreen: 开始权限状态检查');

      // 检查基础权限状态
      final hasStorage = await _checkPermission('storage');
      final hasNotification = await _checkPermission('notification');
      final hasAccessibility = await _checkPermission('accessibility');
      final hasDeviceAdmin = await _checkPermission('deviceAdmin');

      debugPrint(
          '🔥 YoYoFocusScreen: 权限状态 - 存储:$hasStorage, 通知:$hasNotification, 无障碍:$hasAccessibility, 设备管理:$hasDeviceAdmin');

      // 检查是否可以启动基础专注模式
      if (!hasStorage || !hasNotification) {
        debugPrint('❌ YoYoFocusScreen: 缺少基础权限，显示权限引导');
        return await _showBasicPermissionDialog();
      }

      // 检查增强权限
      if (!hasAccessibility || !hasDeviceAdmin) {
        debugPrint('⚠️ YoYoFocusScreen: 缺少增强权限，显示可选权限对话框');
        await _showOptionalPermissionDialog();
      }

      debugPrint('✅ YoYoFocusScreen: 权限检查完成，可以启动专注模式');
      return true;
    } catch (e) {
      debugPrint('❌ YoYoFocusScreen: 权限检查异常: $e');
      return false;
    }
  }

  /// 检查单个权限状态
  Future<bool> _checkPermission(String type) async {
    try {
      const platform = MethodChannel('yoyo_lock_screen');
      final result =
          await platform.invokeMethod('checkPermission', {'type': type});

      // 处理不同类型的返回值
      if (result is bool) {
        return result;
      } else if (result is String) {
        return result.toLowerCase() == 'true' || result == 'granted';
      } else {
        return false;
      }
    } catch (e) {
      debugPrint('❌ YoYoFocusScreen: 检查权限失败 ($type): $e');
      return false;
    }
  }

  /// 显示基础权限对话框
  Future<bool> _showBasicPermissionDialog() async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('需要基础权限'),
        content: const Text('专注模式需要以下基础权限才能正常工作：\n\n'
            '• 存储权限：保存专注记录\n'
            '• 通知权限：显示专注状态\n\n'
            '请授予这些权限以继续。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop(true);
              await _requestBasicPermissions();
            },
            child: const Text('授予权限'),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  /// 显示可选权限对话框
  Future<void> _showOptionalPermissionDialog() async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('增强防护权限'),
        content: const Text('为了提供更强的专注保护，建议授予以下权限：\n\n'
            '• 无障碍服务：阻止系统手势\n'
            '• 设备管理员：深度锁定模式\n\n'
            '这些权限是可选的，不授予也能使用基础专注功能。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('跳过'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _requestEnhancedPermissions();
            },
            child: const Text('授予权限'),
          ),
        ],
      ),
    );
  }

  /// 请求基础权限
  Future<void> _requestBasicPermissions() async {
    try {
      const platform = MethodChannel('yoyo_lock_screen');

      // 请求存储权限
      await platform.invokeMethod('requestPermission', {'type': 'storage'});

      // 请求通知权限
      await platform
          .invokeMethod('requestPermission', {'type': 'notification'});

      debugPrint('✅ YoYoFocusScreen: 基础权限请求完成');
    } catch (e) {
      debugPrint('❌ YoYoFocusScreen: 请求基础权限失败: $e');
    }
  }

  /// 请求增强权限
  Future<void> _requestEnhancedPermissions() async {
    try {
      const platform = MethodChannel('yoyo_lock_screen');

      // 请求无障碍权限
      await platform
          .invokeMethod('requestPermission', {'type': 'accessibility'});

      // 请求设备管理员权限
      await platform.invokeMethod('requestPermission', {'type': 'deviceAdmin'});

      debugPrint('✅ YoYoFocusScreen: 增强权限请求完成');
    } catch (e) {
      debugPrint('❌ YoYoFocusScreen: 请求增强权限失败: $e');
    }
  }

  void _showErrorAndExit(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('错误'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false, // 禁用返回手势
      child: GestureDetector(
        onPanUpdate: (details) => _handleGestureInterception(details, context),
        onPanEnd: (details) => _handleGestureEnd(details, context),
        child: Scaffold(
          backgroundColor: AppColors.background,
          body: Consumer<focus_service.FocusManager>(
            builder: (context, focusManager, child) {
              // 监听专注状态变化
              if (focusManager.state == focus_service.FocusState.completed) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  _navigateToCompletion(focusManager.currentSession!);
                });
              }

              return Container(
                decoration: const BoxDecoration(
                  gradient: AppColors.backgroundGradient,
                ),
                child: SafeArea(
                  child: Column(
                    children: [
                      // 顶部状态栏
                      _buildTopBar(focusManager),

                      // 主要内容区域
                      Expanded(
                        child: _buildMainContent(focusManager),
                      ),

                      // 底部信息
                      _buildBottomInfo(focusManager),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildTopBar(focus_service.FocusManager focusManager) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      child: Row(
        children: [
          // 紧急退出区域（隐藏）
          GestureDetector(
            onTap: () => focusManager.handleEmergencyTap(),
            child: Container(
              width: 50,
              height: 50,
              color: Colors.transparent,
              child: focusManager.emergencyExitProgress > 0
                  ? CircularProgressIndicator(
                      value: focusManager.emergencyExitProgress,
                      color: Colors.red.withOpacity(0.7),
                      strokeWidth: 2,
                    )
                  : null,
            ),
          ),

          const Spacer(),

          // 锁定级别指示
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 6,
            ),
            decoration: BoxDecoration(
              color: AppColors.surface.withOpacity(0.3),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _getLockIcon(focusManager.lockLevel),
                  size: 16,
                  color: AppColors.onSurface.withOpacity(0.7),
                ),
                const SizedBox(width: 4),
                Text(
                  focusManager.lockLevelDescription,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  IconData _getLockIcon(focus_service.LockLevel level) {
    switch (level) {
      case focus_service.LockLevel.basic:
        return Icons.lock_outline;
      case focus_service.LockLevel.enhanced:
        return Icons.lock;
      case focus_service.LockLevel.deep:
        return Icons.security;
    }
  }

  Widget _buildMainContent(focus_service.FocusManager focusManager) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 倒计时显示
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: Text(
                  focusManager.currentTimeDisplay,
                  style: AppTextStyles.timerLarge.copyWith(
                    fontSize: 72,
                    fontWeight: FontWeight.w300,
                    color: AppColors.primary,
                  ),
                ),
              );
            },
          ),

          const SizedBox(height: 40),

          // 进度条
          Container(
            width: 200,
            height: 8,
            decoration: BoxDecoration(
              color: AppColors.surface.withOpacity(0.3),
              borderRadius: BorderRadius.circular(4),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: focusManager.progress,
              child: Container(
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ),
          ),

          const SizedBox(height: 60),

          // 任务类型
          Text(
            widget.taskType,
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.onSurface,
            ),
          ),

          const SizedBox(height: 20),

          // 激励文案
          AnimatedSwitcher(
            duration: const Duration(milliseconds: 500),
            child: Text(
              _motivationTexts[_currentMotivationIndex],
              key: ValueKey(_currentMotivationIndex),
              style: AppTextStyles.bodyLarge.copyWith(
                color: AppColors.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomInfo(focus_service.FocusManager focusManager) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      child: Column(
        children: [
          // 专注状态
          Text(
            focusManager.stateDescription,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.onSurface.withOpacity(0.6),
            ),
          ),

          const SizedBox(height: 8),

          // 已专注时间
          Text(
            '已专注 ${focusManager.focusedTimeDisplay}',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.onSurface.withOpacity(0.5),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToCompletion(dynamic session) {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => CompletionScreen(session: session),
      ),
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _motivationTimer?.cancel();
    super.dispose();
  }
}
