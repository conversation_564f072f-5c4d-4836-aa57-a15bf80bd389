import 'package:flutter/material.dart';
import 'package:lockphone/services/permission_cache_service.dart';
import 'package:lockphone/services/permission_manager.dart';

/// 权限缓存功能演示程序
/// 展示如何避免重复授权提示，提升用户体验
class PermissionCacheDemo extends StatefulWidget {
  const PermissionCacheDemo({super.key});

  @override
  State<PermissionCacheDemo> createState() => _PermissionCacheDemoState();
}

class _PermissionCacheDemoState extends State<PermissionCacheDemo> {
  final _cacheService = PermissionCacheService.instance;
  final _permissionManager = PermissionManager.instance;
  
  String _statusText = '准备就绪';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    await _cacheService.init();
    setState(() {
      _statusText = '服务已初始化';
    });
  }

  /// 模拟应用启动时的权限检查
  Future<void> _simulateAppStartup() async {
    setState(() {
      _isLoading = true;
      _statusText = '模拟应用启动...';
    });

    try {
      // 使用智能权限检查
      final result = await _cacheService.smartPermissionCheck(PermissionType.accessibility);
      
      String message = '智能权限检查结果:\n';
      message += '当前状态: ${result.currentStatus.name}\n';
      message += '需要请求: ${result.needsRequest}\n';
      message += '可自动启用: ${result.canAutoEnable}\n';
      message += '显示说明: ${result.shouldShowRationale}\n';
      message += '用户已同意: ${result.hasUserConsent}\n';
      message += '已尝试自动启用: ${result.hasAutoEnableAttempted}\n';

      if (result.currentStatus == PermissionStatus.granted) {
        message += '\n✅ 权限已授予，可以直接开始专注！';
      } else if (result.canAutoEnable) {
        message += '\n🔄 用户已同意，尝试自动启用...';
      } else if (result.shouldShowRationale) {
        message += '\n💡 需要向用户说明权限用途';
      } else {
        message += '\n⚙️ 直接跳转到设置页面';
      }

      setState(() {
        _statusText = message;
      });
    } catch (e) {
      setState(() {
        _statusText = '检查失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 模拟用户同意权限
  Future<void> _simulateUserConsent() async {
    setState(() {
      _isLoading = true;
      _statusText = '记录用户同意...';
    });

    await _cacheService.recordUserConsent(PermissionType.accessibility);
    
    setState(() {
      _statusText = '✅ 用户同意已记录\n下次启动时将尝试自动启用';
      _isLoading = false;
    });
  }

  /// 模拟自动启用尝试
  Future<void> _simulateAutoEnableAttempt() async {
    setState(() {
      _isLoading = true;
      _statusText = '模拟自动启用尝试...';
    });

    await _cacheService.recordAutoEnableAttempt(PermissionType.accessibility);
    
    setState(() {
      _statusText = '🔄 自动启用尝试已记录\n下次将直接跳转设置页面';
      _isLoading = false;
    });
  }

  /// 模拟权限授予成功
  Future<void> _simulatePermissionGranted() async {
    setState(() {
      _isLoading = true;
      _statusText = '模拟权限授予...';
    });

    await _cacheService.cachePermissionStatus(
      PermissionType.accessibility,
      PermissionStatus.granted,
    );
    
    setState(() {
      _statusText = '🎉 权限已授予并缓存\n下次启动将直接可用';
      _isLoading = false;
    });
  }

  /// 重置所有状态
  Future<void> _resetAllStates() async {
    setState(() {
      _isLoading = true;
      _statusText = '重置所有状态...';
    });

    await _cacheService.resetPermissionState(PermissionType.accessibility);
    
    setState(() {
      _statusText = '🔄 所有状态已重置\n回到初始状态';
      _isLoading = false;
    });
  }

  /// 查看缓存详情
  Future<void> _viewCacheDetails() async {
    setState(() {
      _isLoading = true;
      _statusText = '查看缓存详情...';
    });

    final cachedStatus = await _cacheService.getCachedPermissionStatus(PermissionType.accessibility);
    final hasConsent = await _cacheService.hasUserConsent(PermissionType.accessibility);
    final hasAttempted = await _cacheService.hasAutoEnableAttempted(PermissionType.accessibility);

    String details = '缓存详情:\n';
    details += '权限状态: ${cachedStatus?.name ?? '无缓存'}\n';
    details += '用户同意: $hasConsent\n';
    details += '自动启用尝试: $hasAttempted\n';

    setState(() {
      _statusText = details;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('权限缓存演示'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '状态信息',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: double.infinity,
                      height: 200,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: SingleChildScrollView(
                        child: Text(
                          _statusText,
                          style: const TextStyle(
                            fontFamily: 'monospace',
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            if (_isLoading)
              const Center(
                child: CircularProgressIndicator(),
              )
            else
              Expanded(
                child: GridView.count(
                  crossAxisCount: 2,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                  childAspectRatio: 2.5,
                  children: [
                    _buildActionButton(
                      '模拟应用启动',
                      Icons.play_arrow,
                      Colors.green,
                      _simulateAppStartup,
                    ),
                    _buildActionButton(
                      '用户同意权限',
                      Icons.check_circle,
                      Colors.blue,
                      _simulateUserConsent,
                    ),
                    _buildActionButton(
                      '自动启用尝试',
                      Icons.autorenew,
                      Colors.orange,
                      _simulateAutoEnableAttempt,
                    ),
                    _buildActionButton(
                      '权限授予成功',
                      Icons.verified,
                      Colors.purple,
                      _simulatePermissionGranted,
                    ),
                    _buildActionButton(
                      '查看缓存详情',
                      Icons.info,
                      Colors.teal,
                      _viewCacheDetails,
                    ),
                    _buildActionButton(
                      '重置所有状态',
                      Icons.refresh,
                      Colors.red,
                      _resetAllStates,
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16),
      label: Text(
        label,
        style: const TextStyle(fontSize: 12),
        textAlign: TextAlign.center,
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      ),
    );
  }
}
