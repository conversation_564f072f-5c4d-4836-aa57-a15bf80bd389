# 自动权限授权测试指南

## 🎯 测试目标
验证修复后的自动权限授权流程，确保用户同意后不再弹出手工授权提示。

## 🔧 修复内容

### 1. 逻辑流程优化
- **修复时序问题** - 自动启用成功后直接返回，不再执行后续手动权限请求
- **增强状态检查** - 自动启用后进行多次重试检查，确保状态正确
- **简化权限验证** - 避免复杂的异步操作导致的状态不一致

### 2. 用户体验改进
- **清晰的日志输出** - 帮助调试和了解执行流程
- **友好的错误提示** - 自动启用失败时提供详细的原因说明
- **智能降级处理** - 自动启用失败时引导用户手动操作

## 📱 测试步骤

### 前置条件
1. **确保已授予系统权限**
   ```bash
   # Windows
   grant_permissions.bat
   
   # Linux/Mac
   ./grant_permissions.sh
   ```

2. **确保无障碍服务当前未启用**
   - 进入设置 → 无障碍 → 已下载的应用
   - 确认"专注锁屏服务"处于关闭状态

### 测试场景1：自动启用成功
1. **启动应用**
2. **选择深度锁定级别**
3. **点击"开始专注"**
4. **预期行为：**
   - 显示权限说明对话框
   - 点击"同意并启用"
   - 应用自动启用无障碍服务
   - **不应该再弹出手工授权提示**
   - 直接进入专注界面

### 测试场景2：自动启用失败
1. **移除系统权限**（模拟失败场景）
   ```bash
   adb shell pm revoke com.example.lockphone android.permission.WRITE_SECURE_SETTINGS
   ```
2. **重复测试场景1**
3. **预期行为：**
   - 显示权限说明对话框
   - 点击"同意并启用"
   - 自动启用失败
   - 显示手动启用引导对话框
   - 用户可以选择去设置手动启用

### 测试场景3：用户拒绝授权
1. **选择深度锁定级别**
2. **点击"开始专注"**
3. **在权限说明对话框中点击"取消"**
4. **预期行为：**
   - 权限检查失败
   - 不进入专注模式
   - 返回创建任务界面

## 🔍 关键日志检查

在测试过程中，注意查看以下日志输出：

### 成功流程日志
```
用户同意启用无障碍服务，开始自动启用
自动启用无障碍服务结果: true
自动启用后最终状态检查: PermissionStatus.granted
无障碍服务自动启用成功，跳过手动权限请求
```

### 失败流程日志
```
用户同意启用无障碍服务，开始自动启用
没有WRITE_SECURE_SETTINGS权限，无法自动启用
自动启用无障碍服务结果: false
无障碍服务自动启用失败，引导用户手动启用
```

### Android端详细日志
```
尝试自动启用无障碍服务
第1次检查无障碍服务状态: false
第2次检查无障碍服务状态: true
无障碍服务自动启用成功
```

## 🛠️ 调试命令

### 查看应用日志
```bash
# Flutter日志
flutter logs

# Android系统日志
adb logcat | grep -E "(YoYoLockScreen|LockScreenManager|MainActivity)"
```

### 检查权限状态
```bash
# 检查应用权限
adb shell dumpsys package com.example.lockphone | grep "android.permission"

# 检查无障碍服务状态
adb shell settings get secure enabled_accessibility_services
```

### 手动控制无障碍服务（用于测试）
```bash
# 启用无障碍服务
adb shell settings put secure enabled_accessibility_services com.example.lockphone/.YoYoAccessibilityService
adb shell settings put secure accessibility_enabled 1

# 禁用无障碍服务
adb shell settings put secure enabled_accessibility_services ""
adb shell settings put secure accessibility_enabled 0
```

## ✅ 验收标准

### 成功标准
- ✅ 用户同意后自动启用无障碍服务
- ✅ 自动启用成功后不再弹出手工授权提示
- ✅ 直接进入专注界面
- ✅ 专注结束后自动关闭无障碍服务

### 失败处理标准
- ✅ 自动启用失败时显示清晰的错误说明
- ✅ 提供手动启用的详细指导
- ✅ 用户可以选择取消或继续手动操作

## 🚨 常见问题

### Q: 自动启用成功但仍弹出手工授权
**A:** 检查权限检查逻辑的时序，确保自动启用后的状态检查正确

### Q: 自动启用总是失败
**A:** 确认已通过ADB授予WRITE_SECURE_SETTINGS权限

### Q: 权限对话框重复出现
**A:** 检查异步操作的mounted状态，避免context跨异步使用

### Q: 无障碍服务启用后无法检测到
**A:** 检查服务名称匹配逻辑，确保包名和类名正确

这个测试指南将帮助验证修复效果，确保用户获得流畅的自动权限授权体验！
