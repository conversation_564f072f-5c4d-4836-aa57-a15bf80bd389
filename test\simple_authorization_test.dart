import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:lockphone/services/enhanced_basic_lock.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('简化授权流程核心测试', () {
    setUp(() async {
      SharedPreferences.setMockInitialValues({});
      // 确保每个测试开始时锁定都是禁用状态
      await EnhancedBasicLock.instance.disableLock();
    });

    test('增强基础锁定 - 返回键拦截', () async {
      final basicLock = EnhancedBasicLock.instance;

      // 测试初始状态
      expect(basicLock.isLockActive, false);
      expect(basicLock.backPressCount, 0);

      // 启用锁定
      await basicLock.enableLock();
      expect(basicLock.isLockActive, true);

      // 测试返回键拦截 - 前9次应该被拦截
      for (int i = 1; i < 10; i++) {
        final shouldBlock = basicLock.handleBackPress();
        expect(shouldBlock, true, reason: '第$i次按键应该被拦截');
        expect(basicLock.backPressCount, i);
      }

      // 第10次应该允许退出
      final shouldBlock = basicLock.handleBackPress();
      expect(shouldBlock, false, reason: '第10次按键应该允许退出');

      // 清理
      await basicLock.disableLock();
    });

    test('增强基础锁定 - 时间窗口重置', () async {
      final basicLock = EnhancedBasicLock.instance;

      await basicLock.enableLock();

      // 按下5次返回键
      for (int i = 0; i < 5; i++) {
        basicLock.handleBackPress();
      }
      expect(basicLock.backPressCount, 5);

      // 等待超过时间窗口
      await Future.delayed(const Duration(seconds: 4));

      // 再次按下返回键，计数应该重置
      basicLock.handleBackPress();
      expect(basicLock.backPressCount, 1, reason: '超时后计数应该重置');

      // 清理
      await basicLock.disableLock();
    });

    test('增强基础锁定 - 状态管理', () async {
      final basicLock = EnhancedBasicLock.instance;

      // 初始状态
      expect(basicLock.isLockActive, false);

      // 启用锁定
      await basicLock.enableLock();
      expect(basicLock.isLockActive, true);

      // 禁用锁定
      await basicLock.disableLock();
      expect(basicLock.isLockActive, false);
      expect(basicLock.backPressCount, 0, reason: '禁用后计数应该重置');
    });

    test('增强基础锁定 - 重复启用安全性', () async {
      final basicLock = EnhancedBasicLock.instance;

      // 多次启用应该是安全的
      await basicLock.enableLock();
      expect(basicLock.isLockActive, true);

      await basicLock.enableLock(); // 重复启用
      expect(basicLock.isLockActive, true);

      // 清理
      await basicLock.disableLock();
    });

    test('增强基础锁定 - 阈值配置', () {
      final basicLock = EnhancedBasicLock.instance;

      // 验证阈值配置
      expect(basicLock.backPressThreshold, 10);
      expect(basicLock.backPressThreshold > 0, true);
    });
  });

  group('权限降级逻辑测试', () {
    test('权限级别优先级', () {
      // 这里测试权限级别的逻辑优先级
      // 深度锁定 > 增强锁定 > 基础锁定

      const levels = ['basic', 'enhanced', 'deep'];
      expect(levels.contains('basic'), true);
      expect(levels.contains('enhanced'), true);
      expect(levels.contains('deep'), true);
    });

    test('降级策略验证', () {
      // 验证降级策略的正确性
      // 当高级权限不可用时，应该降级到可用的最高级别

      // 模拟权限状态
      final permissions = {
        'storage': true, // 基础权限
        'overlay': false, // 悬浮窗权限不可用
        'accessibility': false, // 无障碍权限不可用
      };

      // 在这种情况下，应该降级到基础锁定
      expect(permissions['storage'], true);
      expect(permissions['overlay'], false);
      expect(permissions['accessibility'], false);

      // 基础锁定应该可用
      final canUseBasic = permissions['storage'] == true;
      expect(canUseBasic, true);
    });
  });

  group('用户体验测试', () {
    test('返回键提示逻辑', () async {
      final basicLock = EnhancedBasicLock.instance;

      await basicLock.enableLock();

      // 测试提示逻辑
      expect(basicLock.backPressCount, 0);

      basicLock.handleBackPress();
      expect(basicLock.backPressCount, 1);

      // 用户应该能看到进度提示
      final progress =
          '${basicLock.backPressCount}/${basicLock.backPressThreshold}';
      expect(progress, '1/10');

      await basicLock.disableLock();
    });

    test('锁定状态描述', () {
      // 测试不同锁定级别的描述
      final descriptions = {
        'basic': '基础锁定 - 阻止返回键和应用切换',
        'enhanced': '增强锁定 - 阻止手势操作和通知栏',
        'deep': '深度锁定 - 完全阻止系统操作',
      };

      expect(descriptions['basic']!.contains('基础锁定'), true);
      expect(descriptions['enhanced']!.contains('增强锁定'), true);
      expect(descriptions['deep']!.contains('深度锁定'), true);
    });
  });

  group('错误处理测试', () {
    test('异常状态处理', () async {
      final basicLock = EnhancedBasicLock.instance;

      // 在未启用状态下处理返回键应该是安全的
      final shouldBlock = basicLock.handleBackPress();
      expect(shouldBlock, false, reason: '未启用状态下不应该拦截返回键');

      // 重置计数应该是安全的
      basicLock.resetBackPressCount();
      expect(basicLock.backPressCount, 0);
    });

    test('状态一致性', () async {
      final basicLock = EnhancedBasicLock.instance;

      // 确保初始状态一致
      expect(basicLock.isLockActive, false);
      expect(basicLock.backPressCount, 0);

      // 启用后状态一致
      await basicLock.enableLock();
      expect(basicLock.isLockActive, true);

      // 禁用后状态一致
      await basicLock.disableLock();
      expect(basicLock.isLockActive, false);
      expect(basicLock.backPressCount, 0);
    });
  });
}
