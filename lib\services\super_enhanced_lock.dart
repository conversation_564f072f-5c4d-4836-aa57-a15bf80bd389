import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';

/// 超级增强锁定服务
/// 在没有无障碍权限的情况下，提供最强的锁定效果
/// 通过多重防护机制阻止用户逃逸
class SuperEnhancedLock {
  static SuperEnhancedLock? _instance;
  static SuperEnhancedLock get instance => _instance ??= SuperEnhancedLock._();

  SuperEnhancedLock._();

  bool _isLockActive = false;
  Timer? _systemUIMonitorTimer;
  Timer? _appStateMonitorTimer;
  Timer? _gestureBlockTimer;
  int _backPressCount = 0;
  DateTime? _lastBackPress;
  int _escapeAttempts = 0;

  // 配置参数
  static const int _backPressThreshold = 15; // 需要连续按返回键15次才能退出
  static const Duration _backPressWindow = Duration(seconds: 2); // 2秒内的按键才算连续
  static const Duration _systemUICheckInterval =
      Duration(milliseconds: 200); // 系统UI检查间隔
  static const Duration _appStateCheckInterval =
      Duration(milliseconds: 500); // 应用状态检查间隔
  static const Duration _gestureBlockInterval =
      Duration(milliseconds: 100); // 手势阻止间隔

  /// 启用超级增强锁定
  Future<bool> enableLock() async {
    if (_isLockActive) {
      debugPrint('超级增强锁定已经启用');
      return true;
    }

    try {
      debugPrint('启用超级增强锁定模式');

      // 1. 立即隐藏系统UI
      await _hideSystemUIAggressively();

      // 2. 启动系统UI持续监控
      _startSystemUIMonitoring();

      // 3. 启动应用状态监控
      _startAppStateMonitoring();

      // 4. 启动手势阻止机制
      _startGestureBlocking();

      // 5. 设置屏幕常亮和方向锁定
      await _setScreenSettings();

      // 6. 启用原生层锁定（通过MethodChannel）
      await _enableNativeLock();

      _isLockActive = true;
      _escapeAttempts = 0;
      debugPrint('超级增强锁定已启用');
      return true;
    } catch (e) {
      debugPrint('启用超级增强锁定失败: $e');
      return false;
    }
  }

  /// 禁用超级增强锁定
  Future<void> disableLock() async {
    if (!_isLockActive) return;

    debugPrint('禁用超级增强锁定');

    // 停止所有监控
    _systemUIMonitorTimer?.cancel();
    _appStateMonitorTimer?.cancel();
    _gestureBlockTimer?.cancel();

    // 禁用原生层锁定
    await _disableNativeLock();

    // 恢复系统UI
    await _showSystemUI();

    // 恢复屏幕设置
    await _restoreScreenSettings();

    _isLockActive = false;
    _backPressCount = 0;
    _lastBackPress = null;
    _escapeAttempts = 0;

    debugPrint('超级增强锁定已禁用');
  }

  /// 处理返回键按下
  bool handleBackPress() {
    if (!_isLockActive) {
      return false;
    }

    final now = DateTime.now();

    // 检查是否在时间窗口内
    if (_lastBackPress == null ||
        now.difference(_lastBackPress!) > _backPressWindow) {
      _backPressCount = 1;
    } else {
      _backPressCount++;
    }

    _lastBackPress = now;
    _escapeAttempts++;

    debugPrint(
        '返回键按下次数: $_backPressCount/$_backPressThreshold (总逃逸尝试: $_escapeAttempts)');

    // 如果达到阈值，允许退出
    if (_backPressCount >= _backPressThreshold) {
      debugPrint('达到返回键阈值，允许退出');
      return false; // 允许系统处理返回键
    }

    // 每次按返回键都重新强化锁定
    _reinforceLock();

    return true; // 拦截返回键
  }

  /// 强化锁定机制
  void _reinforceLock() {
    // 立即重新隐藏系统UI
    _hideSystemUIAggressively();

    // 重新设置屏幕标志
    _setScreenSettings();

    // 通知原生层加强锁定
    _reinforceNativeLock();
  }

  /// 激进地隐藏系统UI
  Future<void> _hideSystemUIAggressively() async {
    try {
      // 使用最强的沉浸式模式
      await SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.immersiveSticky,
        overlays: [],
      );

      // 设置系统UI样式
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          systemNavigationBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          systemNavigationBarIconBrightness: Brightness.light,
          systemNavigationBarDividerColor: Colors.transparent,
        ),
      );
    } catch (e) {
      debugPrint('隐藏系统UI失败: $e');
    }
  }

  /// 显示系统UI
  Future<void> _showSystemUI() async {
    try {
      await SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.edgeToEdge,
        overlays: SystemUiOverlay.values,
      );
    } catch (e) {
      debugPrint('显示系统UI失败: $e');
    }
  }

  /// 设置屏幕设置
  Future<void> _setScreenSettings() async {
    try {
      // 锁定竖屏方向
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
      ]);

      // 保持屏幕常亮（通过原生实现）
      await _setScreenAlwaysOn(true);
    } catch (e) {
      debugPrint('设置屏幕设置失败: $e');
    }
  }

  /// 恢复屏幕设置
  Future<void> _restoreScreenSettings() async {
    try {
      // 恢复屏幕方向
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);

      // 恢复屏幕亮度控制
      await _setScreenAlwaysOn(false);
    } catch (e) {
      debugPrint('恢复屏幕设置失败: $e');
    }
  }

  /// 启动系统UI监控
  void _startSystemUIMonitoring() {
    _systemUIMonitorTimer = Timer.periodic(_systemUICheckInterval, (timer) {
      if (!_isLockActive) {
        timer.cancel();
        return;
      }

      // 持续重新隐藏系统UI
      _hideSystemUIAggressively();
    });
  }

  /// 启动应用状态监控
  void _startAppStateMonitoring() {
    _appStateMonitorTimer = Timer.periodic(_appStateCheckInterval, (timer) {
      if (!_isLockActive) {
        timer.cancel();
        return;
      }

      // 检查应用是否仍在前台，如果不在则尝试恢复
      _checkAndRestoreAppState();
    });
  }

  /// 启动手势阻止机制
  void _startGestureBlocking() {
    _gestureBlockTimer = Timer.periodic(_gestureBlockInterval, (timer) {
      if (!_isLockActive) {
        timer.cancel();
        return;
      }

      // 定期强化手势阻止
      _blockSystemGestures();
    });
  }

  /// 检查并恢复应用状态
  void _checkAndRestoreAppState() {
    // 这里可以添加检查应用是否在前台的逻辑
    // 如果检测到应用不在前台，可以尝试恢复
    _reinforceLock();
  }

  /// 阻止系统手势
  void _blockSystemGestures() {
    // 通过原生层阻止手势
    _callNativeMethod('blockGestures', {});
  }

  /// 启用原生层锁定
  Future<void> _enableNativeLock() async {
    try {
      await _callNativeMethod('enableSuperEnhancedLock', {
        'backPressThreshold': _backPressThreshold,
      });
    } catch (e) {
      debugPrint('启用原生锁定失败: $e');
    }
  }

  /// 禁用原生层锁定
  Future<void> _disableNativeLock() async {
    try {
      await _callNativeMethod('disableSuperEnhancedLock', {});
    } catch (e) {
      debugPrint('禁用原生锁定失败: $e');
    }
  }

  /// 强化原生锁定
  void _reinforceNativeLock() {
    _callNativeMethod('reinforceLock', {
      'escapeAttempts': _escapeAttempts,
    });
  }

  /// 设置屏幕常亮
  Future<void> _setScreenAlwaysOn(bool alwaysOn) async {
    try {
      await _callNativeMethod('setScreenAlwaysOn', {'alwaysOn': alwaysOn});
    } catch (e) {
      debugPrint('设置屏幕常亮失败: $e');
    }
  }

  /// 调用原生方法
  Future<dynamic> _callNativeMethod(
      String method, Map<String, dynamic> arguments) async {
    try {
      const platform = MethodChannel('yoyo_lock_screen');
      return await platform.invokeMethod(method, arguments);
    } catch (e) {
      debugPrint('调用原生方法失败: $method, $e');
      return null;
    }
  }

  /// 获取当前锁定状态
  bool get isLockActive => _isLockActive;

  /// 获取返回键按下次数
  int get backPressCount => _backPressCount;

  /// 获取返回键阈值
  int get backPressThreshold => _backPressThreshold;

  /// 获取逃逸尝试次数
  int get escapeAttempts => _escapeAttempts;

  /// 重置返回键计数
  void resetBackPressCount() {
    _backPressCount = 0;
    _lastBackPress = null;
  }
}

/// 超级增强锁定界面Widget
class SuperEnhancedLockScreen extends StatefulWidget {
  final String taskName;
  final int remainingSeconds;
  final VoidCallback? onEmergencyExit;
  final VoidCallback? onTimeUp;

  const SuperEnhancedLockScreen({
    super.key,
    required this.taskName,
    required this.remainingSeconds,
    this.onEmergencyExit,
    this.onTimeUp,
  });

  @override
  State<SuperEnhancedLockScreen> createState() =>
      _SuperEnhancedLockScreenState();
}

class _SuperEnhancedLockScreenState extends State<SuperEnhancedLockScreen>
    with WidgetsBindingObserver {
  late Timer _uiUpdateTimer;
  String _backPressHint = '';
  String _escapeAttemptHint = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // 启动UI更新定时器
    _uiUpdateTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          // 更新返回键提示
          final lockService = SuperEnhancedLock.instance;
          if (lockService.backPressCount > 0) {
            _backPressHint =
                '连续按返回键 ${lockService.backPressCount}/${lockService.backPressThreshold} 次可退出专注';
          } else {
            _backPressHint = '';
          }

          // 更新逃逸尝试提示
          if (lockService.escapeAttempts > 0) {
            _escapeAttemptHint = '逃逸尝试: ${lockService.escapeAttempts} 次';
          } else {
            _escapeAttemptHint = '';
          }
        });
      }
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _uiUpdateTimer.cancel();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // 当应用状态改变时，重新应用锁定设置
    if (state == AppLifecycleState.resumed) {
      SuperEnhancedLock.instance._hideSystemUIAggressively();
      SuperEnhancedLock.instance._setScreenSettings();
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          // 处理返回键
          SuperEnhancedLock.instance.handleBackPress();
        }
      },
      child: Scaffold(
        backgroundColor: Colors.black,
        body: Container(
          width: double.infinity,
          height: double.infinity,
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color(0xFF0a0a0a),
                Color(0xFF1a1a2e),
                Color(0xFF16213e),
                Color(0xFF0f3460),
              ],
            ),
          ),
          child: SafeArea(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 超级增强锁定标识
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: Colors.red.withOpacity(0.5)),
                  ),
                  child: const Text(
                    '🔒 超级增强锁定',
                    style: TextStyle(
                      color: Colors.red,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),

                const SizedBox(height: 20),

                // 任务名称
                Text(
                  widget.taskName,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 40),

                // 倒计时显示
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.3),
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.blue.withOpacity(0.3),
                        blurRadius: 20,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: Text(
                    _formatTime(widget.remainingSeconds),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 48,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'monospace',
                    ),
                  ),
                ),

                const SizedBox(height: 40),

                // 专注提示
                const Text(
                  '超级专注进行中...',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 18,
                  ),
                ),

                const SizedBox(height: 20),

                // 返回键提示
                if (_backPressHint.isNotEmpty)
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 20),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.orange.withOpacity(0.5),
                      ),
                    ),
                    child: Text(
                      _backPressHint,
                      style: const TextStyle(
                        color: Colors.orange,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),

                const SizedBox(height: 10),

                // 逃逸尝试提示
                if (_escapeAttemptHint.isNotEmpty)
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 20),
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.red.withOpacity(0.5),
                      ),
                    ),
                    child: Text(
                      _escapeAttemptHint,
                      style: const TextStyle(
                        color: Colors.red,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),

                const Spacer(),

                // 底部提示
                Container(
                  margin: const EdgeInsets.all(20),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      const Icon(
                        Icons.security,
                        color: Colors.white60,
                        size: 20,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '超级增强锁定模式\n多重防护阻止逃逸\n连续按返回键 ${SuperEnhancedLock.instance.backPressThreshold} 次可退出',
                        style: const TextStyle(
                          color: Colors.white60,
                          fontSize: 12,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 格式化时间显示
  String _formatTime(int seconds) {
    final hours = seconds ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    final secs = seconds % 60;

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
    }
  }
}
