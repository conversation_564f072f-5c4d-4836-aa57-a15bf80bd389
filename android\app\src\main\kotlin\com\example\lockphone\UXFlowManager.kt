package com.example.lockphone

import android.app.Activity
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.AlphaAnimation
import android.view.animation.Animation
import android.view.animation.ScaleAnimation

/**
 * 用户体验流程管理器
 * 优化从启动专注到进入锁定状态的完整用户体验流程
 * 
 * 主要功能：
 * 1. 管理专注模式启动的完整流程
 * 2. 提供仪式感的倒计时体验
 * 3. 协调各个组件的无缝切换
 * 4. 处理用户体验的平滑过渡
 * 5. 提供专注会话的生命周期管理
 */
class UXFlowManager(private val activity: Activity) {
    
    companion object {
        private const val TAG = "UXFlowManager"
        private const val PREPARATION_DURATION = 2000L // 准备阶段2秒
        private const val COUNTDOWN_DURATION = 3000L // 倒计时3秒
        private const val TRANSITION_DURATION = 1000L // 过渡动画1秒
    }
    
    // 核心组件
    private val integratedFocusLockManager = IntegratedFocusLockManager(activity)
    private val smartLockScreenOverlay = SmartLockScreenOverlay(activity)
    
    // 流程状态
    private var currentFlowState = FlowState.IDLE
    private var currentSessionData: FocusSessionData? = null
    
    // 回调接口
    private var flowCallbacks: UXFlowCallbacks? = null
    
    /**
     * 启动优化的专注流程
     */
    fun startOptimizedFocusFlow(
        taskType: String,
        taskDescription: String,
        durationMinutes: Int,
        lockLevel: String = "ultimate",
        callbacks: UXFlowCallbacks? = null
    ): Boolean {
        return try {
            Log.d(TAG, "🚀 启动优化的专注流程")
            Log.d(TAG, "   - 任务类型: $taskType")
            Log.d(TAG, "   - 任务描述: $taskDescription")
            Log.d(TAG, "   - 时长: ${durationMinutes}分钟")
            Log.d(TAG, "   - 锁定级别: $lockLevel")
            
            if (currentFlowState != FlowState.IDLE) {
                Log.w(TAG, "⚠️ 专注流程已在进行中")
                return false
            }
            
            // 保存回调和会话数据
            flowCallbacks = callbacks
            currentSessionData = FocusSessionData(
                taskType = taskType,
                taskDescription = taskDescription,
                durationMinutes = durationMinutes,
                startTime = System.currentTimeMillis(),
                lockLevel = lockLevel,
                dailyFocusCount = getDailyFocusCount(),
                totalFocusMinutes = getTotalFocusMinutes()
            )
            
            // 开始流程
            startPreparationPhase()
            true
        } catch (e: Exception) {
            Log.e(TAG, "❌ 启动专注流程失败: ${e.message}")
            false
        }
    }
    
    /**
     * 停止专注流程
     */
    fun stopFocusFlow(isEmergencyExit: Boolean = false): Boolean {
        return try {
            Log.d(TAG, "🛑 停止专注流程 - 紧急退出: $isEmergencyExit")
            
            // 更新状态
            currentFlowState = FlowState.STOPPING
            
            // 隐藏覆盖层
            smartLockScreenOverlay.hideAndDestroyOverlay()
            
            // 禁用集成锁定
            val disableResult = integratedFocusLockManager.disableIntegratedFocusLock()
            
            // 通知回调
            if (isEmergencyExit) {
                flowCallbacks?.onEmergencyExit()
            } else {
                flowCallbacks?.onFocusComplete(currentSessionData)
            }
            
            // 重置状态
            currentFlowState = FlowState.IDLE
            currentSessionData = null
            flowCallbacks = null
            
            Log.d(TAG, "✅ 专注流程停止成功")
            disableResult
        } catch (e: Exception) {
            Log.e(TAG, "❌ 停止专注流程失败: ${e.message}")
            false
        }
    }
    
    /**
     * 第一阶段：准备阶段
     */
    private fun startPreparationPhase() {
        Log.d(TAG, "📋 开始准备阶段")
        currentFlowState = FlowState.PREPARING
        
        // 通知开始准备
        flowCallbacks?.onPreparationStart()
        
        // 显示准备界面（可以是Flutter界面或原生界面）
        showPreparationUI()
        
        // 延迟进入倒计时阶段
        Handler(Looper.getMainLooper()).postDelayed({
            startCountdownPhase()
        }, PREPARATION_DURATION)
    }
    
    /**
     * 第二阶段：倒计时阶段
     */
    private fun startCountdownPhase() {
        Log.d(TAG, "⏰ 开始倒计时阶段")
        currentFlowState = FlowState.COUNTDOWN
        
        // 通知开始倒计时
        flowCallbacks?.onCountdownStart()
        
        // 执行3-2-1倒计时
        executeCountdown { 
            startLockActivationPhase()
        }
    }
    
    /**
     * 第三阶段：锁定激活阶段
     */
    private fun startLockActivationPhase() {
        Log.d(TAG, "🔒 开始锁定激活阶段")
        currentFlowState = FlowState.ACTIVATING_LOCK
        
        // 通知开始激活锁定
        flowCallbacks?.onLockActivationStart()
        
        // 启用集成专注锁定（包含系统锁屏）
        val lockResult = integratedFocusLockManager.enableIntegratedFocusLock(
            currentSessionData?.lockLevel ?: "ultimate"
        )
        
        if (lockResult) {
            // 设置解锁后的拦截回调
            setupUnlockInterception()
            Log.d(TAG, "✅ 锁定激活成功，等待用户解锁")
            flowCallbacks?.onLockActivated()
        } else {
            Log.e(TAG, "❌ 锁定激活失败")
            flowCallbacks?.onFlowError("锁定激活失败")
            stopFocusFlow()
        }
    }
    
    /**
     * 第四阶段：专注界面显示阶段
     */
    private fun startFocusInterfacePhase() {
        Log.d(TAG, "🎯 开始专注界面显示阶段")
        currentFlowState = FlowState.FOCUS_ACTIVE
        
        // 显示智能锁屏覆盖层
        currentSessionData?.let { sessionData ->
            val overlayResult = smartLockScreenOverlay.createAndShowOverlay(
                sessionData = sessionData,
                onEmergencyExit = {
                    handleEmergencyExit()
                }
            )
            
            if (overlayResult) {
                Log.d(TAG, "✅ 专注界面显示成功")
                flowCallbacks?.onFocusInterfaceShown()
            } else {
                Log.e(TAG, "❌ 专注界面显示失败")
                flowCallbacks?.onFlowError("专注界面显示失败")
                stopFocusFlow()
            }
        }
    }
    
    /**
     * 显示准备界面
     */
    private fun showPreparationUI() {
        // 这里可以显示准备界面
        // 可以是Flutter界面或者简单的原生提示
        Log.d(TAG, "📱 显示准备界面")
        
        // 示例：可以通过回调让Flutter显示准备界面
        flowCallbacks?.onShowPreparationUI(currentSessionData)
    }
    
    /**
     * 执行倒计时
     */
    private fun executeCountdown(onComplete: () -> Unit) {
        Log.d(TAG, "⏰ 执行3-2-1倒计时")
        
        var countdownNumber = 3
        val countdownInterval = COUNTDOWN_DURATION / 3
        
        val countdownHandler = Handler(Looper.getMainLooper())
        
        fun showCountdownNumber() {
            if (countdownNumber > 0) {
                Log.d(TAG, "⏰ 倒计时: $countdownNumber")
                flowCallbacks?.onCountdownTick(countdownNumber)
                
                countdownNumber--
                countdownHandler.postDelayed({ showCountdownNumber() }, countdownInterval)
            } else {
                Log.d(TAG, "⏰ 倒计时完成")
                flowCallbacks?.onCountdownComplete()
                onComplete()
            }
        }
        
        showCountdownNumber()
    }
    
    /**
     * 设置解锁拦截
     */
    private fun setupUnlockInterception() {
        // 这个方法会在IntegratedFocusLockManager中的解锁监听器被触发时调用
        // 我们需要修改IntegratedFocusLockManager来支持这个回调
        Log.d(TAG, "🔓 设置解锁拦截机制")
        
        // 注意：实际的解锁拦截在FocusSystemLockManager中处理
        // 这里我们需要确保解锁后立即显示专注界面
    }
    
    /**
     * 处理用户解锁事件（由外部调用）
     */
    fun handleUserUnlock() {
        if (currentFlowState == FlowState.ACTIVATING_LOCK) {
            Log.d(TAG, "🔓 用户解锁，显示专注界面")
            
            // 添加平滑过渡效果
            Handler(Looper.getMainLooper()).postDelayed({
                startFocusInterfacePhase()
            }, TRANSITION_DURATION / 2) // 短暂延迟确保解锁动画完成
        }
    }
    
    /**
     * 处理紧急退出
     */
    private fun handleEmergencyExit() {
        Log.w(TAG, "🚨 处理紧急退出")
        stopFocusFlow(isEmergencyExit = true)
    }
    
    /**
     * 获取今日专注次数（示例实现）
     */
    private fun getDailyFocusCount(): Int {
        // 这里应该从数据库或SharedPreferences获取
        return 3 // 示例数据
    }
    
    /**
     * 获取总专注时长（示例实现）
     */
    private fun getTotalFocusMinutes(): Int {
        // 这里应该从数据库或SharedPreferences获取
        return 125 // 示例数据
    }
    
    /**
     * 获取当前流程状态
     */
    fun getCurrentFlowState(): FlowState = currentFlowState
    
    /**
     * 获取当前会话数据
     */
    fun getCurrentSessionData(): FocusSessionData? = currentSessionData
    
    /**
     * 检查是否处于专注状态
     */
    fun isInFocusMode(): Boolean = currentFlowState == FlowState.FOCUS_ACTIVE
    
    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            Log.d(TAG, "🧹 清理UXFlowManager资源")
            stopFocusFlow()
            integratedFocusLockManager.cleanup()
        } catch (e: Exception) {
            Log.e(TAG, "❌ 清理资源失败: ${e.message}")
        }
    }
}

/**
 * 流程状态枚举
 */
enum class FlowState {
    IDLE,               // 空闲状态
    PREPARING,          // 准备阶段
    COUNTDOWN,          // 倒计时阶段
    ACTIVATING_LOCK,    // 激活锁定阶段
    FOCUS_ACTIVE,       // 专注激活状态
    STOPPING            // 停止中
}

/**
 * UX流程回调接口
 */
interface UXFlowCallbacks {
    fun onPreparationStart()
    fun onShowPreparationUI(sessionData: FocusSessionData?)
    fun onCountdownStart()
    fun onCountdownTick(number: Int)
    fun onCountdownComplete()
    fun onLockActivationStart()
    fun onLockActivated()
    fun onFocusInterfaceShown()
    fun onFocusComplete(sessionData: FocusSessionData?)
    fun onEmergencyExit()
    fun onFlowError(error: String)
}
