package com.example.lockphone

import android.app.Activity
import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.os.Vibrator
import android.os.VibrationEffect
import android.provider.Settings
import android.util.Log
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.ProgressBar
import android.widget.TextView
import java.text.SimpleDateFormat
import java.util.*

/**
 * 智能锁屏覆盖层管理器
 * 在用户解锁后立即显示的自定义专注界面
 * 
 * 主要功能：
 * 1. 创建沉浸式专注界面
 * 2. 实时更新倒计时和进度
 * 3. 处理紧急退出机制
 * 4. 提供激励文案和统计信息
 * 5. 完全拦截用户交互
 */
class SmartLockScreenOverlay(private val activity: Activity) {
    
    companion object {
        private const val TAG = "SmartLockScreenOverlay"
        private const val EMERGENCY_EXIT_CLICK_COUNT = 10
        private const val EMERGENCY_EXIT_TIMEOUT = 3000L // 3秒内连续点击
        private const val UPDATE_INTERVAL = 1000L // 1秒更新间隔
    }
    
    // 窗口管理
    private val windowManager = activity.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    private var overlayView: View? = null
    private var isOverlayActive = false
    
    // UI组件
    private var timerText: TextView? = null
    private var progressBar: ProgressBar? = null
    private var progressCenterText: TextView? = null
    private var taskTypeText: TextView? = null
    private var taskDescriptionText: TextView? = null
    private var motivationText: TextView? = null
    private var subMotivationText: TextView? = null
    private var dailyCountNumber: TextView? = null
    private var totalTimeNumber: TextView? = null
    private var lockStatusText: TextView? = null
    private var connectionStatusText: TextView? = null
    private var emergencyExitHint: View? = null
    
    // 专注会话数据
    private var focusSession: FocusSessionData? = null
    private var updateHandler: Handler? = null
    private var updateRunnable: Runnable? = null
    
    // 紧急退出处理
    private var emergencyClickCount = 0
    private var lastEmergencyClickTime = 0L
    private var emergencyExitCallback: (() -> Unit)? = null
    
    // 激励文案库
    private val motivationTexts = listOf(
        "保持专注，你正在做一件了不起的事",
        "每一分钟的专注都在塑造更好的自己",
        "专注是通往成功的唯一道路",
        "深度工作创造深度价值",
        "专注的力量超乎你的想象",
        "在专注中找到内心的平静",
        "专注是最好的投资",
        "用专注点亮人生的每一刻"
    )
    
    private val subMotivationTexts = listOf(
        "远离干扰，拥抱专注",
        "专注当下，成就未来",
        "深度思考，创造价值",
        "保持节奏，持续前进",
        "专注是一种修行",
        "在专注中成长",
        "专注带来内心的力量",
        "每一次专注都是进步"
    )
    
    /**
     * 创建并显示智能锁屏覆盖层
     */
    fun createAndShowOverlay(sessionData: FocusSessionData, onEmergencyExit: (() -> Unit)? = null): Boolean {
        return try {
            Log.d(TAG, "🎨 创建智能锁屏覆盖层")
            
            if (isOverlayActive) {
                Log.w(TAG, "⚠️ 覆盖层已激活")
                return true
            }
            
            // 检查悬浮窗权限
            if (!hasOverlayPermission()) {
                Log.w(TAG, "❌ 缺少悬浮窗权限")
                return false
            }
            
            // 保存会话数据和回调
            focusSession = sessionData
            emergencyExitCallback = onEmergencyExit
            
            // 创建覆盖视图
            createOverlayView()
            
            // 设置窗口参数并添加到窗口管理器
            val params = createOverlayParams()
            windowManager.addView(overlayView, params)
            
            // 初始化UI组件
            initializeUIComponents()
            
            // 设置初始数据
            updateUIWithSessionData()
            
            // 启动定时更新
            startPeriodicUpdate()
            
            isOverlayActive = true
            Log.d(TAG, "✅ 智能锁屏覆盖层创建成功")
            true
        } catch (e: Exception) {
            Log.e(TAG, "❌ 创建智能锁屏覆盖层失败: ${e.message}")
            false
        }
    }
    
    /**
     * 隐藏并销毁覆盖层
     */
    fun hideAndDestroyOverlay(): Boolean {
        return try {
            Log.d(TAG, "🗑️ 隐藏并销毁智能锁屏覆盖层")
            
            if (!isOverlayActive) {
                Log.w(TAG, "⚠️ 覆盖层未激活")
                return true
            }
            
            // 停止定时更新
            stopPeriodicUpdate()
            
            // 移除覆盖视图
            overlayView?.let { view ->
                windowManager.removeView(view)
                overlayView = null
            }
            
            // 清理资源
            clearUIComponents()
            focusSession = null
            emergencyExitCallback = null
            
            isOverlayActive = false
            Log.d(TAG, "✅ 智能锁屏覆盖层销毁成功")
            true
        } catch (e: Exception) {
            Log.e(TAG, "❌ 销毁智能锁屏覆盖层失败: ${e.message}")
            false
        }
    }
    
    /**
     * 创建覆盖视图
     */
    private fun createOverlayView() {
        val layoutInflater = LayoutInflater.from(activity)
        overlayView = layoutInflater.inflate(R.layout.focus_lock_screen_overlay, null)
        
        // 设置全屏触摸拦截
        setupTouchInterception()
        
        // 设置紧急退出处理
        setupEmergencyExit()
    }
    
    /**
     * 创建覆盖层窗口参数 - 增强版手势拦截
     */
    private fun createOverlayParams(): WindowManager.LayoutParams {
        return WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                @Suppress("DEPRECATION")
                WindowManager.LayoutParams.TYPE_PHONE
            },
            // 修复：移除FLAG_NOT_FOCUSABLE以允许拦截系统手势
            WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
            WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH or
            WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
            WindowManager.LayoutParams.FLAG_FULLSCREEN or
            WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON or
            WindowManager.LayoutParams.FLAG_LAYOUT_INSET_DECOR or
            // 添加系统手势拦截标志
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
            PixelFormat.TRANSLUCENT
        ).apply {
            // 确保覆盖整个屏幕，包括系统手势区域
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
            }
        }
    }
    
    /**
     * 设置触摸拦截 - 增强版系统手势阻止
     */
    private fun setupTouchInterception() {
        overlayView?.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    val x = event.x
                    val y = event.y
                    Log.d(TAG, "🚫 拦截触摸事件: ($x, $y)")

                    // 检查是否为系统手势区域
                    if (isSystemGestureArea(x, y)) {
                        Log.w(TAG, "🚨 检测到系统手势区域触摸，强制拦截")
                        handleSystemGestureAttempt(x, y)
                        return@setOnTouchListener true
                    }

                    // 检查是否为紧急退出区域
                    if (!isEmergencyExitArea(x, y)) {
                        // 显示轻微的视觉反馈
                        showTouchBlockedFeedback()
                    }
                }
                MotionEvent.ACTION_MOVE -> {
                    // 检测滑动手势
                    if (isSwipeGesture(event)) {
                        Log.w(TAG, "🚨 检测到滑动手势，强制拦截")
                        return@setOnTouchListener true
                    }
                }
                MotionEvent.ACTION_UP -> {
                    // 重置手势检测状态
                    resetGestureDetection()
                }
            }
            true // 消费所有触摸事件
        }

        // 设置额外的触摸拦截保护
        setupAdditionalTouchProtection()
    }
    
    /**
     * 设置紧急退出处理
     */
    private fun setupEmergencyExit() {
        val emergencyTrigger = overlayView?.findViewById<View>(R.id.emergency_exit_trigger)
        emergencyTrigger?.setOnClickListener {
            handleEmergencyExitClick()
        }
    }
    
    /**
     * 初始化UI组件
     */
    private fun initializeUIComponents() {
        overlayView?.let { view ->
            timerText = view.findViewById(R.id.focus_timer)
            progressBar = view.findViewById(R.id.focus_progress)
            progressCenterText = view.findViewById(R.id.progress_center_text)
            taskTypeText = view.findViewById(R.id.task_type_text)
            taskDescriptionText = view.findViewById(R.id.task_description_text)
            motivationText = view.findViewById(R.id.motivation_text)
            subMotivationText = view.findViewById(R.id.sub_motivation_text)
            dailyCountNumber = view.findViewById(R.id.daily_count_number)
            totalTimeNumber = view.findViewById(R.id.total_time_number)
            lockStatusText = view.findViewById(R.id.lock_status_text)
            connectionStatusText = view.findViewById(R.id.connection_status_text)
            emergencyExitHint = view.findViewById(R.id.emergency_exit_hint)
        }
    }
    
    /**
     * 使用会话数据更新UI
     */
    private fun updateUIWithSessionData() {
        focusSession?.let { session ->
            // 更新任务信息
            taskTypeText?.text = "${session.taskType}进行中"
            taskDescriptionText?.text = session.taskDescription
            
            // 更新统计信息
            dailyCountNumber?.text = session.dailyFocusCount.toString()
            totalTimeNumber?.text = session.totalFocusMinutes.toString()
            
            // 更新锁定状态
            lockStatusText?.text = "🔒 ${session.lockLevel}锁定模式"
            connectionStatusText?.text = "● 系统连接正常"
            
            // 设置随机激励文案
            setRandomMotivationTexts()
            
            // 更新倒计时和进度
            updateTimerAndProgress()
        }
    }
    
    /**
     * 更新倒计时和进度
     */
    private fun updateTimerAndProgress() {
        focusSession?.let { session ->
            val currentTime = System.currentTimeMillis()
            val elapsedTime = currentTime - session.startTime
            val remainingTime = (session.durationMinutes * 60 * 1000) - elapsedTime
            
            if (remainingTime > 0) {
                // 更新倒计时显示
                val minutes = (remainingTime / 1000 / 60).toInt()
                val seconds = ((remainingTime / 1000) % 60).toInt()
                timerText?.text = String.format("%02d:%02d", minutes, seconds)
                
                // 更新进度条
                val progress = ((elapsedTime.toFloat() / (session.durationMinutes * 60 * 1000)) * 100).toInt()
                progressBar?.progress = progress
                progressCenterText?.text = "${progress}%"
            } else {
                // 专注时间结束
                timerText?.text = "00:00"
                progressBar?.progress = 100
                progressCenterText?.text = "100%"
                
                // 可以在这里触发专注完成的处理
                handleFocusSessionComplete()
            }
        }
    }
    
    /**
     * 设置随机激励文案
     */
    private fun setRandomMotivationTexts() {
        val randomMotivation = motivationTexts.random()
        val randomSubMotivation = subMotivationTexts.random()
        
        motivationText?.text = randomMotivation
        subMotivationText?.text = randomSubMotivation
    }
    
    /**
     * 启动定期更新
     */
    private fun startPeriodicUpdate() {
        updateHandler = Handler(Looper.getMainLooper())
        updateRunnable = object : Runnable {
            override fun run() {
                updateTimerAndProgress()
                updateHandler?.postDelayed(this, UPDATE_INTERVAL)
            }
        }
        updateHandler?.post(updateRunnable!!)
    }
    
    /**
     * 停止定期更新
     */
    private fun stopPeriodicUpdate() {
        updateRunnable?.let { runnable ->
            updateHandler?.removeCallbacks(runnable)
        }
        updateHandler = null
        updateRunnable = null
    }
    
    /**
     * 处理紧急退出点击
     */
    private fun handleEmergencyExitClick() {
        val currentTime = System.currentTimeMillis()
        
        // 检查点击时间间隔
        if (currentTime - lastEmergencyClickTime > EMERGENCY_EXIT_TIMEOUT) {
            emergencyClickCount = 0
        }
        
        emergencyClickCount++
        lastEmergencyClickTime = currentTime
        
        Log.d(TAG, "🚨 紧急退出点击: $emergencyClickCount/$EMERGENCY_EXIT_CLICK_COUNT")
        
        // 显示提示
        if (emergencyClickCount >= 3) {
            emergencyExitHint?.visibility = View.VISIBLE
        }
        
        // 检查是否达到退出条件
        if (emergencyClickCount >= EMERGENCY_EXIT_CLICK_COUNT) {
            Log.w(TAG, "🚨 触发紧急退出")
            emergencyExitCallback?.invoke()
        }
    }
    
    /**
     * 检查是否有悬浮窗权限
     */
    private fun hasOverlayPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(activity)
        } else {
            true
        }
    }
    
    /**
     * 检查是否为紧急退出区域
     */
    private fun isEmergencyExitArea(x: Float, y: Float): Boolean {
        // 左上角60dp区域
        val density = activity.resources.displayMetrics.density
        val emergencyAreaSize = 60 * density
        return x < emergencyAreaSize && y < emergencyAreaSize
    }
    
    /**
     * 显示触摸被阻止的反馈
     */
    private fun showTouchBlockedFeedback() {
        // 可以添加轻微的震动或视觉反馈
        // 这里暂时只记录日志
        Log.d(TAG, "🚫 触摸被阻止，显示反馈")
    }
    
    /**
     * 处理专注会话完成
     */
    private fun handleFocusSessionComplete() {
        Log.d(TAG, "🎉 专注会话完成")
        // 这里可以添加完成动画或通知
        // 实际的完成处理应该由外部管理器处理
    }
    
    /**
     * 清理UI组件引用
     */
    private fun clearUIComponents() {
        timerText = null
        progressBar = null
        progressCenterText = null
        taskTypeText = null
        taskDescriptionText = null
        motivationText = null
        subMotivationText = null
        dailyCountNumber = null
        totalTimeNumber = null
        lockStatusText = null
        connectionStatusText = null
        emergencyExitHint = null
    }
    
    /**
     * 检查是否为系统手势区域
     */
    private fun isSystemGestureArea(x: Float, y: Float): Boolean {
        val screenWidth = activity.resources.displayMetrics.widthPixels
        val screenHeight = activity.resources.displayMetrics.heightPixels
        val density = activity.resources.displayMetrics.density

        // 底部手势区域（导航栏区域）
        val bottomGestureHeight = 100 * density // 底部100dp
        if (y > screenHeight - bottomGestureHeight) {
            Log.d(TAG, "🚨 检测到底部手势区域: y=$y, threshold=${screenHeight - bottomGestureHeight}")
            return true
        }

        // 侧边手势区域
        val sideGestureWidth = 50 * density // 侧边50dp
        if (x < sideGestureWidth || x > screenWidth - sideGestureWidth) {
            Log.d(TAG, "🚨 检测到侧边手势区域: x=$x")
            return true
        }

        // 顶部状态栏区域
        val topGestureHeight = 100 * density // 顶部100dp
        if (y < topGestureHeight) {
            Log.d(TAG, "🚨 检测到顶部手势区域: y=$y")
            return true
        }

        return false
    }

    /**
     * 处理系统手势尝试
     */
    private fun handleSystemGestureAttempt(x: Float, y: Float) {
        Log.w(TAG, "🚨 系统手势尝试被阻止: ($x, $y)")

        // 触发无障碍服务的手势阻止
        YoYoAccessibilityService.getInstance()?.blockSystemGestures()

        // 显示阻止反馈
        showSystemGestureBlockedFeedback()

        // 可以添加震动反馈
        try {
            val vibrator = activity.getSystemService(Context.VIBRATOR_SERVICE) as? Vibrator
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                vibrator?.vibrate(VibrationEffect.createOneShot(50, VibrationEffect.DEFAULT_AMPLITUDE))
            } else {
                @Suppress("DEPRECATION")
                vibrator?.vibrate(50)
            }
        } catch (e: Exception) {
            Log.w(TAG, "震动反馈失败: ${e.message}")
        }
    }

    /**
     * 检测滑动手势
     */
    private fun isSwipeGesture(event: MotionEvent): Boolean {
        // 简单的滑动检测逻辑
        val deltaX = Math.abs(event.x - (event.historySize.takeIf { it > 0 }?.let { event.getHistoricalX(0) } ?: event.x))
        val deltaY = Math.abs(event.y - (event.historySize.takeIf { it > 0 }?.let { event.getHistoricalY(0) } ?: event.y))
        val threshold = 50 * activity.resources.displayMetrics.density

        return deltaX > threshold || deltaY > threshold
    }

    /**
     * 重置手势检测状态
     */
    private fun resetGestureDetection() {
        // 重置手势检测相关的状态
        Log.d(TAG, "🔄 重置手势检测状态")
    }

    /**
     * 设置额外的触摸保护
     */
    private fun setupAdditionalTouchProtection() {
        // 设置额外的触摸保护机制
        overlayView?.setOnSystemUiVisibilityChangeListener { visibility ->
            Log.w(TAG, "🚨 系统UI可见性变化: $visibility")
            // 如果系统UI变为可见，立即隐藏
            if (visibility and View.SYSTEM_UI_FLAG_FULLSCREEN == 0) {
                hideSystemUI()
            }
        }
    }

    /**
     * 隐藏系统UI
     */
    private fun hideSystemUI() {
        overlayView?.systemUiVisibility = (
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
            View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
            View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
            View.SYSTEM_UI_FLAG_FULLSCREEN or
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
        )
    }

    /**
     * 显示系统手势被阻止的反馈
     */
    private fun showSystemGestureBlockedFeedback() {
        Log.d(TAG, "🚫 显示系统手势阻止反馈")
        // 可以在这里添加视觉反馈，比如闪烁效果
    }

    /**
     * 检查覆盖层是否激活
     */
    fun isOverlayActive(): Boolean = isOverlayActive
    
    /**
     * 更新专注会话数据
     */
    fun updateSessionData(sessionData: FocusSessionData) {
        focusSession = sessionData
        updateUIWithSessionData()
    }
}

/**
 * 专注会话数据类
 */
data class FocusSessionData(
    val taskType: String,
    val taskDescription: String,
    val durationMinutes: Int,
    val startTime: Long,
    val lockLevel: String,
    val dailyFocusCount: Int,
    val totalFocusMinutes: Int
)
