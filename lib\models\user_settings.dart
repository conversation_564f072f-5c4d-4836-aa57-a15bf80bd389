class UserSettings {
  final int defaultDuration;
  final int breakDuration;
  final int longBreakDuration;
  final int sessionsUntilLongBreak;
  final bool soundEnabled;
  final bool vibrationEnabled;
  final String themeMode;

  UserSettings({
    required this.defaultDuration,
    required this.breakDuration,
    required this.longBreakDuration,
    required this.sessionsUntilLongBreak,
    required this.soundEnabled,
    required this.vibrationEnabled,
    required this.themeMode,
  });

  Map<String, dynamic> toMap() {
    return {
      'defaultDuration': defaultDuration,
      'breakDuration': breakDuration,
      'longBreakDuration': longBreakDuration,
      'sessionsUntilLongBreak': sessionsUntilLongBreak,
      'soundEnabled': soundEnabled,
      'vibrationEnabled': vibrationEnabled,
      'themeMode': themeMode,
    };
  }

  factory UserSettings.fromMap(Map<String, dynamic> map) {
    return UserSettings(
      defaultDuration: map['defaultDuration'] ?? 25,
      breakDuration: map['breakDuration'] ?? 5,
      longBreakDuration: map['longBreakDuration'] ?? 15,
      sessionsUntilLongBreak: map['sessionsUntilLongBreak'] ?? 4,
      soundEnabled: map['soundEnabled'] ?? true,
      vibrationEnabled: map['vibrationEnabled'] ?? true,
      themeMode: map['themeMode'] ?? 'system',
    );
  }

  UserSettings copyWith({
    int? defaultDuration,
    int? breakDuration,
    int? longBreakDuration,
    int? sessionsUntilLongBreak,
    bool? soundEnabled,
    bool? vibrationEnabled,
    String? themeMode,
  }) {
    return UserSettings(
      defaultDuration: defaultDuration ?? this.defaultDuration,
      breakDuration: breakDuration ?? this.breakDuration,
      longBreakDuration: longBreakDuration ?? this.longBreakDuration,
      sessionsUntilLongBreak:
          sessionsUntilLongBreak ?? this.sessionsUntilLongBreak,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      themeMode: themeMode ?? this.themeMode,
    );
  }
}

class AppSettings {
  static const String defaultFocusDuration = 'default_focus_duration';
  static const String hasCompletedOnboarding = 'has_completed_onboarding';
  static const String totalFocusSessions = 'total_focus_sessions';
  static const String totalFocusMinutes = 'total_focus_minutes';
  static const String currentStreak = 'current_streak';
  static const String lastFocusDate = 'last_focus_date';
  static const String hasDeviceAdminPermission = 'has_device_admin_permission';
  static const String hasUsageStatsPermission = 'has_usage_stats_permission';

  // Default values
  static const int defaultFocusDurationValue = 25; // minutes
}
