import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/ux_flow_service.dart';

/// 专注准备界面
/// 显示专注启动的准备阶段和倒计时
class FocusPreparationScreen extends StatefulWidget {
  final FocusSessionData sessionData;
  final VoidCallback? onPreparationComplete;

  const FocusPreparationScreen({
    super.key,
    required this.sessionData,
    this.onPreparationComplete,
  });

  @override
  State<FocusPreparationScreen> createState() => _FocusPreparationScreenState();
}

class _FocusPreparationScreenState extends State<FocusPreparationScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _fadeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _fadeAnimation;

  PreparationPhase _currentPhase = PreparationPhase.preparing;
  int _countdownNumber = 3;

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeIn,
    ));

    // 启动动画
    _pulseController.repeat(reverse: true);
    _fadeController.forward();

    // 开始准备流程
    _startPreparationFlow();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  /// 开始准备流程
  void _startPreparationFlow() {
    // 准备阶段持续2秒
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        _startCountdown();
      }
    });
  }

  /// 开始倒计时
  void _startCountdown() {
    setState(() {
      _currentPhase = PreparationPhase.countdown;
    });

    _executeCountdown();
  }

  /// 执行倒计时
  void _executeCountdown() {
    if (_countdownNumber > 0) {
      // 震动反馈
      HapticFeedback.mediumImpact();

      // 更新倒计时数字
      setState(() {});

      // 1秒后继续下一个数字
      Future.delayed(const Duration(seconds: 1), () {
        if (mounted) {
          _countdownNumber--;
          if (_countdownNumber > 0) {
            _executeCountdown();
          } else {
            _completePreparation();
          }
        }
      });
    }
  }

  /// 完成准备
  void _completePreparation() {
    setState(() {
      _currentPhase = PreparationPhase.complete;
    });

    // 强烈震动反馈
    HapticFeedback.heavyImpact();

    // 延迟一下然后回调
    Future.delayed(const Duration(milliseconds: 500), () {
      widget.onPreparationComplete?.call();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF1A1A2E),
              Color(0xFF16213E),
              Color(0xFF0F3460),
            ],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: _buildPhaseContent(),
          ),
        ),
      ),
    );
  }

  /// 构建阶段内容
  Widget _buildPhaseContent() {
    switch (_currentPhase) {
      case PreparationPhase.preparing:
        return _buildPreparingContent();
      case PreparationPhase.countdown:
        return _buildCountdownContent();
      case PreparationPhase.complete:
        return _buildCompleteContent();
    }
  }

  /// 构建准备阶段内容
  Widget _buildPreparingContent() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 专注图标
        ScaleTransition(
          scale: _pulseAnimation,
          child: Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: const Color(0xFF00BCD4).withOpacity(0.2),
              border: Border.all(
                color: const Color(0xFF00BCD4),
                width: 2,
              ),
            ),
            child: const Icon(
              Icons.self_improvement,
              size: 60,
              color: Color(0xFF00BCD4),
            ),
          ),
        ),

        const SizedBox(height: 40),

        // 准备文案
        const Text(
          '准备进入专注模式',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),

        const SizedBox(height: 16),

        // 任务信息
        Text(
          widget.sessionData.taskType,
          style: const TextStyle(
            fontSize: 18,
            color: Color(0xFF00BCD4),
            fontWeight: FontWeight.w500,
          ),
        ),

        const SizedBox(height: 8),

        if (widget.sessionData.taskDescription.isNotEmpty)
          Text(
            widget.sessionData.taskDescription,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.white70,
            ),
            textAlign: TextAlign.center,
          ),

        const SizedBox(height: 32),

        // 时长信息
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: Colors.white.withOpacity(0.1),
          ),
          child: Text(
            '专注时长：${widget.sessionData.durationMinutes} 分钟',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),

        const SizedBox(height: 40),

        // 准备提示
        const Text(
          '正在为您准备最佳的专注环境...',
          style: TextStyle(
            fontSize: 14,
            color: Colors.white60,
          ),
        ),

        const SizedBox(height: 20),

        // 加载指示器
        const SizedBox(
          width: 30,
          height: 30,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF00BCD4)),
          ),
        ),
      ],
    );
  }

  /// 构建倒计时内容
  Widget _buildCountdownContent() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 倒计时数字
        TweenAnimationBuilder<double>(
          duration: const Duration(milliseconds: 300),
          tween: Tween<double>(begin: 0.5, end: 1.0),
          builder: (context, scale, child) {
            return Transform.scale(
              scale: scale,
              child: Container(
                width: 200,
                height: 200,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: const Color(0xFF00BCD4).withOpacity(0.1),
                  border: Border.all(
                    color: const Color(0xFF00BCD4),
                    width: 3,
                  ),
                ),
                child: Center(
                  child: Text(
                    '$_countdownNumber',
                    style: const TextStyle(
                      fontSize: 80,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF00BCD4),
                      fontFamily: 'monospace',
                    ),
                  ),
                ),
              ),
            );
          },
        ),

        const SizedBox(height: 40),

        // 倒计时提示
        const Text(
          '专注模式即将开始',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),

        const SizedBox(height: 16),

        const Text(
          '请做好心理准备，保持专注',
          style: TextStyle(
            fontSize: 14,
            color: Colors.white70,
          ),
        ),
      ],
    );
  }

  /// 构建完成内容
  Widget _buildCompleteContent() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 完成图标
        TweenAnimationBuilder<double>(
          duration: const Duration(milliseconds: 500),
          tween: Tween<double>(begin: 0.0, end: 1.0),
          builder: (context, scale, child) {
            return Transform.scale(
              scale: scale,
              child: Container(
                width: 120,
                height: 120,
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  color: Color(0xFF4CAF50),
                ),
                child: const Icon(
                  Icons.check,
                  size: 60,
                  color: Colors.white,
                ),
              ),
            );
          },
        ),

        const SizedBox(height: 32),

        // 完成文案
        const Text(
          '准备完成',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),

        const SizedBox(height: 16),

        const Text(
          '正在激活专注锁定模式...',
          style: TextStyle(
            fontSize: 16,
            color: Colors.white70,
          ),
        ),
      ],
    );
  }
}

/// 准备阶段枚举
enum PreparationPhase {
  preparing, // 准备中
  countdown, // 倒计时
  complete, // 完成
}
