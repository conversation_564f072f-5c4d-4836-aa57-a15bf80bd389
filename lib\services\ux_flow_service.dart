import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

/// UX流程服务
/// 管理专注模式的完整用户体验流程
///
/// 主要功能：
/// 1. 启动优化的专注流程（准备→倒计时→锁定→专注）
/// 2. 管理流程状态和回调
/// 3. 提供流畅的用户体验过渡
/// 4. 处理紧急退出和错误情况
class UXFlowService {
  static const MethodChannel _channel = MethodChannel('yoyo_lock_screen');

  /// 单例实例
  static final UXFlowService _instance = UXFlowService._internal();
  factory UXFlowService() => _instance;
  UXFlowService._internal();

  /// 当前流程状态
  FocusFlowState _currentState = FocusFlowState.idle;
  FocusSessionData? _currentSession;

  /// 流程回调
  UXFlowCallbacks? _callbacks;

  /// 设置流程回调监听器
  void setFlowCallbacks(UXFlowCallbacks callbacks) {
    _callbacks = callbacks;
  }

  /// 启动优化的专注流程
  ///
  /// [taskType] 任务类型（如：深度工作、学习充电、冥想修行等）
  /// [taskDescription] 任务描述
  /// [durationMinutes] 专注时长（分钟）
  /// [lockLevel] 锁定级别（basic/enhanced/ultimate）
  Future<bool> startOptimizedFocusFlow({
    required String taskType,
    String taskDescription = '',
    required int durationMinutes,
    String lockLevel = 'ultimate',
  }) async {
    try {
      debugPrint('🚀 UXFlowService: 启动优化的专注流程');
      debugPrint('   - 任务类型: $taskType');
      debugPrint('   - 任务描述: $taskDescription');
      debugPrint('   - 时长: $durationMinutes分钟');
      debugPrint('   - 锁定级别: $lockLevel');

      if (_currentState != FocusFlowState.idle) {
        debugPrint('⚠️ UXFlowService: 专注流程已在进行中');
        return false;
      }

      // 创建会话数据
      _currentSession = FocusSessionData(
        taskType: taskType,
        taskDescription: taskDescription,
        durationMinutes: durationMinutes,
        startTime: DateTime.now(),
        lockLevel: lockLevel,
      );

      // 调用原生方法启动流程
      final bool result =
          await _channel.invokeMethod('startOptimizedFocusFlow', {
        'taskType': taskType,
        'taskDescription': taskDescription,
        'durationMinutes': durationMinutes,
        'lockLevel': lockLevel,
      });

      if (result) {
        _currentState = FocusFlowState.preparing;
        debugPrint('✅ UXFlowService: 优化的专注流程启动成功');
        _callbacks?.onFlowStarted(_currentSession!);
      } else {
        debugPrint('❌ UXFlowService: 优化的专注流程启动失败');
        _currentSession = null;
      }

      return result;
    } catch (e) {
      debugPrint('❌ UXFlowService: 启动优化的专注流程异常: $e');
      _currentSession = null;
      return false;
    }
  }

  /// 停止专注流程
  ///
  /// [isEmergencyExit] 是否为紧急退出
  Future<bool> stopFocusFlow({bool isEmergencyExit = false}) async {
    try {
      debugPrint('🛑 UXFlowService: 停止专注流程 - 紧急退出: $isEmergencyExit');

      final bool result = await _channel.invokeMethod('stopFocusFlow', {
        'isEmergencyExit': isEmergencyExit,
      });

      if (result) {
        final session = _currentSession;
        _currentState = FocusFlowState.idle;
        _currentSession = null;

        debugPrint('✅ UXFlowService: 专注流程停止成功');

        if (isEmergencyExit) {
          _callbacks?.onEmergencyExit(session);
        } else {
          _callbacks?.onFlowCompleted(session);
        }
      } else {
        debugPrint('❌ UXFlowService: 专注流程停止失败');
      }

      return result;
    } catch (e) {
      debugPrint('❌ UXFlowService: 停止专注流程异常: $e');
      return false;
    }
  }

  /// 获取专注流程状态
  Future<FocusFlowStatus> getFocusFlowStatus() async {
    try {
      final Map<dynamic, dynamic> result =
          await _channel.invokeMethod('getFocusFlowStatus');

      final status = FocusFlowStatus.fromMap(Map<String, dynamic>.from(result));

      // 更新本地状态
      _currentState = _parseFlowState(status.flowState);

      return status;
    } catch (e) {
      debugPrint('❌ UXFlowService: 获取专注流程状态异常: $e');
      return FocusFlowStatus(
        flowState: 'ERROR',
        isInFocusMode: false,
        sessionData: null,
        error: e.toString(),
      );
    }
  }

  /// 处理准备阶段UI显示
  /// 在原生端准备阶段时调用，用于显示Flutter准备界面
  Future<void> showPreparationUI() async {
    debugPrint('📱 UXFlowService: 显示准备界面');
    _currentState = FocusFlowState.preparing;
    _callbacks?.onPreparationUIRequested(_currentSession);
  }

  /// 处理倒计时更新
  /// 在原生端倒计时时调用，用于更新Flutter倒计时显示
  Future<void> updateCountdown(int number) async {
    debugPrint('⏰ UXFlowService: 倒计时更新 - $number');
    _currentState = FocusFlowState.countdown;
    _callbacks?.onCountdownTick(number);
  }

  /// 处理锁定激活
  /// 在原生端激活锁定时调用
  Future<void> onLockActivated() async {
    debugPrint('🔒 UXFlowService: 锁定已激活');
    _currentState = FocusFlowState.lockActivated;
    _callbacks?.onLockActivated();
  }

  /// 处理专注界面显示
  /// 在原生端显示专注界面时调用
  Future<void> onFocusInterfaceShown() async {
    debugPrint('🎯 UXFlowService: 专注界面已显示');
    _currentState = FocusFlowState.focusActive;
    _callbacks?.onFocusInterfaceShown();
  }

  /// 处理流程错误
  /// 在原生端发生错误时调用
  Future<void> onFlowError(String error) async {
    debugPrint('❌ UXFlowService: 流程错误 - $error');
    _callbacks?.onFlowError(error);
  }

  /// 获取当前流程状态
  FocusFlowState get currentState => _currentState;

  /// 获取当前会话数据
  FocusSessionData? get currentSession => _currentSession;

  /// 检查是否处于专注状态
  bool get isInFocusMode => _currentState == FocusFlowState.focusActive;

  /// 解析流程状态字符串
  FocusFlowState _parseFlowState(String stateString) {
    switch (stateString.toUpperCase()) {
      case 'IDLE':
        return FocusFlowState.idle;
      case 'PREPARING':
        return FocusFlowState.preparing;
      case 'COUNTDOWN':
        return FocusFlowState.countdown;
      case 'ACTIVATING_LOCK':
        return FocusFlowState.activatingLock;
      case 'FOCUS_ACTIVE':
        return FocusFlowState.focusActive;
      case 'STOPPING':
        return FocusFlowState.stopping;
      default:
        return FocusFlowState.idle;
    }
  }
}

/// 专注流程状态枚举
enum FocusFlowState {
  idle, // 空闲状态
  preparing, // 准备阶段
  countdown, // 倒计时阶段
  activatingLock, // 激活锁定阶段
  lockActivated, // 锁定已激活
  focusActive, // 专注激活状态
  stopping, // 停止中
}

/// 专注会话数据类
class FocusSessionData {
  final String taskType;
  final String taskDescription;
  final int durationMinutes;
  final DateTime startTime;
  final String lockLevel;

  const FocusSessionData({
    required this.taskType,
    required this.taskDescription,
    required this.durationMinutes,
    required this.startTime,
    required this.lockLevel,
  });

  Map<String, dynamic> toMap() {
    return {
      'taskType': taskType,
      'taskDescription': taskDescription,
      'durationMinutes': durationMinutes,
      'startTime': startTime.millisecondsSinceEpoch,
      'lockLevel': lockLevel,
    };
  }

  factory FocusSessionData.fromMap(Map<String, dynamic> map) {
    return FocusSessionData(
      taskType: map['taskType'] ?? '',
      taskDescription: map['taskDescription'] ?? '',
      durationMinutes: map['durationMinutes'] ?? 0,
      startTime: DateTime.fromMillisecondsSinceEpoch(map['startTime'] ?? 0),
      lockLevel: map['lockLevel'] ?? 'basic',
    );
  }
}

/// 专注流程状态类
class FocusFlowStatus {
  final String flowState;
  final bool isInFocusMode;
  final FocusSessionData? sessionData;
  final String? error;

  const FocusFlowStatus({
    required this.flowState,
    required this.isInFocusMode,
    this.sessionData,
    this.error,
  });

  factory FocusFlowStatus.fromMap(Map<String, dynamic> map) {
    return FocusFlowStatus(
      flowState: map['flowState'] ?? 'IDLE',
      isInFocusMode: map['isInFocusMode'] ?? false,
      sessionData: map['sessionData'] != null
          ? FocusSessionData.fromMap(
              Map<String, dynamic>.from(map['sessionData']))
          : null,
      error: map['error'],
    );
  }
}

/// UX流程回调接口
abstract class UXFlowCallbacks {
  /// 流程启动
  void onFlowStarted(FocusSessionData sessionData);

  /// 请求显示准备界面
  void onPreparationUIRequested(FocusSessionData? sessionData);

  /// 倒计时更新
  void onCountdownTick(int number);

  /// 锁定已激活
  void onLockActivated();

  /// 专注界面已显示
  void onFocusInterfaceShown();

  /// 流程完成
  void onFlowCompleted(FocusSessionData? sessionData);

  /// 紧急退出
  void onEmergencyExit(FocusSessionData? sessionData);

  /// 流程错误
  void onFlowError(String error);
}
