import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'permission_manager.dart';

/// 无障碍服务回退机制服务
class AccessibilityFallbackService {
  static const _channel = MethodChannel('yoyo_lock_screen');

  /// 单例模式
  static final AccessibilityFallbackService _instance =
      AccessibilityFallbackService._internal();
  factory AccessibilityFallbackService() => _instance;
  AccessibilityFallbackService._internal();

  static AccessibilityFallbackService get instance => _instance;

  /// 尝试所有可能的启用方法
  Future<AccessibilityEnableResult> tryAllEnableMethods() async {
    final result = AccessibilityEnableResult();

    try {
      // 1. 首先检查当前状态
      final currentStatus = await PermissionManager.instance
          .checkPermission(PermissionType.accessibility);
      if (currentStatus == PermissionStatus.granted) {
        result.success = true;
        result.method = 'already_enabled';
        result.message = '无障碍服务已启用';
        return result;
      }

      // 2. 尝试增强版自动启用
      final guidanceResult = await PermissionManager.instance
          .requestAccessibilityServiceWithGuidance();
      result.guidanceResult = guidanceResult;

      if (guidanceResult.success) {
        result.success = true;
        result.method =
            guidanceResult.autoEnableSuccess ? 'auto_enable' : 'manual_setup';
        result.message = '无障碍服务启用成功';
        return result;
      }

      // 3. 尝试传统自动启用方法
      final autoEnableSuccess =
          await PermissionManager.instance.enableAccessibilityServiceAuto();
      if (autoEnableSuccess) {
        result.success = true;
        result.method = 'legacy_auto_enable';
        result.message = '通过传统方法自动启用成功';
        return result;
      }

      // 4. 检查系统权限状态，提供具体的解决方案
      final systemPermissions =
          await PermissionManager.instance.checkSystemPermissions();
      result.systemPermissions = systemPermissions;

      // 5. 生成回退方案
      result.fallbackOptions =
          await _generateFallbackOptions(systemPermissions, guidanceResult);

      result.success = false;
      result.method = 'requires_fallback';
      result.message = '自动启用失败，需要使用回退方案';
    } catch (e) {
      result.success = false;
      result.method = 'error';
      result.message = '启用过程中发生错误: $e';
      result.error = e.toString();
    }

    return result;
  }

  /// 生成回退选项
  Future<List<FallbackOption>> _generateFallbackOptions(
    Map<String, dynamic> systemPermissions,
    AccessibilityServiceGuidanceResult guidanceResult,
  ) async {
    final options = <FallbackOption>[];

    final hasWriteSecure =
        systemPermissions['hasWriteSecureSettings'] as bool? ?? false;
    final isRootAvailable =
        systemPermissions['isRootAvailable'] as bool? ?? false;

    // 选项1: ADB命令授权（如果没有WRITE_SECURE_SETTINGS权限）
    if (!hasWriteSecure) {
      options.add(const FallbackOption(
        type: FallbackType.adbCommands,
        title: 'ADB命令授权',
        description: '通过电脑使用ADB命令授予应用系统权限，然后重试自动启用',
        difficulty: FallbackDifficulty.medium,
        requiresComputer: true,
        steps: [
          '在电脑上安装ADB工具',
          '启用手机的USB调试模式',
          '连接手机到电脑',
          '运行权限授予脚本',
          '重新尝试自动启用'
        ],
        estimatedTime: '5-10分钟',
      ));
    }

    // 选项2: Root权限授权（如果设备支持Root）
    if (!isRootAvailable) {
      options.add(const FallbackOption(
        type: FallbackType.rootAccess,
        title: 'Root权限授权',
        description: 'Root设备后获得系统级权限，实现完全自动化',
        difficulty: FallbackDifficulty.hard,
        requiresComputer: false,
        steps: [
          '备份重要数据',
          '解锁Bootloader',
          '刷入Root包',
          '安装Root管理器',
          '授予应用Root权限',
          '重新尝试自动启用'
        ],
        estimatedTime: '30-60分钟',
        warning: 'Root操作有风险，可能影响设备保修和安全性',
      ));
    }

    // 选项3: 手动设置（总是可用）
    options.add(FallbackOption(
      type: FallbackType.manualSetup,
      title: '手动设置',
      description: '按照详细指导手动启用无障碍服务',
      difficulty: FallbackDifficulty.easy,
      requiresComputer: false,
      steps: guidanceResult.basicSteps.isNotEmpty
          ? guidanceResult.basicSteps
          : ['打开系统设置', '进入无障碍设置', '找到"专注锁屏服务"', '开启服务开关', '确认授权'],
      estimatedTime: '2-3分钟',
    ));

    // 选项4: 厂商特定方法（如果有厂商特定指导）
    if (guidanceResult.vendorSpecificGuidance != null) {
      final vendorGuidance = guidanceResult.vendorSpecificGuidance!;
      final vendor = vendorGuidance['vendor'] as String;
      final additionalSteps =
          vendorGuidance['additionalSteps'] as List<dynamic>;

      options.add(FallbackOption(
        type: FallbackType.vendorSpecific,
        title: '$vendor设备优化',
        description: '针对$vendor设备的特殊优化设置',
        difficulty: FallbackDifficulty.medium,
        requiresComputer: false,
        steps: additionalSteps.cast<String>(),
        estimatedTime: '5-10分钟',
      ));
    }

    // 选项5: 降级使用（基于五层防护体系）
    final hasStorage = await PermissionManager.instance
        .checkPermission(PermissionType.storage);
    if (hasStorage == PermissionStatus.granted) {
      options.add(const FallbackOption(
        type: FallbackType.degradedMode,
        title: '降级模式',
        description: '使用悬浮窗权限提供基础锁屏功能（功能有限）',
        difficulty: FallbackDifficulty.easy,
        requiresComputer: false,
        steps: ['使用现有的悬浮窗权限', '启用基础锁屏模式', '注意：无法阻止系统手势'],
        estimatedTime: '立即可用',
        warning: '功能受限，无法完全阻止退出',
      ));
    }

    return options;
  }

  /// 执行特定的回退选项
  Future<bool> executeFallbackOption(FallbackType type) async {
    switch (type) {
      case FallbackType.adbCommands:
        return await _executeAdbCommands();
      case FallbackType.rootAccess:
        return await _executeRootAccess();
      case FallbackType.manualSetup:
        return await _executeManualSetup();
      case FallbackType.vendorSpecific:
        return await _executeVendorSpecific();
      case FallbackType.degradedMode:
        return await _executeDegradedMode();
    }
  }

  Future<bool> _executeAdbCommands() async {
    try {
      // 生成ADB命令并显示给用户
      // final commands = await _channel.invokeMethod('generateAdbCommands'); // 暂时未使用
      // 这里应该显示命令给用户，让用户在电脑上执行
      return false; // 需要用户手动执行
    } catch (e) {
      return false;
    }
  }

  Future<bool> _executeRootAccess() async {
    try {
      return await PermissionManager.instance.grantPermissionsViaRoot();
    } catch (e) {
      return false;
    }
  }

  Future<bool> _executeManualSetup() async {
    try {
      await PermissionManager.instance
          .openPermissionSettings(PermissionType.accessibility);
      return false; // 需要用户手动设置
    } catch (e) {
      return false;
    }
  }

  Future<bool> _executeVendorSpecific() async {
    // 厂商特定的处理逻辑
    return false;
  }

  Future<bool> _executeDegradedMode() async {
    // 启用降级模式
    return true;
  }
}

/// 无障碍服务启用结果
class AccessibilityEnableResult {
  bool success = false;
  String method = '';
  String message = '';
  String? error;
  AccessibilityServiceGuidanceResult? guidanceResult;
  Map<String, dynamic>? systemPermissions;
  List<FallbackOption> fallbackOptions = [];
}

/// 回退选项类型
enum FallbackType {
  adbCommands,
  rootAccess,
  manualSetup,
  vendorSpecific,
  degradedMode,
}

/// 回退选项难度
enum FallbackDifficulty {
  easy,
  medium,
  hard,
}

/// 回退选项
class FallbackOption {
  final FallbackType type;
  final String title;
  final String description;
  final FallbackDifficulty difficulty;
  final bool requiresComputer;
  final List<String> steps;
  final String estimatedTime;
  final String? warning;

  const FallbackOption({
    required this.type,
    required this.title,
    required this.description,
    required this.difficulty,
    required this.requiresComputer,
    required this.steps,
    required this.estimatedTime,
    this.warning,
  });

  IconData get difficultyIcon {
    switch (difficulty) {
      case FallbackDifficulty.easy:
        return Icons.sentiment_satisfied;
      case FallbackDifficulty.medium:
        return Icons.sentiment_neutral;
      case FallbackDifficulty.hard:
        return Icons.sentiment_dissatisfied;
    }
  }

  Color get difficultyColor {
    switch (difficulty) {
      case FallbackDifficulty.easy:
        return Colors.green;
      case FallbackDifficulty.medium:
        return Colors.orange;
      case FallbackDifficulty.hard:
        return Colors.red;
    }
  }

  String get difficultyText {
    switch (difficulty) {
      case FallbackDifficulty.easy:
        return '简单';
      case FallbackDifficulty.medium:
        return '中等';
      case FallbackDifficulty.hard:
        return '困难';
    }
  }
}
