import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../widgets/enhanced_accessibility_permission_dialog.dart';
import 'permission_cache_service.dart';

/// 权限类型枚举
enum PermissionType {
  accessibility, // 无障碍服务
  deviceAdmin, // 设备管理员权限
  notification, // 通知权限
  storage, // 存储权限
}

/// 权限状态枚举
enum PermissionStatus {
  granted, // 已授权
  denied, // 已拒绝
  unknown, // 未知状态
}

/// 权限信息类
class PermissionInfo {
  final PermissionType type;
  final String title;
  final String description;
  final String icon;
  final bool isRequired;
  final String guideText;

  const PermissionInfo({
    required this.type,
    required this.title,
    required this.description,
    required this.icon,
    required this.isRequired,
    required this.guideText,
  });
}

/// 无障碍服务引导结果类
class AccessibilityServiceGuidanceResult {
  final bool success;
  final bool isCurrentlyEnabled;
  final bool autoEnableAttempted;
  final bool autoEnableSuccess;
  final bool requiresManualSetup;
  final bool settingsIntentSuccess;
  final Map<String, dynamic>? deviceInfo;
  final Map<String, dynamic>? systemPermissions;
  final Map<String, dynamic>? guidanceInfo;
  final String? error;

  const AccessibilityServiceGuidanceResult({
    required this.success,
    required this.isCurrentlyEnabled,
    required this.autoEnableAttempted,
    required this.autoEnableSuccess,
    required this.requiresManualSetup,
    this.settingsIntentSuccess = false,
    this.deviceInfo,
    this.systemPermissions,
    this.guidanceInfo,
    this.error,
  });

  factory AccessibilityServiceGuidanceResult.fromMap(Map<String, dynamic> map) {
    return AccessibilityServiceGuidanceResult(
      success: map['success'] as bool? ?? false,
      isCurrentlyEnabled: map['isCurrentlyEnabled'] as bool? ?? false,
      autoEnableAttempted: map['autoEnableAttempted'] as bool? ?? false,
      autoEnableSuccess: map['autoEnableSuccess'] as bool? ?? false,
      requiresManualSetup: map['requiresManualSetup'] as bool? ?? false,
      settingsIntentSuccess: map['settingsIntentSuccess'] as bool? ?? false,
      deviceInfo: _convertToStringMap(map['deviceInfo']),
      systemPermissions: _convertToStringMap(map['systemPermissions']),
      guidanceInfo: _convertToStringMap(map['guidanceInfo']),
      error: map['error'] as String?,
    );
  }

  /// 辅助方法：安全地转换Map类型
  static Map<String, dynamic>? _convertToStringMap(dynamic value) {
    if (value == null) return null;
    if (value is Map<String, dynamic>) return value;
    if (value is Map) {
      return Map<String, dynamic>.from(value);
    }
    return null;
  }

  /// 获取厂商特定的引导信息
  Map<String, dynamic>? get vendorSpecificGuidance {
    return guidanceInfo?['vendorSpecific'] as Map<String, dynamic>?;
  }

  /// 获取基础设置步骤
  List<String> get basicSteps {
    final steps = guidanceInfo?['basicSteps'] as List?;
    return steps?.cast<String>() ?? [];
  }

  /// 获取权限状态信息
  Map<String, dynamic> get permissionStatus {
    return guidanceInfo?['permissionStatus'] as Map<String, dynamic>? ?? {};
  }

  /// 获取替代方案
  List<String> get alternatives {
    final alts = guidanceInfo?['alternatives'] as List?;
    return alts?.cast<String>() ?? [];
  }
}

/// YoYo日常风格的权限管理器
/// 提供渐进式、用户友好的权限申请体验
class PermissionManager {
  static const MethodChannel _channel = MethodChannel('yoyo_lock_screen');

  static PermissionManager? _instance;
  static PermissionManager get instance => _instance ??= PermissionManager._();

  PermissionManager._();

  /// 权限配置信息
  static const Map<PermissionType, PermissionInfo> _permissionInfos = {
    PermissionType.accessibility: PermissionInfo(
      type: PermissionType.accessibility,
      title: '无障碍服务',
      description: '用于系统级锁定功能，防止通过系统手势退出专注',
      icon: '🛡️',
      isRequired: false,
      guideText: '在无障碍设置中找到"专注锁屏服务"并开启',
    ),
    PermissionType.deviceAdmin: PermissionInfo(
      type: PermissionType.deviceAdmin,
      title: '设备管理员权限',
      description: '用于终极Kiosk模式，提供最强的锁定防护',
      icon: '🔐',
      isRequired: false,
      guideText: '在设备管理员设置中启用"专注锁屏管理器"',
    ),
    PermissionType.notification: PermissionInfo(
      type: PermissionType.notification,
      title: '通知权限',
      description: '用于发送专注完成提醒和重要通知',
      icon: '🔔',
      isRequired: false,
      guideText: '允许应用发送通知',
    ),
    PermissionType.storage: PermissionInfo(
      type: PermissionType.storage,
      title: '存储权限',
      description: '用于保存专注记录和应用设置',
      icon: '💾',
      isRequired: true,
      guideText: '允许应用访问设备存储',
    ),
  };

  /// 获取权限信息
  PermissionInfo getPermissionInfo(PermissionType type) {
    return _permissionInfos[type]!;
  }

  /// 获取所有权限信息
  List<PermissionInfo> getAllPermissionInfos() {
    return _permissionInfos.values.toList();
  }

  /// 检查单个权限状态（优先使用缓存）
  Future<PermissionStatus> checkPermission(PermissionType type) async {
    return await PermissionCacheService.instance.getPermissionStatus(type);
  }

  /// 直接检查权限状态（不使用缓存）
  Future<PermissionStatus> _checkPermissionDirect(PermissionType type) async {
    try {
      final result = await _channel.invokeMethod('checkPermission', {
        'type': type.name,
      });

      switch (result) {
        case 'granted':
          return PermissionStatus.granted;
        case 'denied':
          return PermissionStatus.denied;
        default:
          return PermissionStatus.unknown;
      }
    } catch (e) {
      debugPrint('检查权限失败: $e');
      return PermissionStatus.unknown;
    }
  }

  /// 检查所有权限状态
  Future<Map<PermissionType, PermissionStatus>> checkAllPermissions() async {
    final Map<PermissionType, PermissionStatus> results = {};

    for (final type in PermissionType.values) {
      results[type] = await checkPermission(type);
    }

    return results;
  }

  /// 请求单个权限
  Future<bool> requestPermission(PermissionType type) async {
    try {
      final result = await _channel.invokeMethod('requestPermission', {
        'type': type.name,
      });

      final granted = result as bool;

      // 更新权限缓存
      if (granted) {
        await PermissionCacheService.instance
            .cachePermissionStatus(type, PermissionStatus.granted);
      } else {
        await PermissionCacheService.instance
            .cachePermissionStatus(type, PermissionStatus.denied);
      }

      return granted;
    } catch (e) {
      debugPrint('请求权限失败: $e');
      // 缓存错误状态
      await PermissionCacheService.instance
          .cachePermissionStatus(type, PermissionStatus.unknown);
      return false;
    }
  }

  /// 检查是否具备基础专注功能所需权限
  Future<bool> hasBasicPermissions() async {
    final storageStatus = await checkPermission(PermissionType.storage);
    return storageStatus == PermissionStatus.granted;
  }

  /// 检查是否具备增强锁屏所需权限
  Future<bool> hasEnhancedPermissions() async {
    // 增强锁定现在基于系统级手势拦截，不再需要悬浮窗权限
    // 只需要基础存储权限即可
    final storageStatus = await checkPermission(PermissionType.storage);
    return storageStatus == PermissionStatus.granted;
  }

  /// 检查是否具备深度锁屏所需权限
  /// 优化：深度锁定使用五层防护体系，无障碍服务为可选增强
  Future<bool> hasDeepLockPermissions() async {
    // 深度锁定的基础要求是存储权限
    final storageStatus = await checkPermission(PermissionType.storage);
    return storageStatus == PermissionStatus.granted;

    // 注意：无障碍服务和设备管理员权限为可选增强功能
    // 即使没有这些权限，也可以使用系统级手势拦截器提供强力防护
  }

  /// 智能检查无障碍权限状态
  /// 返回详细的检查结果，包括是否需要重新授权
  Future<PermissionCheckResult> smartCheckAccessibilityPermission() async {
    return await PermissionCacheService.instance
        .smartPermissionCheck(PermissionType.accessibility);
  }

  /// 强制刷新权限状态（绕过缓存）
  Future<PermissionStatus> forceCheckPermission(PermissionType type) async {
    return await PermissionCacheService.instance
        .forceRefreshPermissionStatus(type);
  }

  /// 强制刷新所有权限状态
  Future<Map<PermissionType, PermissionStatus>>
      forceCheckAllPermissions() async {
    final Map<PermissionType, PermissionStatus> results = {};

    for (final type in PermissionType.values) {
      results[type] = await forceCheckPermission(type);
    }

    return results;
  }

  /// 获取缺失的必需权限
  Future<List<PermissionType>> getMissingRequiredPermissions() async {
    final List<PermissionType> missing = [];

    for (final info in _permissionInfos.values) {
      if (info.isRequired) {
        final status = await checkPermission(info.type);
        if (status != PermissionStatus.granted) {
          missing.add(info.type);
        }
      }
    }

    return missing;
  }

  /// 获取缺失的可选权限
  Future<List<PermissionType>> getMissingOptionalPermissions() async {
    final List<PermissionType> missing = [];

    for (final info in _permissionInfos.values) {
      if (!info.isRequired) {
        final status = await checkPermission(info.type);
        if (status != PermissionStatus.granted) {
          missing.add(info.type);
        }
      }
    }

    return missing;
  }

  /// 显示权限引导对话框
  Future<bool?> showPermissionGuide(
    BuildContext context,
    PermissionType type,
  ) async {
    // 对于无障碍服务，使用增强版对话框
    if (type == PermissionType.accessibility) {
      return showEnhancedAccessibilityPermissionDialog(context);
    }

    final info = getPermissionInfo(type);

    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Text(info.icon, style: const TextStyle(fontSize: 24)),
            const SizedBox(width: 8),
            Text(info.title),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(info.description),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(Icons.info_outline, color: Colors.blue),
                  const SizedBox(width: 8),
                  Expanded(child: Text(info.guideText)),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('稍后设置'),
          ),
          ElevatedButton(
            onPressed: () async {
              // 记录用户同意
              await PermissionCacheService.instance.recordUserConsent(type);
              Navigator.of(context).pop(true);
            },
            child: const Text('立即授权'),
          ),
        ],
      ),
    );
  }

  /// 显示增强版无障碍服务权限对话框
  Future<bool?> showEnhancedAccessibilityPermissionDialog(
      BuildContext context) async {
    bool? result;

    await showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => EnhancedAccessibilityPermissionDialog(
        onPermissionGranted: () async {
          // 记录用户同意无障碍服务权限
          await PermissionCacheService.instance
              .recordUserConsent(PermissionType.accessibility);
          result = true;
          Navigator.of(context).pop();
        },
        onPermissionDenied: () {
          result = false;
          Navigator.of(context).pop();
        },
      ),
    );

    return result;
  }

  /// 显示权限设置页面
  Future<void> openPermissionSettings(PermissionType type) async {
    try {
      await _channel.invokeMethod('openPermissionSettings', {
        'type': type.name,
      });
    } catch (e) {
      debugPrint('打开权限设置失败: $e');
    }
  }

  /// 测试无障碍服务详细状态
  Future<Map<String, dynamic>> testAccessibilityService() async {
    try {
      final result = await _channel.invokeMethod('testAccessibilityService');
      debugPrint('无障碍服务详细状态: $result');
      return Map<String, dynamic>.from(result);
    } catch (e) {
      debugPrint('测试无障碍服务失败: $e');
      return {'error': e.toString()};
    }
  }

  /// 自动启用无障碍服务
  Future<bool> enableAccessibilityServiceAuto() async {
    try {
      final result =
          await _channel.invokeMethod('enableAccessibilityServiceAuto');
      debugPrint('自动启用无障碍服务结果: $result');

      // 如果自动启用成功，等待一下再次确认状态
      if (result as bool) {
        await Future.delayed(const Duration(milliseconds: 500));
        // 强制刷新权限缓存
        final finalStatus = await PermissionCacheService.instance
            .forceRefreshPermissionStatus(PermissionType.accessibility);
        debugPrint('自动启用后最终状态检查: $finalStatus');
        return finalStatus == PermissionStatus.granted;
      }

      return false;
    } catch (e) {
      debugPrint('自动启用无障碍服务失败: $e');
      return false;
    }
  }

  /// 请求无障碍服务权限并提供引导 - 增强版
  Future<AccessibilityServiceGuidanceResult>
      requestAccessibilityServiceWithGuidance() async {
    try {
      final result = await _channel
          .invokeMethod('requestAccessibilityServiceWithGuidance');
      debugPrint('无障碍服务引导请求结果: $result');

      return AccessibilityServiceGuidanceResult.fromMap(
          Map<String, dynamic>.from(result));
    } catch (e) {
      debugPrint('请求无障碍服务引导失败: $e');
      return AccessibilityServiceGuidanceResult(
        success: false,
        isCurrentlyEnabled: false,
        autoEnableAttempted: false,
        autoEnableSuccess: false,
        requiresManualSetup: true,
        error: e.toString(),
      );
    }
  }

  /// 自动禁用无障碍服务
  Future<bool> disableAccessibilityServiceAuto() async {
    try {
      final result =
          await _channel.invokeMethod('disableAccessibilityServiceAuto');
      debugPrint('自动禁用无障碍服务结果: $result');
      return result as bool;
    } catch (e) {
      debugPrint('自动禁用无障碍服务失败: $e');
      return false;
    }
  }

  /// 检查系统权限状态
  Future<Map<String, dynamic>> checkSystemPermissions() async {
    try {
      final result = await _channel.invokeMethod('checkSystemPermissions');
      debugPrint('系统权限检查结果: $result');
      return Map<String, dynamic>.from(result);
    } catch (e) {
      debugPrint('检查系统权限失败: $e');
      return {'error': e.toString()};
    }
  }

  /// 通过Root权限授予系统权限
  Future<bool> grantPermissionsViaRoot() async {
    try {
      final result = await _channel.invokeMethod('grantPermissionsViaRoot');
      debugPrint('Root权限授予结果: $result');
      return result as bool;
    } catch (e) {
      debugPrint('Root权限授予失败: $e');
      return false;
    }
  }

  /// 检查ROOT状态
  Future<Map<String, dynamic>> checkRootStatus() async {
    try {
      final result = await _channel.invokeMethod('checkRootStatus');
      debugPrint('ROOT状态检查结果: $result');
      return Map<String, dynamic>.from(result);
    } catch (e) {
      debugPrint('ROOT状态检查失败: $e');
      return {'error': e.toString()};
    }
  }

  /// 生成ADB命令
  Future<Map<String, dynamic>> generateAdbCommands() async {
    try {
      final result = await _channel.invokeMethod('generateAdbCommands');
      debugPrint('ADB命令生成结果: $result');
      return Map<String, dynamic>.from(result);
    } catch (e) {
      debugPrint('ADB命令生成失败: $e');
      return {'error': e.toString()};
    }
  }

  /// 批量请求权限（渐进式）
  Future<Map<PermissionType, bool>> requestPermissionsGradually(
    BuildContext context,
    List<PermissionType> permissions,
  ) async {
    final Map<PermissionType, bool> results = {};

    for (final type in permissions) {
      // 检查当前权限状态
      final status = await checkPermission(type);
      if (status == PermissionStatus.granted) {
        results[type] = true;
        continue;
      }

      // 显示权限引导
      final shouldRequest = await showPermissionGuide(context, type);
      if (shouldRequest == true) {
        // 请求权限
        final granted = await requestPermission(type);
        results[type] = granted;

        if (!granted) {
          // 如果是必需权限且未授权，提示用户
          final info = getPermissionInfo(type);
          if (info.isRequired) {
            await _showPermissionRequiredDialog(context, info);
          }
        }
      } else {
        results[type] = false;
      }
    }

    return results;
  }

  /// 显示必需权限未授权对话框
  Future<void> _showPermissionRequiredDialog(
    BuildContext context,
    PermissionInfo info,
  ) async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('权限必需'),
        content: Text('${info.title}是应用正常运行的必需权限，请在设置中手动开启。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('我知道了'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              openPermissionSettings(info.type);
            },
            child: const Text('去设置'),
          ),
        ],
      ),
    );
  }
}
