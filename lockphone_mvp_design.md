# 锁手机APP MVP设计方案

## 1. MVP核心理念

### 1.1 MVP定位
**一句话描述**：一个真正"锁得住"的专注应用，让用户无法逃离专注状态

**核心价值主张**：
- 市场上唯一真正无法退出的专注应用
- 3步即可开始专注，操作极简
- 强制专注体验，专治各种不专注

### 1.2 MVP验证假设
- **核心假设1**：用户确实需要"强制性"的专注工具
- **核心假设2**：技术实现的Kiosk模式能够有效防止用户逃离
- **核心假设3**：用户能够接受为了专注而暂时"失去自由"
- **核心假设4**：监狱主题的包装能让强制体验变得有趣而非压抑

## 2. MVP功能矩阵

### 2.1 核心功能（必须有）
| 功能 | 重要性 | 开发难度 | MVP包含 | 理由 |
|------|--------|----------|---------|------|
| 强制专注模式 | ⭐⭐⭐⭐⭐ | 🔥🔥🔥🔥 | ✅ | 产品唯一卖点，技术壁垒 |
| 基础计时器 | ⭐⭐⭐⭐⭐ | 🔥🔥 | ✅ | 专注核心体验 |
| 紧急退出机制 | ⭐⭐⭐⭐⭐ | 🔥🔥🔥 | ✅ | 用户安全保障 |
| 权限申请流程 | ⭐⭐⭐⭐⭐ | 🔥🔥🔥 | ✅ | 功能实现前提 |

### 2.2 重要功能（应该有）
| 功能 | 重要性 | 开发难度 | MVP包含 | 理由 |
|------|--------|----------|---------|------|
| 专注记录统计 | ⭐⭐⭐⭐ | 🔥🔥 | ✅ | 成就感来源 |
| 任务分类设置 | ⭐⭐⭐⭐ | 🔥🔥 | ✅ | 增强目标感 |
| 基础成就系统 | ⭐⭐⭐ | 🔥🔥 | ✅ | 激励用户继续使用 |
| 个性化设置 | ⭐⭐⭐ | 🔥🔥 | ⚡ | 简化版本 |

### 2.3 期望功能（可以等）
| 功能 | 重要性 | 开发难度 | MVP包含 | 后续版本 |
|------|--------|----------|---------|----------|
| 专注社区 | ⭐⭐⭐⭐ | 🔥🔥🔥🔥 | ❌ | V1.1 |
| 智能建议 | ⭐⭐⭐ | 🔥🔥🔥🔥 | ❌ | V1.2 |
| 多样化主题 | ⭐⭐ | 🔥🔥 | ❌ | V1.1 |
| 背景音效 | ⭐⭐ | 🔥🔥 | ❌ | V1.1 |

## 3. MVP用户体验流程

### 3.1 首次启动体验
```
1. 欢迎页面
   - "欢迎来到专注监狱"
   - 一句话说明产品价值
   - 开始使用按钮

2. 权限申请引导
   - 动画演示为什么需要权限
   - "为了真正锁住你的专注"
   - 分步骤申请权限，每步都有明确说明

3. 功能演示
   - 30秒快速演示核心功能
   - 重点说明紧急退出方式
   - 首次专注建议（5分钟体验）

4. 创建首次专注任务
   - 选择专注类型
   - 设定专注时间
   - 确认开始
```

### 3.2 核心专注流程
```
1. 任务创建
   - 快速选择预设任务类型
   - 设定时间（预设：25分钟）
   - 一键开始

2. 进入专注状态
   - 3-2-1倒计时仪式感
   - 系统权限激活
   - 进入监狱主题界面

3. 专注过程
   - 大字号倒计时显示
   - 简洁的进度条
   - 激励语句显示
   - 隐藏的紧急退出入口

4. 完成专注
   - 胜利动画
   - 本次专注总结
   - 引导用户查看成就
```

### 3.3 紧急退出流程
```
1. 触发方式
   - 连续点击左上角10次
   - 显示"越狱申请"界面

2. 确认流程
   - 显示警告："确定要逃离专注监狱吗？"
   - 15秒冷却倒计时
   - 最终确认按钮

3. 退出后处理
   - 记录为"越狱"
   - 显示幽默的"通缉令"
   - 不影响历史统计
```

## 4. MVP界面设计

### 4.1 设计原则
- **极简主义**：每个界面最多3个主要元素
- **监狱主题**：酷炫而非压抑的监狱风格
- **强对比**：黑白灰主色调，重点元素用亮色
- **大字体**：倒计时数字占屏幕30%以上

### 4.2 核心界面设计

#### 4.2.1 主界面
```
+----------------------------------+
|  🔒 专注监狱 FOCUS PRISON        |
|                                  |
|  [创建专注任务]                   |
|                                  |
|  今日专注: 2次 | 45分钟            |
|  连续专注: 3天                    |
|                                  |
|  📊 我的记录  🏆 成就            |
|  ⚙️ 设置                         |
+----------------------------------+
```

#### 4.2.2 任务创建界面
```
+----------------------------------+
|  创建专注任务                     |
|                                  |
|  📝 任务类型                     |
|  🎯 深度工作  📚 学习充电         |
|  🧘 冥想修行  💡 创意思考         |
|                                  |
|  ⏰ 专注时间                     |
|  [25分钟] 45分钟  90分钟  自定义  |
|                                  |
|  [开始专注]                       |
+----------------------------------+
```

#### 4.2.3 专注界面
```
+----------------------------------+
|                                  |
|           24:35                  |
|        ●●●●●●●○○○                |
|                                  |
|      深度工作进行中               |
|                                  |
|    "你正在做一件了不起的事"        |
|                                  |
|     第2次专注 | 今日45分钟        |
|                                  |
+----------------------------------+
```

## 5. MVP技术方案

### 5.1 技术架构
```
MVP技术栈：
- 开发语言：Flutter (Dart)
- 架构：BLoC Pattern + Provider
- 数据库：SQLite (sqflite)
- 权限管理：Android Device Admin (通过Method Channel)
- UI框架：Flutter Material Design
- 状态管理：Provider + ChangeNotifier
```

### 5.2 核心技术实现

#### 5.2.1 Flutter + Native混合架构
```dart
// Flutter端架构
class KioskModeManager {
  static const MethodChannel _channel = MethodChannel('kiosk_mode');
  
  // 启用Kiosk模式
  Future<bool> enableKioskMode() async {
    try {
      final result = await _channel.invokeMethod('enableKioskMode');
      return result as bool;
    } catch (e) {
      print('启用Kiosk模式失败: $e');
      return false;
    }
  }
  
  // 禁用Kiosk模式
  Future<bool> disableKioskMode() async {
    try {
      final result = await _channel.invokeMethod('disableKioskMode');
      return result as bool;
    } catch (e) {
      print('禁用Kiosk模式失败: $e');
      return false;
    }
  }
}
```

#### 5.2.2 Android原生实现（Method Channel）
```kotlin
// android/app/src/main/kotlin/MainActivity.kt
class MainActivity : FlutterActivity() {
    private val CHANNEL = "kiosk_mode"
    
    override fun configureFlutterEngine(@NonNull flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)
            .setMethodCallHandler { call, result ->
                when (call.method) {
                    "enableKioskMode" -> {
                        val success = enableKioskMode()
                        result.success(success)
                    }
                    "disableKioskMode" -> {
                        val success = disableKioskMode()
                        result.success(success)
                    }
                    else -> result.notImplemented()
                }
            }
    }
    
    private fun enableKioskMode(): Boolean {
        return try {
            // 隐藏状态栏和导航栏
            hideSystemUI()
            
            // 禁用系统按键
            disableSystemKeys()
            
            // 创建系统覆盖窗口
            createSystemOverlay()
            
            true
        } catch (e: Exception) {
            false
        }
    }
    
    private fun hideSystemUI() {
        window.decorView.systemUiVisibility = (
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
            or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
            or View.SYSTEM_UI_FLAG_FULLSCREEN
        )
    }
}
```

#### 5.2.3 Flutter状态管理
```dart
// 专注状态管理
class FocusProvider extends ChangeNotifier {
  bool _isInFocusMode = false;
  int _remainingSeconds = 0;
  Timer? _timer;
  
  bool get isInFocusMode => _isInFocusMode;
  int get remainingSeconds => _remainingSeconds;
  
  // 开始专注
  Future<void> startFocus(int durationMinutes) async {
    _remainingSeconds = durationMinutes * 60;
    _isInFocusMode = true;
    
    // 启用Kiosk模式
    final success = await KioskModeManager().enableKioskMode();
    if (!success) {
      _isInFocusMode = false;
      throw Exception('无法启用专注模式');
    }
    
    // 启动计时器
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      _remainingSeconds--;
      notifyListeners();
      
      if (_remainingSeconds <= 0) {
        _endFocus();
      }
    });
    
    notifyListeners();
  }
  
  // 结束专注
  Future<void> _endFocus() async {
    _timer?.cancel();
    _isInFocusMode = false;
    
    // 禁用Kiosk模式
    await KioskModeManager().disableKioskMode();
    
    notifyListeners();
  }
}
```

#### 5.2.4 Flutter界面实现
```dart
// 专注界面
class FocusScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<FocusProvider>(
      builder: (context, focusProvider, child) {
        return Scaffold(
          backgroundColor: Colors.black,
          body: SafeArea(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 倒计时显示
                Text(
                  _formatTime(focusProvider.remainingSeconds),
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 72,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Orbitron',
                  ),
                ),
                SizedBox(height: 40),
                
                // 进度条
                Container(
                  width: 200,
                  child: LinearProgressIndicator(
                    value: focusProvider.progress,
                    backgroundColor: Colors.grey[800],
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.cyan),
                  ),
                ),
                
                SizedBox(height: 60),
                
                // 激励文案
                Text(
                  "你正在做一件了不起的事",
                  style: TextStyle(
                    color: Colors.grey[400],
                    fontSize: 18,
                  ),
                ),
                
                SizedBox(height: 20),
                
                // 当前专注信息
                Text(
                  "深度工作进行中 · 第2次专注",
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
                
                // 隐藏的紧急退出触发区域
                Positioned(
                  top: 0,
                  left: 0,
                  child: GestureDetector(
                    onTap: () => _handleEmergencyExit(context),
                    child: Container(
                      width: 50,
                      height: 50,
                      color: Colors.transparent,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
  
  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }
}
```

#### 5.2.5 数据存储实现
```dart
// 数据库服务
class DatabaseService {
  static late Database _database;
  
  static Future<void> init() async {
    _database = await openDatabase(
      'focus_app.db',
      version: 1,
      onCreate: (db, version) {
        // 创建专注记录表
        db.execute('''
          CREATE TABLE focus_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            task_type TEXT,
            duration_minutes INTEGER,
            completed BOOLEAN,
            start_time TEXT,
            end_time TEXT,
            is_escaped BOOLEAN DEFAULT 0
          )
        ''');
        
        // 创建用户设置表
        db.execute('''
          CREATE TABLE user_settings (
            key TEXT PRIMARY KEY,
            value TEXT
          )
        ''');
      },
    );
  }
  
  // 保存专注记录
  static Future<void> saveFocusSession(FocusSession session) async {
    await _database.insert('focus_sessions', session.toMap());
  }
  
  // 获取专注记录
  static Future<List<FocusSession>> getFocusSessions() async {
    final List<Map<String, dynamic>> maps = await _database.query('focus_sessions');
    return List.generate(maps.length, (i) {
      return FocusSession.fromMap(maps[i]);
    });
  }
}
```

### 5.3 Flutter项目结构
```
lib/
├── main.dart                 # 应用入口
├── models/                   # 数据模型
│   ├── focus_session.dart
│   └── user_settings.dart
├── providers/                # 状态管理
│   ├── focus_provider.dart
│   └── statistics_provider.dart
├── screens/                  # 界面页面
│   ├── home_screen.dart
│   ├── focus_screen.dart
│   ├── create_task_screen.dart
│   └── statistics_screen.dart
├── services/                 # 服务层
│   ├── database_service.dart
│   ├── kiosk_mode_manager.dart
│   └── notification_service.dart
├── widgets/                  # 通用组件
│   ├── focus_timer_widget.dart
│   ├── task_type_selector.dart
│   └── emergency_exit_dialog.dart
└── utils/                    # 工具类
    ├── constants.dart
    └── time_formatter.dart
```

### 5.4 Flutter依赖配置
```yaml
# pubspec.yaml
dependencies:
  flutter:
    sdk: flutter
  
  # 状态管理
  provider: ^6.0.5
  
  # 数据库
  sqflite: ^2.3.0
  path: ^1.8.3
  
  # UI组件
  flutter_svg: ^2.0.9
  animated_text_kit: ^4.2.2
  
  # 权限管理
  permission_handler: ^11.0.1
  
  # 本地存储
  shared_preferences: ^2.2.2
  
  # 时间处理
  intl: ^0.18.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
```

## 6. MVP数据指标

### 6.1 核心验证指标
- **技术可行性**：Kiosk模式成功率 > 95%
- **用户接受度**：首次完成专注率 > 60%
- **用户留存**：3日留存率 > 40%
- **功能使用**：平均专注时长 > 20分钟

### 6.2 用户行为指标
- **权限授权率**：必要权限授权 > 80%
- **任务完成率**：专注任务完成 > 70%
- **紧急退出率**：越狱使用率 < 20%
- **重复使用率**：每日平均专注次数 > 2次

### 6.3 产品质量指标
- **应用稳定性**：崩溃率 < 1%
- **性能表现**：启动时间 < 3秒
- **兼容性**：主流设备适配 > 95%
- **用户反馈**：应用商店评分 > 4.0

## 7. MVP开发计划

### 7.1 开发阶段划分
```
Phase 1: Flutter架构搭建 (3周)
- Flutter项目初始化
- 基础架构搭建（BLoC + Provider）
- 数据库设计和Service层
- Android原生模块开发（Method Channel）

Phase 2: 核心功能开发 (4周)
- Kiosk模式实现和测试
- 专注计时器功能
- 权限申请流程
- 数据存储功能

Phase 3: 界面和体验优化 (3周)
- Flutter界面设计实现
- 动画和交互效果
- 紧急退出机制
- 用户引导流程

Phase 4: 测试与发布 (2周)
- 单元测试和集成测试
- 多设备兼容性测试
- 性能优化
- 应用商店上架
```

### 7.2 Flutter特有的技术风险
- **Method Channel稳定性**：Flutter与Android原生通信可能存在延迟
- **热重载限制**：Kiosk模式下无法使用热重载调试
- **包体积控制**：Flutter应用包体积相对较大
- **Android权限兼容**：不同Android版本的权限申请差异

## 8. MVP成功标准

### 8.1 产品验证成功标准
- ✅ 核心功能正常运行（Kiosk模式有效）
- ✅ 用户能够理解并使用产品
- ✅ 技术方案得到验证
- ✅ 获得初步用户反馈

### 8.2 用户验证成功标准
- ✅ 500+ 种子用户使用
- ✅ 应用商店评分 4.0+
- ✅ 用户平均专注时长 25分钟+
- ✅ 日活用户留存率 30%+

### 8.3 商业验证成功标准
- ✅ 验证用户付费意愿
- ✅ 获得正面的媒体报道
- ✅ 在专注应用类别中获得关注
- ✅ 为后续版本制定清晰路线图

## 9. 后续迭代规划

### 9.1 基于MVP反馈的迭代
根据MVP用户反馈，优先迭代：
- 用户反馈最多的功能问题
- 技术实现中的稳定性问题
- 用户体验中的痛点

### 9.2 功能扩展路线图
- **V1.1版本**：社交功能（专注房间、排行榜）
- **V1.2版本**：智能化功能（推荐、分析）
- **V1.3版本**：商业化功能（会员、付费）

## 10. MVP执行建议

### 10.1 Flutter开发优势
- **跨平台潜力**：后续可快速扩展到iOS平台
- **开发效率**：Flutter热重载提升开发速度
- **UI一致性**：保证不同设备上的界面统一
- **性能表现**：Flutter性能接近原生应用

### 10.2 技术架构优势
- **混合开发**：Flutter处理UI，Android原生处理系统权限
- **状态管理**：Provider模式简单易懂，便于维护
- **代码复用**：业务逻辑可在后续iOS版本中复用
- **社区支持**：Flutter生态丰富，第三方库完善

### 10.3 Flutter开发建议
1. **优先保证Android原生功能稳定**：Kiosk模式是核心卖点
2. **Method Channel通信优化**：确保Flutter与原生通信流畅
3. **充分测试混合架构**：重点测试状态同步和错误处理
4. **考虑后续iOS扩展**：代码结构设计要考虑跨平台兼容

### 10.4 开发团队建议
- **1名Flutter开发工程师**：负责Flutter端开发
- **1名Android原生开发工程师**：负责Kiosk模式和权限管理
- **1名UI/UX设计师**：负责界面设计和用户体验
- **1名产品经理**：负责需求管理和测试协调

---

这个MVP设计专注于验证核心假设：用户是否真的需要一个"无法逃离"的专注应用。通过最小化但完整的功能集，快速获得市场反馈，为后续迭代奠定基础。