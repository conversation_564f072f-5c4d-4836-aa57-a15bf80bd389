<?xml version="1.0" encoding="utf-8"?>
<accessibility-service xmlns:android="http://schemas.android.com/apk/res/android"
    android:description="@string/yoyo_accessibility_service_description"
    android:packageNames=""
    android:accessibilityEventTypes="typeAllMask"
    android:accessibilityFlags="flagDefault|flagReportViewIds|flagRetrieveInteractiveWindows|flagRequestTouchExplorationMode|flagRequestEnhancedWebAccessibility|flagRequestFilterKeyEvents|flagIncludeNotImportantViews|flagRequestAccessibilityButton"
    android:accessibilityFeedbackType="feedbackGeneric|feedbackHaptic|feedbackAudible"
    android:notificationTimeout="50"
    android:canRetrieveWindowContent="true"
    android:canRequestTouchExplorationMode="true"
    android:canRequestEnhancedWebAccessibility="true"
    android:canRequestFilterKeyEvents="true"
    android:canPerformGestures="true"
    android:settingsActivity="com.example.lockphone.MainActivity" />
