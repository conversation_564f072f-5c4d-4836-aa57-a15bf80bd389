import 'package:flutter/foundation.dart';
import 'permission_manager.dart';
import 'focus_manager.dart' as focus_service;

/// 权限状态服务
/// 基于五层防护体系，简化权限状态管理
class PermissionStatusService {
  static final PermissionStatusService _instance =
      PermissionStatusService._internal();
  factory PermissionStatusService() => _instance;
  PermissionStatusService._internal();

  static PermissionStatusService get instance => _instance;

  /// 权限状态缓存
  final Map<PermissionType, PermissionStatus> _statusCache = {};

  /// 上次检查时间
  DateTime? _lastCheckTime;

  /// 缓存有效期（5分钟）
  static const Duration _cacheValidDuration = Duration(minutes: 5);

  /// 获取权限状态（带缓存）
  Future<PermissionStatus> getPermissionStatus(PermissionType type) async {
    // 检查缓存是否有效
    if (_isCacheValid() && _statusCache.containsKey(type)) {
      return _statusCache[type]!;
    }

    // 重新检查权限状态
    final status = await PermissionManager.instance.checkPermission(type);
    _statusCache[type] = status;
    _lastCheckTime = DateTime.now();

    return status;
  }

  /// 批量获取权限状态
  Future<Map<PermissionType, PermissionStatus>> getBatchPermissionStatus(
    List<PermissionType> types,
  ) async {
    final Map<PermissionType, PermissionStatus> result = {};

    for (final type in types) {
      result[type] = await getPermissionStatus(type);
    }

    return result;
  }

  /// 检查是否满足锁定级别要求
  Future<bool> canUseLockLevel(focus_service.LockLevel level) async {
    switch (level) {
      case focus_service.LockLevel.basic:
        // 基础锁定只需要存储权限
        final storage = await getPermissionStatus(PermissionType.storage);
        return storage == PermissionStatus.granted;

      case focus_service.LockLevel.enhanced:
        // 增强锁定基于系统级手势拦截，只需要存储权限
        final storage = await getPermissionStatus(PermissionType.storage);
        return storage == PermissionStatus.granted;

      case focus_service.LockLevel.deep:
        // 深度锁定基于五层防护体系，只需要存储权限
        final storage = await getPermissionStatus(PermissionType.storage);
        return storage == PermissionStatus.granted;
    }
  }

  /// 获取当前可用的最高锁定级别
  Future<focus_service.LockLevel> getMaxAvailableLockLevel() async {
    // 检查存储权限
    final storage = await getPermissionStatus(PermissionType.storage);
    if (storage != PermissionStatus.granted) {
      // 如果连存储权限都没有，返回基础级别（但实际无法使用）
      return focus_service.LockLevel.basic;
    }

    // 基于五层防护体系，所有级别都可用
    // 检查可选增强权限来确定推荐级别
    final accessibility =
        await getPermissionStatus(PermissionType.accessibility);
    final deviceAdmin = await getPermissionStatus(PermissionType.deviceAdmin);

    if (accessibility == PermissionStatus.granted &&
        deviceAdmin == PermissionStatus.granted) {
      return focus_service.LockLevel.deep; // 终极防护
    } else if (accessibility == PermissionStatus.granted) {
      return focus_service.LockLevel.deep; // 强力防护
    } else {
      return focus_service.LockLevel.enhanced; // 标准防护（基于五层防护体系）
    }
  }

  /// 获取防护强度描述
  Future<String> getProtectionDescription() async {
    final storage = await getPermissionStatus(PermissionType.storage);
    final accessibility =
        await getPermissionStatus(PermissionType.accessibility);
    final deviceAdmin = await getPermissionStatus(PermissionType.deviceAdmin);

    if (storage != PermissionStatus.granted) {
      return '⚠️ 需要基础权限';
    }

    if (accessibility == PermissionStatus.granted &&
        deviceAdmin == PermissionStatus.granted) {
      return '🛡️ 终极防护 (五层防护体系 + 系统级控制)';
    } else if (accessibility == PermissionStatus.granted) {
      return '🔒 强力防护 (五层防护体系 + 无障碍增强)';
    } else {
      return '🔐 标准防护 (五层防护体系)';
    }
  }

  /// 获取缺失的权限列表
  Future<List<PermissionType>> getMissingPermissions(
      focus_service.LockLevel level) async {
    final List<PermissionType> missing = [];

    // 检查必需权限
    final storage = await getPermissionStatus(PermissionType.storage);
    if (storage != PermissionStatus.granted) {
      missing.add(PermissionType.storage);
    }

    return missing;
  }

  /// 获取可选增强权限列表
  Future<List<PermissionType>> getOptionalPermissions() async {
    final List<PermissionType> optional = [];

    final accessibility =
        await getPermissionStatus(PermissionType.accessibility);
    if (accessibility != PermissionStatus.granted) {
      optional.add(PermissionType.accessibility);
    }

    final deviceAdmin = await getPermissionStatus(PermissionType.deviceAdmin);
    if (deviceAdmin != PermissionStatus.granted) {
      optional.add(PermissionType.deviceAdmin);
    }

    return optional;
  }

  /// 刷新权限状态
  Future<void> refreshPermissionStatus() async {
    _statusCache.clear();
    _lastCheckTime = null;

    // 重新检查所有权限
    final types = [
      PermissionType.storage,
      PermissionType.accessibility,
      PermissionType.deviceAdmin,
      PermissionType.notification,
    ];

    for (final type in types) {
      await getPermissionStatus(type);
    }

    debugPrint('权限状态已刷新');
  }

  /// 检查缓存是否有效
  bool _isCacheValid() {
    if (_lastCheckTime == null) return false;

    final now = DateTime.now();
    final difference = now.difference(_lastCheckTime!);

    return difference < _cacheValidDuration;
  }

  /// 清除缓存
  void clearCache() {
    _statusCache.clear();
    _lastCheckTime = null;
  }

  /// 获取权限状态摘要
  Future<PermissionStatusSummary> getPermissionSummary() async {
    final storage = await getPermissionStatus(PermissionType.storage);
    final accessibility =
        await getPermissionStatus(PermissionType.accessibility);
    final deviceAdmin = await getPermissionStatus(PermissionType.deviceAdmin);
    final notification = await getPermissionStatus(PermissionType.notification);

    final maxLevel = await getMaxAvailableLockLevel();
    final description = await getProtectionDescription();
    final missing = await getMissingPermissions(maxLevel);
    final optional = await getOptionalPermissions();

    return PermissionStatusSummary(
      storage: storage,
      accessibility: accessibility,
      deviceAdmin: deviceAdmin,
      notification: notification,
      maxAvailableLevel: maxLevel,
      protectionDescription: description,
      missingPermissions: missing,
      optionalPermissions: optional,
      canStartFocus: storage == PermissionStatus.granted,
    );
  }
}

/// 权限状态摘要
class PermissionStatusSummary {
  final PermissionStatus storage;
  final PermissionStatus accessibility;
  final PermissionStatus deviceAdmin;
  final PermissionStatus notification;
  final focus_service.LockLevel maxAvailableLevel;
  final String protectionDescription;
  final List<PermissionType> missingPermissions;
  final List<PermissionType> optionalPermissions;
  final bool canStartFocus;

  const PermissionStatusSummary({
    required this.storage,
    required this.accessibility,
    required this.deviceAdmin,
    required this.notification,
    required this.maxAvailableLevel,
    required this.protectionDescription,
    required this.missingPermissions,
    required this.optionalPermissions,
    required this.canStartFocus,
  });

  @override
  String toString() {
    return 'PermissionStatusSummary('
        'storage: $storage, '
        'accessibility: $accessibility, '
        'deviceAdmin: $deviceAdmin, '
        'notification: $notification, '
        'maxLevel: $maxAvailableLevel, '
        'canStart: $canStartFocus'
        ')';
  }
}
