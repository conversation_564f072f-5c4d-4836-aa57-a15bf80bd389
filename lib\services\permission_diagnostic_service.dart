import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'permission_manager.dart';
import 'permission_cache_service.dart';

/// 权限诊断服务
/// 用于调试和诊断权限检查问题
class PermissionDiagnosticService {
  static PermissionDiagnosticService? _instance;
  static PermissionDiagnosticService get instance =>
      _instance ??= PermissionDiagnosticService._();

  PermissionDiagnosticService._();

  static const MethodChannel _channel = MethodChannel('yoyo_lock_screen');

  /// 全面诊断权限状态
  Future<PermissionDiagnosticResult> diagnosePermission(
      PermissionType type) async {
    final result = PermissionDiagnosticResult();
    result.permissionType = type;
    result.timestamp = DateTime.now();

    try {
      debugPrint('开始诊断权限: ${type.name}');

      // 1. 检查缓存状态
      result.cachedStatus =
          await PermissionCacheService.instance.getCachedPermissionStatus(type);
      debugPrint('缓存状态: ${result.cachedStatus?.name ?? 'null'}');

      // 2. 直接检查系统状态
      result.systemStatus = await _checkSystemPermissionDirect(type);
      debugPrint('系统状态: ${result.systemStatus.name}');

      // 3. 检查用户历史
      result.hasUserConsent =
          await PermissionCacheService.instance.hasUserConsent(type);
      result.hasAutoEnableAttempted =
          await PermissionCacheService.instance.hasAutoEnableAttempted(type);
      debugPrint(
          '用户同意: ${result.hasUserConsent}, 自动启用尝试: ${result.hasAutoEnableAttempted}');

      // 4. 状态一致性检查
      result.isConsistent = result.cachedStatus == result.systemStatus;
      debugPrint('状态一致性: ${result.isConsistent}');

      // 5. 特殊检查（针对无障碍服务）
      if (type == PermissionType.accessibility) {
        result.accessibilityDetails = await _diagnoseAccessibilityService();
      }

      // 6. 生成建议
      result.recommendations = _generateRecommendations(result);

      debugPrint('权限诊断完成');
      return result;
    } catch (e) {
      debugPrint('权限诊断失败: $e');
      result.error = e.toString();
      return result;
    }
  }

  /// 直接检查系统权限状态
  Future<PermissionStatus> _checkSystemPermissionDirect(
      PermissionType type) async {
    try {
      final result = await _channel.invokeMethod('checkPermission', {
        'type': type.name,
      });

      switch (result) {
        case 'granted':
          return PermissionStatus.granted;
        case 'denied':
          return PermissionStatus.denied;
        default:
          return PermissionStatus.unknown;
      }
    } catch (e) {
      debugPrint('直接检查系统权限失败: $e');
      return PermissionStatus.unknown;
    }
  }

  /// 诊断无障碍服务详细状态
  Future<AccessibilityDiagnosticDetails> _diagnoseAccessibilityService() async {
    final details = AccessibilityDiagnosticDetails();

    try {
      // 调用Android端的详细检查
      final result = await _channel.invokeMethod('testAccessibilityService');

      if (result is Map) {
        details.managerCheck = result['accessibilityManager'] ?? false;
        details.settingsCheck = result['settings'] ?? false;
        details.instanceCheck = result['instance'] ?? false;
        details.serviceReady = result['serviceReady'] ?? false;
        details.enabledServices =
            List<String>.from(result['enabledServices'] ?? []);
        details.targetServiceFound = result['targetServiceFound'] ?? false;
      }
    } catch (e) {
      debugPrint('诊断无障碍服务失败: $e');
      details.error = e.toString();
    }

    return details;
  }

  /// 生成修复建议
  List<String> _generateRecommendations(PermissionDiagnosticResult result) {
    final recommendations = <String>[];

    // 状态不一致的建议
    if (!result.isConsistent) {
      recommendations.add('缓存状态与系统状态不一致，建议强制刷新缓存');
    }

    // 权限被拒绝的建议
    if (result.systemStatus == PermissionStatus.denied) {
      if (result.hasUserConsent && !result.hasAutoEnableAttempted) {
        recommendations.add('用户已同意但未尝试自动启用，建议尝试自动启用');
      } else if (result.hasUserConsent && result.hasAutoEnableAttempted) {
        recommendations.add('自动启用已尝试但失败，建议引导用户手动设置');
      } else {
        recommendations.add('用户未同意权限，建议显示权限说明对话框');
      }
    }

    // 无障碍服务特殊建议
    if (result.permissionType == PermissionType.accessibility &&
        result.accessibilityDetails != null) {
      final details = result.accessibilityDetails!;

      if (!details.managerCheck &&
          !details.settingsCheck &&
          !details.instanceCheck) {
        recommendations.add('所有检查方法都显示未启用，确认无障碍服务确实未开启');
      } else if (details.managerCheck != details.settingsCheck) {
        recommendations.add('AccessibilityManager和Settings检查结果不一致，可能存在系统延迟');
      }

      if (!details.serviceReady && details.instanceCheck) {
        recommendations.add('服务实例存在但未准备就绪，可能正在初始化');
      }
    }

    // 缓存相关建议
    if (result.cachedStatus == null) {
      recommendations.add('无缓存状态，建议进行首次权限检查');
    }

    return recommendations;
  }

  /// 修复权限状态不一致问题
  Future<bool> fixPermissionInconsistency(PermissionType type) async {
    try {
      debugPrint('开始修复权限状态不一致: ${type.name}');

      // 强制刷新权限状态
      final actualStatus =
          await PermissionManager.instance.forceCheckPermission(type);
      debugPrint('强制刷新后的状态: ${actualStatus.name}');

      // 如果权限实际已授予但缓存显示未授予，更新相关状态
      if (actualStatus == PermissionStatus.granted) {
        // 确保用户同意状态也被记录
        await PermissionCacheService.instance.recordUserConsent(type);
        debugPrint('权限已授予，更新用户同意状态');
      }

      return true;
    } catch (e) {
      debugPrint('修复权限状态不一致失败: $e');
      return false;
    }
  }

  /// 生成诊断报告
  String generateDiagnosticReport(PermissionDiagnosticResult result) {
    final buffer = StringBuffer();

    buffer.writeln('=== 权限诊断报告 ===');
    buffer.writeln('权限类型: ${result.permissionType.name}');
    buffer.writeln('诊断时间: ${result.timestamp}');
    buffer.writeln();

    buffer.writeln('状态信息:');
    buffer.writeln('  缓存状态: ${result.cachedStatus?.name ?? 'null'}');
    buffer.writeln('  系统状态: ${result.systemStatus.name}');
    buffer.writeln('  状态一致: ${result.isConsistent}');
    buffer.writeln();

    buffer.writeln('用户历史:');
    buffer.writeln('  用户同意: ${result.hasUserConsent}');
    buffer.writeln('  自动启用尝试: ${result.hasAutoEnableAttempted}');
    buffer.writeln();

    if (result.accessibilityDetails != null) {
      final details = result.accessibilityDetails!;
      buffer.writeln('无障碍服务详情:');
      buffer.writeln('  AccessibilityManager检查: ${details.managerCheck}');
      buffer.writeln('  Settings检查: ${details.settingsCheck}');
      buffer.writeln('  实例检查: ${details.instanceCheck}');
      buffer.writeln('  服务准备就绪: ${details.serviceReady}');
      buffer.writeln('  目标服务找到: ${details.targetServiceFound}');
      buffer.writeln('  已启用服务: ${details.enabledServices.join(', ')}');
      buffer.writeln();
    }

    if (result.recommendations.isNotEmpty) {
      buffer.writeln('修复建议:');
      for (int i = 0; i < result.recommendations.length; i++) {
        buffer.writeln('  ${i + 1}. ${result.recommendations[i]}');
      }
      buffer.writeln();
    }

    if (result.error != null) {
      buffer.writeln('错误信息: ${result.error}');
    }

    return buffer.toString();
  }
}

/// 权限诊断结果
class PermissionDiagnosticResult {
  PermissionType permissionType = PermissionType.accessibility;
  DateTime timestamp = DateTime.now();
  PermissionStatus? cachedStatus;
  PermissionStatus systemStatus = PermissionStatus.unknown;
  bool isConsistent = false;
  bool hasUserConsent = false;
  bool hasAutoEnableAttempted = false;
  AccessibilityDiagnosticDetails? accessibilityDetails;
  List<String> recommendations = [];
  String? error;
}

/// 无障碍服务诊断详情
class AccessibilityDiagnosticDetails {
  bool managerCheck = false;
  bool settingsCheck = false;
  bool instanceCheck = false;
  bool serviceReady = false;
  bool targetServiceFound = false;
  List<String> enabledServices = [];
  String? error;
}
