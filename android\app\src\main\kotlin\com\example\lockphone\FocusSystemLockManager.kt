package com.example.lockphone

import android.app.Activity
import android.app.KeyguardManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.os.PowerManager
import android.util.Log

/**
 * 专注模式系统锁屏管理器
 * 负责管理专注模式下的系统锁屏集成功能
 * 
 * 主要功能：
 * 1. 自动激活系统锁屏
 * 2. 监听解锁事件
 * 3. 解锁后立即拦截并激活专注界面
 * 4. 与现有Kiosk模式无缝集成
 */
class FocusSystemLockManager(
    private val activity: Activity,
    private val deviceAdminKioskManager: DeviceAdminKioskManager
) {
    
    companion object {
        private const val TAG = "FocusSystemLockManager"
        private const val UNLOCK_INTERCEPTION_DELAY = 500L // 解锁拦截延迟（毫秒）
    }
    
    // 系统服务
    private val keyguardManager = activity.getSystemService(Context.KEYGUARD_SERVICE) as KeyguardManager
    private val powerManager = activity.getSystemService(Context.POWER_SERVICE) as PowerManager
    
    // 状态管理
    private var isInFocusLockMode = false
    private var lockScreenReceiver: BroadcastReceiver? = null
    private var unlockInterceptionCallback: (() -> Unit)? = null
    
    /**
     * 启用专注模式系统锁屏
     * 
     * @param onUnlockInterception 解锁拦截回调函数
     * @return 是否成功启用
     */
    fun enableFocusSystemLock(onUnlockInterception: (() -> Unit)? = null): Boolean {
        return try {
            Log.d(TAG, "🔒 启用专注模式系统锁屏")
            
            // 保存解锁拦截回调
            unlockInterceptionCallback = onUnlockInterception
            
            // 1. 注册锁屏状态监听器
            val listenerResult = registerLockScreenStateListener()
            
            // 2. 启用设备管理员的专注锁屏功能
            val deviceAdminResult = deviceAdminKioskManager.enableFocusSystemLock()
            
            val success = listenerResult && deviceAdminResult
            
            if (success) {
                isInFocusLockMode = true
                Log.d(TAG, "✅ 专注模式系统锁屏已启用")
            } else {
                Log.w(TAG, "⚠️ 专注模式系统锁屏启用部分失败 (监听器:$listenerResult, 设备管理员:$deviceAdminResult)")
            }
            
            success
        } catch (e: Exception) {
            Log.e(TAG, "❌ 启用专注模式系统锁屏失败: ${e.message}")
            false
        }
    }
    
    /**
     * 禁用专注模式系统锁屏
     */
    fun disableFocusSystemLock(): Boolean {
        return try {
            Log.d(TAG, "🔒 禁用专注模式系统锁屏")
            
            // 1. 注销锁屏状态监听器
            unregisterLockScreenStateListener()
            
            // 2. 禁用设备管理员的专注锁屏功能
            val deviceAdminResult = deviceAdminKioskManager.disableFocusSystemLock()
            
            isInFocusLockMode = false
            unlockInterceptionCallback = null
            
            Log.d(TAG, "✅ 专注模式系统锁屏已禁用")
            deviceAdminResult
        } catch (e: Exception) {
            Log.e(TAG, "❌ 禁用专注模式系统锁屏失败: ${e.message}")
            false
        }
    }
    
    /**
     * 注册锁屏状态监听器
     */
    private fun registerLockScreenStateListener(): Boolean {
        return try {
            Log.d(TAG, "📱 注册锁屏状态监听器")
            
            lockScreenReceiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context?, intent: Intent?) {
                    when (intent?.action) {
                        Intent.ACTION_USER_PRESENT -> {
                            // 用户解锁后立即拦截
                            if (isInFocusLockMode) {
                                Log.d(TAG, "🔓 检测到用户解锁，准备拦截")
                                handleUserUnlock()
                            }
                        }
                        Intent.ACTION_SCREEN_OFF -> {
                            // 屏幕关闭时的处理
                            if (isInFocusLockMode) {
                                Log.d(TAG, "📱 屏幕关闭，保持专注模式状态")
                                handleScreenOff()
                            }
                        }
                        Intent.ACTION_SCREEN_ON -> {
                            // 屏幕点亮时的处理
                            if (isInFocusLockMode) {
                                Log.d(TAG, "💡 屏幕点亮，准备监控解锁")
                                handleScreenOn()
                            }
                        }
                    }
                }
            }
            
            val intentFilter = IntentFilter().apply {
                addAction(Intent.ACTION_USER_PRESENT)
                addAction(Intent.ACTION_SCREEN_OFF)
                addAction(Intent.ACTION_SCREEN_ON)
            }
            
            activity.registerReceiver(lockScreenReceiver, intentFilter)
            Log.d(TAG, "✅ 锁屏状态监听器注册成功")
            true
        } catch (e: Exception) {
            Log.e(TAG, "❌ 注册锁屏状态监听器失败: ${e.message}")
            false
        }
    }
    
    /**
     * 注销锁屏状态监听器
     */
    private fun unregisterLockScreenStateListener() {
        try {
            lockScreenReceiver?.let { receiver ->
                activity.unregisterReceiver(receiver)
                lockScreenReceiver = null
                Log.d(TAG, "✅ 锁屏状态监听器已注销")
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ 注销锁屏状态监听器失败: ${e.message}")
        }
    }
    
    /**
     * 处理用户解锁事件
     */
    private fun handleUserUnlock() {
        Log.d(TAG, "🔓 处理用户解锁事件")
        
        // 延迟执行拦截，确保解锁动画完成
        Handler(Looper.getMainLooper()).postDelayed({
            try {
                Log.d(TAG, "🚀 执行解锁拦截")
                
                // 1. 将应用带到前台
                bringAppToForeground()
                
                // 2. 执行解锁拦截回调
                unlockInterceptionCallback?.invoke()
                
                Log.d(TAG, "✅ 解锁拦截执行完成")
            } catch (e: Exception) {
                Log.e(TAG, "❌ 解锁拦截执行失败: ${e.message}")
            }
        }, UNLOCK_INTERCEPTION_DELAY)
    }
    
    /**
     * 处理屏幕关闭事件
     */
    private fun handleScreenOff() {
        Log.d(TAG, "📱 处理屏幕关闭事件")
        // 屏幕关闭时保持专注状态，无需特殊处理
        // 可以在这里添加统计或日志记录
    }
    
    /**
     * 处理屏幕点亮事件
     */
    private fun handleScreenOn() {
        Log.d(TAG, "💡 处理屏幕点亮事件")
        // 屏幕点亮时准备监控解锁，无需特殊处理
        // 可以在这里添加预处理逻辑
    }
    
    /**
     * 将应用带到前台
     */
    private fun bringAppToForeground() {
        try {
            val intent = Intent(activity, MainActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or 
                       Intent.FLAG_ACTIVITY_CLEAR_TOP or
                       Intent.FLAG_ACTIVITY_SINGLE_TOP
                putExtra("focus_mode_active", true)
                putExtra("unlock_intercepted", true)
            }
            
            activity.startActivity(intent)
            Log.d(TAG, "✅ 应用已带到前台")
        } catch (e: Exception) {
            Log.e(TAG, "❌ 将应用带到前台失败: ${e.message}")
        }
    }
    
    /**
     * 检查是否处于专注锁屏模式
     */
    fun isInFocusLockMode(): Boolean = isInFocusLockMode
    
    /**
     * 强制激活系统锁屏
     * 用于手动触发锁屏
     */
    fun forceLockScreen(): Boolean {
        return if (isInFocusLockMode) {
            deviceAdminKioskManager.forceLockScreen()
            true
        } else {
            Log.w(TAG, "⚠️ 未处于专注锁屏模式，无法强制锁屏")
            false
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            Log.d(TAG, "🧹 清理FocusSystemLockManager资源")
            disableFocusSystemLock()
        } catch (e: Exception) {
            Log.e(TAG, "❌ 清理资源失败: ${e.message}")
        }
    }
}
