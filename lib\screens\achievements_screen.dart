import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../utils/constants.dart';
import '../models/achievement.dart';
import '../providers/achievement_provider.dart';

class AchievementsScreen extends StatefulWidget {
  const AchievementsScreen({super.key});

  @override
  State<AchievementsScreen> createState() => _AchievementsScreenState();
}

class _AchievementsScreenState extends State<AchievementsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Load achievements when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AchievementProvider>().loadAchievements();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: <PERSON><PERSON><PERSON>(
          child: Column(
            children: [
              // Header
              _buildHeader(),

              // Stats overview
              _buildStatsOverview(),

              // Tab bar
              _buildTabBar(),

              // Tab content
              Expanded(
                child: _buildTabContent(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(
              Icons.arrow_back,
              color: AppColors.onSurface,
            ),
          ),
          const SizedBox(width: AppDimensions.paddingMedium),
          const Text(
            '🏆 成就系统',
            style: AppTextStyles.titleMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildStatsOverview() {
    return Consumer<AchievementProvider>(
      builder: (context, achievementProvider, child) {
        final stats = achievementProvider.getAchievementStats();

        return Container(
          margin: const EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingLarge),
          padding: const EdgeInsets.all(AppDimensions.paddingLarge),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
            border: Border.all(
              color: AppColors.primary.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              // Progress circle
              SizedBox(
                width: 80,
                height: 80,
                child: Stack(
                  children: [
                    CircularProgressIndicator(
                      value: achievementProvider.completionPercentage,
                      strokeWidth: 6,
                      backgroundColor: AppColors.onSurface.withOpacity(0.1),
                      valueColor: const AlwaysStoppedAnimation<Color>(
                          AppColors.primary),
                    ),
                    Center(
                      child: Text(
                        '${(achievementProvider.completionPercentage * 100).round()}%',
                        style: AppTextStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppColors.primary,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(width: AppDimensions.paddingLarge),

              // Stats text
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '已解锁 ${achievementProvider.unlockedCount}/${achievementProvider.totalAchievements} 个成就',
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '最近7天解锁 ${stats['recent']} 个',
                      style: AppTextStyles.bodySmall,
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        ),
        labelColor: Colors.black,
        unselectedLabelColor: AppColors.onSurface,
        labelStyle: AppTextStyles.bodyMedium.copyWith(
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: AppTextStyles.bodyMedium,
        tabs: const [
          Tab(text: '已解锁'),
          Tab(text: '未解锁'),
        ],
      ),
    );
  }

  Widget _buildTabContent() {
    return Consumer<AchievementProvider>(
      builder: (context, achievementProvider, child) {
        return TabBarView(
          controller: _tabController,
          children: [
            _buildAchievementList(
                achievementProvider.unlockedAchievements, true),
            _buildAchievementList(
                achievementProvider.lockedAchievements, false),
          ],
        );
      },
    );
  }

  Widget _buildAchievementList(
      List<Achievement> achievements, bool isUnlocked) {
    if (achievements.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              isUnlocked ? '🎯' : '🔒',
              style: const TextStyle(fontSize: 48),
            ),
            const SizedBox(height: AppDimensions.paddingMedium),
            Text(
              isUnlocked ? '还没有解锁任何成就' : '所有成就都已解锁！',
              style: AppTextStyles.bodyMedium,
            ),
            const SizedBox(height: AppDimensions.paddingSmall),
            Text(
              isUnlocked ? '开始专注来解锁你的第一个成就吧' : '恭喜你成为专注大师！',
              style: AppTextStyles.bodySmall,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      itemCount: achievements.length,
      itemBuilder: (context, index) {
        final achievement = achievements[index];
        return _buildAchievementCard(achievement, isUnlocked);
      },
    );
  }

  Widget _buildAchievementCard(Achievement achievement, bool isUnlocked) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.paddingMedium),
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: BoxDecoration(
        color:
            isUnlocked ? AppColors.primary.withOpacity(0.1) : AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        border: Border.all(
          color: isUnlocked
              ? AppColors.primary.withOpacity(0.3)
              : AppColors.onSurface.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Icon
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: isUnlocked
                  ? AppColors.primary.withOpacity(0.2)
                  : AppColors.onSurface.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
            ),
            child: Center(
              child: Text(
                achievement.icon,
                style: TextStyle(
                  fontSize: 28,
                  color:
                      isUnlocked ? null : AppColors.onSurface.withOpacity(0.5),
                ),
              ),
            ),
          ),

          const SizedBox(width: AppDimensions.paddingMedium),

          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  achievement.title,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: isUnlocked ? AppColors.primary : AppColors.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  achievement.description,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: isUnlocked
                        ? AppColors.onSurface
                        : AppColors.onSurfaceVariant,
                  ),
                ),
                if (!isUnlocked) ...[
                  const SizedBox(height: AppDimensions.paddingSmall),
                  Consumer<AchievementProvider>(
                    builder: (context, achievementProvider, child) {
                      return FutureBuilder<double>(
                        future: achievementProvider
                            .getAchievementProgress(achievement),
                        builder: (context, snapshot) {
                          final progress = snapshot.data ?? 0.0;
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              LinearProgressIndicator(
                                value: progress,
                                backgroundColor:
                                    AppColors.onSurface.withOpacity(0.1),
                                valueColor: const AlwaysStoppedAnimation<Color>(
                                    AppColors.primary),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                '进度: ${(progress * 100).round()}%',
                                style: AppTextStyles.bodySmall.copyWith(
                                  color: AppColors.primary,
                                ),
                              ),
                            ],
                          );
                        },
                      );
                    },
                  ),
                ],
                if (isUnlocked && achievement.unlockedAt != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    '解锁于 ${_formatDate(achievement.unlockedAt!)}',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ],
            ),
          ),

          // Status indicator
          if (isUnlocked)
            const Icon(
              Icons.check_circle,
              color: AppColors.primary,
              size: 24,
            )
          else
            const Icon(
              Icons.lock_outline,
              color: AppColors.onSurfaceVariant,
              size: 24,
            ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return '今天';
    } else if (difference.inDays == 1) {
      return '昨天';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${date.month}月${date.day}日';
    }
  }
}
