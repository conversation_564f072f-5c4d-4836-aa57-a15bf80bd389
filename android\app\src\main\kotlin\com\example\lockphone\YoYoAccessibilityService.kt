package com.example.lockphone

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.AccessibilityServiceInfo
import android.accessibilityservice.GestureDescription
import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.graphics.Path
import android.os.Build
import android.util.Log
import android.view.accessibility.AccessibilityEvent

/**
 * YoYo日常风格的无障碍服务
 * 提供深度锁定功能，阻止用户通过系统手势逃逸
 */
class YoYoAccessibilityService : AccessibilityService() {

    companion object {
        private const val TAG = "YoYoAccessibilityService"

        @Volatile
        private var instance: YoYoAccessibilityService? = null

        @Volatile
        private var isLockModeActive = false

        @Volatile
        private var isServiceReady = false

        @Volatile
        private var isBottomSwipeBlocked = false
        private var isNavigationGestureBlocked = false
        private var isStatusBarGestureBlocked = false

        /**
         * 获取服务实例
         */
        fun getInstance(): YoYoAccessibilityService? = instance

        /**
         * 启用锁定模式
         */
        fun enableLockMode() {
            isLockModeActive = true
            if (isServiceReady) {
                instance?.configureServiceForLockMode()
            }
            Log.d(TAG, "锁定模式已启用")
        }

        /**
         * 禁用锁定模式
         */
        fun disableLockMode() {
            isLockModeActive = false
            if (isServiceReady) {
                instance?.configureServiceForNormalMode()
            }
            Log.d(TAG, "锁定模式已禁用")
        }

        /**
         * 检查锁定模式是否激活
         */
        fun isLockModeActive(): Boolean = isLockModeActive

        /**
         * 检查服务是否运行
         */
        fun isServiceRunning(): Boolean = instance != null

        /**
         * 检查服务是否准备就绪
         */
        fun isServiceReady(): Boolean = isServiceReady
    }

    override fun onServiceConnected() {
        super.onServiceConnected()
        instance = this

        Log.d(TAG, "🚀 YoYo无障碍服务已连接")

        // 初始配置 - 启用时使用最小权限，不影响正常使用
        configureServiceForNormalMode()

        // 延迟标记服务为准备就绪，给用户时间返回应用
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            isServiceReady = true
            Log.d(TAG, "✅ YoYo无障碍服务准备就绪")

            // 如果锁定模式已经被请求启用，现在配置它
            if (isLockModeActive) {
                configureServiceForLockMode()
                Log.d(TAG, "🔒 延迟配置锁定模式")
            }

            // 立即测试手势阻止功能
            testGestureBlocking()
        }, 3000) // 3秒延迟

        Log.d(TAG, "YoYo无障碍服务已连接 - 正常模式")
    }

    /**
     * 配置服务为正常模式（最小权限）
     */
    private fun configureServiceForNormalMode() {
        val info = AccessibilityServiceInfo().apply {
            // 最小事件监听
            eventTypes = AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED

            // 最小反馈
            feedbackType = AccessibilityServiceInfo.FEEDBACK_GENERIC

            // 最小标志 - 不请求触摸探索等可能影响导航的功能
            flags = AccessibilityServiceInfo.FLAG_REPORT_VIEW_IDS

            // 不延迟
            notificationTimeout = 0

            // 只监控自己的包
            packageNames = arrayOf(packageName)
        }

        serviceInfo = info
        Log.d(TAG, "服务配置为正常模式")
    }

    /**
     * 配置服务为锁定模式（完整权限）
     */
    private fun configureServiceForLockMode() {
        val info = AccessibilityServiceInfo().apply {
            // 监听的事件类型 - 添加更多事件类型来拦截手势
            eventTypes = AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED or
                        AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED or
                        AccessibilityEvent.TYPE_VIEW_CLICKED or
                        AccessibilityEvent.TYPE_GESTURE_DETECTION_START or
                        AccessibilityEvent.TYPE_GESTURE_DETECTION_END or
                        AccessibilityEvent.TYPE_TOUCH_EXPLORATION_GESTURE_START or
                        AccessibilityEvent.TYPE_TOUCH_EXPLORATION_GESTURE_END

            // 反馈类型
            feedbackType = AccessibilityServiceInfo.FEEDBACK_GENERIC or
                          AccessibilityServiceInfo.FEEDBACK_HAPTIC

            // 标志 - 添加手势拦截相关标志
            flags = AccessibilityServiceInfo.FLAG_REPORT_VIEW_IDS or
                   AccessibilityServiceInfo.FLAG_RETRIEVE_INTERACTIVE_WINDOWS or
                   AccessibilityServiceInfo.FLAG_REQUEST_TOUCH_EXPLORATION_MODE or
                   AccessibilityServiceInfo.FLAG_REQUEST_ENHANCED_WEB_ACCESSIBILITY or
                   AccessibilityServiceInfo.FLAG_REQUEST_FILTER_KEY_EVENTS

            // 不延迟
            notificationTimeout = 0

            // 设置包名过滤 - 监控系统UI
            packageNames = arrayOf("com.android.systemui", packageName)
        }

        serviceInfo = info
        Log.d(TAG, "服务配置为锁定模式")
    }

    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        if (!isLockModeActive || event == null) {
            return
        }

        when (event.eventType) {
            AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED -> {
                handleWindowStateChanged(event)
            }
            AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED -> {
                handleWindowContentChanged(event)
            }
            AccessibilityEvent.TYPE_VIEW_CLICKED -> {
                handleViewClicked(event)
            }
            AccessibilityEvent.TYPE_GESTURE_DETECTION_START -> {
                handleGestureStart(event)
            }
            AccessibilityEvent.TYPE_GESTURE_DETECTION_END -> {
                handleGestureEnd(event)
            }
            AccessibilityEvent.TYPE_TOUCH_EXPLORATION_GESTURE_START -> {
                handleTouchGestureStart(event)
            }
            AccessibilityEvent.TYPE_TOUCH_EXPLORATION_GESTURE_END -> {
                handleTouchGestureEnd(event)
            }
            AccessibilityEvent.TYPE_TOUCH_INTERACTION_START -> {
                handleTouchInteractionStart(event)
            }
            AccessibilityEvent.TYPE_TOUCH_INTERACTION_END -> {
                handleTouchInteractionEnd(event)
            }
        }
    }

    /**
     * 处理窗口状态变化
     */
    private fun handleWindowStateChanged(event: AccessibilityEvent) {
        val packageName = event.packageName?.toString()
        val className = event.className?.toString()

        Log.d(TAG, "🔍 窗口状态变化: package=$packageName, class=$className")

        // 检查是否切换到了其他应用
        if (packageName != null && packageName != this.packageName) {
            Log.w(TAG, "🚨 检测到应用切换到: $packageName，立即强制返回")

            // 特别检查是否是桌面应用
            if (isLauncherApp(packageName)) {
                Log.w(TAG, "🏠 检测到用户返回桌面，立即阻止")
            }

            // 立即强制返回专注应用
            forceReturnToFocusApp()
        }

        // 检查是否是系统UI相关的窗口变化
        if (packageName == "com.android.systemui") {
            Log.w(TAG, "🚫 检测到系统UI变化，可能是手势操作")
            // 立即强制返回
            forceReturnToFocusApp()
        }
    }

    /**
     * 检查是否是桌面应用
     */
    private fun isLauncherApp(packageName: String): Boolean {
        return packageName.contains("launcher") ||
               packageName.contains("home") ||
               packageName == "com.android.launcher3" ||
               packageName == "com.miui.home" ||
               packageName == "com.huawei.android.launcher" ||
               packageName == "com.oppo.launcher"
    }

    /**
     * 处理窗口内容变化
     */
    private fun handleWindowContentChanged(event: AccessibilityEvent) {
        // 可以在这里添加更细粒度的内容监控
        // 例如检测系统设置页面的打开等
    }

    /**
     * 处理视图点击
     */
    private fun handleViewClicked(event: AccessibilityEvent) {
        // 可以在这里监控特定的系统按钮点击
        // 例如最近任务按钮、设置按钮等
        val packageName = event.packageName?.toString()
        if (packageName == "com.android.systemui") {
            Log.w(TAG, "检测到系统UI点击，可能是导航按钮")
            // 可以在这里阻止某些系统按钮的点击
        }
    }

    /**
     * 处理手势开始
     */
    private fun handleGestureStart(event: AccessibilityEvent) {
        Log.w(TAG, "检测到手势开始: ${event.packageName}")
        // 在锁定模式下阻止手势
        if (isLockModeActive) {
            Log.w(TAG, "锁定模式下阻止手势")
            // 尝试返回到专注应用
            returnToFocusApp()
        }
    }

    /**
     * 处理手势结束
     */
    private fun handleGestureEnd(event: AccessibilityEvent) {
        Log.w(TAG, "检测到手势结束: ${event.packageName}")
    }

    /**
     * 处理触摸手势开始
     */
    private fun handleTouchGestureStart(event: AccessibilityEvent) {
        Log.w(TAG, "检测到触摸手势开始: ${event.packageName}")
        if (isLockModeActive) {
            // 强制返回专注应用
            returnToFocusApp()
        }
    }

    /**
     * 处理触摸手势结束
     */
    private fun handleTouchGestureEnd(event: AccessibilityEvent) {
        Log.w(TAG, "检测到触摸手势结束: ${event.packageName}")
    }

    /**
     * 处理触摸交互开始
     */
    private fun handleTouchInteractionStart(event: AccessibilityEvent) {
        Log.w(TAG, "🚫 检测到触摸交互开始，立即阻止: ${event.packageName}")
        if (isLockModeActive) {
            // 立即强制返回专注应用
            executeImmediateReturn()
        }
    }

    /**
     * 处理触摸交互结束
     */
    private fun handleTouchInteractionEnd(event: AccessibilityEvent) {
        Log.w(TAG, "🚫 检测到触摸交互结束，确保锁定: ${event.packageName}")
        if (isLockModeActive) {
            // 确保仍在专注应用中
            returnToFocusApp()
        }
    }

    /**
     * 返回到专注应用
     */
    private fun returnToFocusApp() {
        forceReturnToFocusApp()
    }

    /**
     * 强制返回到专注应用 - 增强版
     */
    private fun forceReturnToFocusApp() {
        try {
            Log.w(TAG, "🚨 强制返回专注应用 - 增强模式")

            // 方法1: 立即启动专注应用 (最高优先级)
            val intent = packageManager.getLaunchIntentForPackage(packageName)
            intent?.let {
                it.addFlags(
                    Intent.FLAG_ACTIVITY_NEW_TASK or
                    Intent.FLAG_ACTIVITY_CLEAR_TOP or
                    Intent.FLAG_ACTIVITY_SINGLE_TOP or
                    Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT or
                    Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED or
                    Intent.FLAG_ACTIVITY_CLEAR_TASK
                )
                startActivity(it)
                Log.d(TAG, "✅ 已强制启动专注应用")
            }

            // 方法2: 连续多次尝试启动 (确保成功)
            for (i in 1..3) {
                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                    try {
                        val retryIntent = packageManager.getLaunchIntentForPackage(packageName)
                        retryIntent?.let {
                            it.addFlags(
                                Intent.FLAG_ACTIVITY_NEW_TASK or
                                Intent.FLAG_ACTIVITY_CLEAR_TOP or
                                Intent.FLAG_ACTIVITY_SINGLE_TOP
                            )
                            startActivity(it)
                            Log.d(TAG, "🔄 第${i}次重试启动成功")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "第${i}次重试启动失败: ${e.message}")
                    }
                }, (i * 200).toLong()) // 200ms, 400ms, 600ms间隔
            }

            // 方法3: 使用全局动作阻止其他操作
            try {
                // 先执行Home动作，然后立即启动我们的应用
                performGlobalAction(GLOBAL_ACTION_HOME)

                // 立即启动我们的应用覆盖Home
                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                    val immediateIntent = packageManager.getLaunchIntentForPackage(packageName)
                    immediateIntent?.let {
                        it.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        startActivity(it)
                        Log.d(TAG, "🏠 覆盖Home操作成功")
                    }
                }, 50) // 50ms后立即启动

            } catch (e: Exception) {
                Log.e(TAG, "全局动作失败: ${e.message}")
            }

        } catch (e: Exception) {
            Log.e(TAG, "强制返回专注应用失败: ${e.message}")
        }
    }

    /**
     * 阻止系统手势 - 增强版
     */
    fun blockSystemGestures() {
        try {
            Log.d(TAG, "🚫 启用增强系统手势阻止")

            // 1. 阻止Home键和最近任务键
            blockNavigationGestures()

            // 2. 阻止状态栏下拉
            blockStatusBarGestures()

            // 3. 阻止底部上划手势
            blockBottomSwipeGestures()

            // 4. 启动手势监控
            startGestureMonitoring()

            Log.d(TAG, "✅ 系统手势阻止已全面激活")
        } catch (e: Exception) {
            Log.e(TAG, "阻止系统手势失败: ${e.message}")
        }
    }

    /**
     * 阻止导航手势
     */
    private fun blockNavigationGestures() {
        try {
            Log.d(TAG, "🚫 启用导航手势阻止")

            // 1. 阻止Home键和最近任务键
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                // Android 9+ 使用手势导航拦截
                performGlobalAction(GLOBAL_ACTION_BACK) // 预先消费Back事件
            }

            // 2. 设置窗口状态监控
            isNavigationGestureBlocked = true

            // 3. 启动导航手势监控线程
            startNavigationGestureMonitoring()

            Log.d(TAG, "✅ 导航手势阻止已激活")
        } catch (e: Exception) {
            Log.e(TAG, "❌ 阻止导航手势失败: ${e.message}")
        }
    }

    /**
     * 阻止状态栏手势
     */
    private fun blockStatusBarGestures() {
        try {
            Log.d(TAG, "🚫 启用状态栏手势阻止")

            // 1. 监控状态栏下拉手势
            isStatusBarGestureBlocked = true

            // 2. 阻止通知面板展开
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                // 尝试折叠状态栏
                try {
                    val statusBarService = getSystemService(Context.STATUS_BAR_SERVICE)
                    val statusBarManager = statusBarService as? Any
                    statusBarManager?.javaClass?.getMethod("collapsePanels")?.invoke(statusBarManager)
                } catch (e: Exception) {
                    Log.w(TAG, "无法直接控制状态栏: ${e.message}")
                }
            }

            // 3. 启动状态栏监控
            startStatusBarMonitoring()

            Log.d(TAG, "✅ 状态栏手势阻止已激活")
        } catch (e: Exception) {
            Log.e(TAG, "❌ 阻止状态栏手势失败: ${e.message}")
        }
    }

    /**
     * 阻止底部上划手势 - 增强版
     */
    private fun blockBottomSwipeGestures() {
        try {
            Log.d(TAG, "🚫 启用底部上划手势阻止")

            // 1. 设置手势拦截标志
            isBottomSwipeBlocked = true

            // 2. 启用手势拦截服务
            enableGestureInterception()

            // 3. 启动底部区域监控
            startBottomZoneMonitoring()

            // 4. 设置手势拦截回调
            setupGestureInterceptionCallback()

            Log.d(TAG, "✅ 底部上划手势阻止已激活")
        } catch (e: Exception) {
            Log.e(TAG, "❌ 阻止底部上划手势失败: ${e.message}")
        }
    }

    /**
     * 启用手势拦截服务 - 增强版
     */
    private fun enableGestureInterception() {
        try {
            // 设置最强的手势拦截标志
            val serviceInfo = AccessibilityServiceInfo()
            serviceInfo.eventTypes = AccessibilityEvent.TYPES_ALL_MASK
            serviceInfo.feedbackType = AccessibilityServiceInfo.FEEDBACK_GENERIC or
                    AccessibilityServiceInfo.FEEDBACK_HAPTIC or
                    AccessibilityServiceInfo.FEEDBACK_AUDIBLE

            serviceInfo.flags = AccessibilityServiceInfo.FLAG_INCLUDE_NOT_IMPORTANT_VIEWS or
                    AccessibilityServiceInfo.FLAG_REQUEST_TOUCH_EXPLORATION_MODE or
                    AccessibilityServiceInfo.FLAG_REQUEST_ENHANCED_WEB_ACCESSIBILITY or
                    AccessibilityServiceInfo.FLAG_REPORT_VIEW_IDS or
                    AccessibilityServiceInfo.FLAG_REQUEST_FILTER_KEY_EVENTS or
                    AccessibilityServiceInfo.FLAG_RETRIEVE_INTERACTIVE_WINDOWS or
                    AccessibilityServiceInfo.FLAG_ENABLE_ACCESSIBILITY_VOLUME

            // 设置包名过滤，监控所有应用
            serviceInfo.packageNames = null // 监控所有包

            // 设置节点超时
            serviceInfo.notificationTimeout = 0

            this.serviceInfo = serviceInfo
            Log.d(TAG, "增强手势拦截服务已启用")
        } catch (e: Exception) {
            Log.e(TAG, "启用增强手势拦截服务失败: ${e.message}")
        }
    }

    /**
     * 启动底部区域监控
     */
    private fun startBottomZoneMonitoring() {
        try {
            val handler = android.os.Handler(android.os.Looper.getMainLooper())
            val runnable = object : Runnable {
                override fun run() {
                    if (isLockModeActive && isBottomSwipeBlocked) {
                        // 检查是否有底部手势尝试
                        checkBottomGestureAttempt()

                        // 继续监控
                        handler.postDelayed(this, 100) // 每100ms检查一次，更频繁
                    }
                }
            }
            handler.post(runnable)

            Log.d(TAG, "底部区域监控已启动")
        } catch (e: Exception) {
            Log.e(TAG, "启动底部区域监控失败: ${e.message}")
        }
    }

    /**
     * 检查底部手势尝试
     */
    private fun checkBottomGestureAttempt() {
        try {
            // 检查当前前台应用
            val currentApp = getCurrentForegroundApp()

            // 如果不是我们的应用，立即强制返回
            if (currentApp != null && currentApp != packageName) {
                Log.w(TAG, "🚨 检测到底部手势导致的应用切换: $currentApp")

                // 立即执行多重返回策略
                executeImmediateReturn()
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查底部手势尝试失败: ${e.message}")
        }
    }

    /**
     * 执行立即返回策略
     */
    fun executeImmediateReturn() {
        try {
            Log.w(TAG, "🔄 执行立即返回策略")

            // 策略1: 立即执行返回操作
            forceReturnToFocusApp()

            // 策略2: 100ms后再次确认
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                val currentApp = getCurrentForegroundApp()
                if (currentApp != packageName) {
                    Log.w(TAG, "🔄 第二次强制返回")
                    forceReturnToFocusApp()
                }
            }, 100)

            // 策略3: 300ms后最终确认
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                val currentApp = getCurrentForegroundApp()
                if (currentApp != packageName) {
                    Log.w(TAG, "🔄 最终强制返回")
                    forceReturnToFocusApp()
                }
            }, 300)

        } catch (e: Exception) {
            Log.e(TAG, "执行立即返回策略失败: ${e.message}")
        }
    }

    /**
     * 获取当前前台应用
     */
    private fun getCurrentForegroundApp(): String? {
        try {
            val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val runningTasks = activityManager.getRunningTasks(1)

            if (runningTasks.isNotEmpty()) {
                return runningTasks[0].topActivity?.packageName
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取前台应用失败: ${e.message}")
        }
        return null
    }

    /**
     * 启动手势监控
     */
    private fun startGestureMonitoring() {
        try {
            // 启动定期检查，确保手势阻止持续有效
            val handler = android.os.Handler(android.os.Looper.getMainLooper())
            val runnable = object : Runnable {
                override fun run() {
                    if (isLockModeActive) {
                        // 检查当前前台应用
                        checkForegroundApp()

                        // 继续监控
                        handler.postDelayed(this, 1000) // 每秒检查一次
                    }
                }
            }
            handler.post(runnable)

            Log.d(TAG, "手势监控已启动")
        } catch (e: Exception) {
            Log.e(TAG, "启动手势监控失败: ${e.message}")
        }
    }

    /**
     * 检查前台应用
     */
    private fun checkForegroundApp() {
        try {
            // 如果检测到用户离开了我们的应用，立即强制返回
            val currentPackage = getCurrentForegroundPackage()
            if (currentPackage != null && currentPackage != packageName) {
                Log.w(TAG, "🚨 检测到用户离开应用: $currentPackage，强制返回")
                forceReturnToFocusApp()
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查前台应用失败: ${e.message}")
        }
    }

    /**
     * 获取当前前台应用包名
     */
    private fun getCurrentForegroundPackage(): String? {
        return try {
            val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val runningTasks = activityManager.getRunningTasks(1)
            if (runningTasks.isNotEmpty()) {
                runningTasks[0].topActivity?.packageName
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取前台应用失败: ${e.message}")
            null
        }
    }

    /**
     * 允许系统手势
     */
    fun allowSystemGestures() {
        try {
            // 恢复正常的手势处理
            Log.d(TAG, "系统手势阻止已解除")
        } catch (e: Exception) {
            Log.e(TAG, "恢复系统手势失败: ${e.message}")
        }
    }

    override fun onInterrupt() {
        Log.w(TAG, "YoYo无障碍服务被中断")
    }

    override fun onDestroy() {
        super.onDestroy()
        instance = null
        isLockModeActive = false
        Log.d(TAG, "YoYo无障碍服务已销毁")
    }

    /**
     * 处理服务启动命令
     */
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "YoYo无障碍服务启动命令")
        return START_STICKY // 服务被杀死后自动重启
    }

    /**
     * 检查服务是否正常工作
     */
    fun isServiceWorking(): Boolean {
        return try {
            // 简单的健康检查
            serviceInfo != null && instance != null
        } catch (e: Exception) {
            Log.e(TAG, "服务健康检查失败: ${e.message}")
            false
        }
    }

    /**
     * 获取服务状态信息
     */
    fun getServiceStatus(): Map<String, Any> {
        return mapOf(
            "isConnected" to (instance != null),
            "isLockModeActive" to isLockModeActive,
            "isServiceReady" to isServiceReady,
            "packageName" to packageName,
            "serviceInfo" to (serviceInfo != null)
        )
    }

    /**
     * 启动导航手势监控
     */
    private fun startNavigationGestureMonitoring() {
        // 监控窗口变化，检测导航手势
        Log.d(TAG, "🔍 启动导航手势监控")
    }

    /**
     * 启动状态栏监控
     */
    private fun startStatusBarMonitoring() {
        // 监控状态栏相关事件
        Log.d(TAG, "🔍 启动状态栏监控")
    }



    /**
     * 设置手势拦截回调
     */
    private fun setupGestureInterceptionCallback() {
        // 设置手势拦截的回调处理
        Log.d(TAG, "🔧 设置手势拦截回调")
    }



    /**
     * 创建手势拦截描述
     */
    private fun createGestureInterceptionDescription(): GestureDescription? {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                // 创建一个空的手势描述来占用手势系统
                val path = Path()
                path.moveTo(0f, 0f)
                path.lineTo(1f, 1f)

                val stroke = GestureDescription.StrokeDescription(path, 0, 1)
                GestureDescription.Builder()
                    .addStroke(stroke)
                    .build()
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "创建手势拦截描述失败: ${e.message}")
            null
        }
    }

    /**
     * 启用全局手势监听
     */
    private fun enableGlobalGestureMonitoring() {
        Log.d(TAG, "🌐 启用全局手势监听")
        // 这里可以添加更多的全局手势监听逻辑
    }

    /**
     * 测试手势阻止功能
     */
    private fun testGestureBlocking() {
        Log.d(TAG, "🧪 测试手势阻止功能")

        try {
            // 测试服务是否正常工作
            Log.d(TAG, "   - 服务实例: ${if (instance != null) "✅ 正常" else "❌ 异常"}")
            Log.d(TAG, "   - 服务准备: ${if (isServiceReady) "✅ 就绪" else "❌ 未就绪"}")
            Log.d(TAG, "   - 锁定模式: ${if (isLockModeActive) "✅ 激活" else "❌ 未激活"}")

            // 测试全局动作是否可用
            val globalActions = listOf(
                GLOBAL_ACTION_BACK,
                GLOBAL_ACTION_HOME,
                GLOBAL_ACTION_RECENTS
            )

            for (action in globalActions) {
                val actionName = when (action) {
                    GLOBAL_ACTION_BACK -> "返回"
                    GLOBAL_ACTION_HOME -> "主页"
                    GLOBAL_ACTION_RECENTS -> "最近任务"
                    else -> "未知"
                }
                Log.d(TAG, "   - 全局动作[$actionName]: 可用")
            }

            Log.d(TAG, "✅ 手势阻止功能测试完成")
        } catch (e: Exception) {
            Log.e(TAG, "❌ 手势阻止功能测试失败: ${e.message}")
        }
    }

    override fun onUnbind(intent: Intent?): Boolean {
        Log.d(TAG, "YoYo无障碍服务断开连接")
        instance = null
        isServiceReady = false
        isLockModeActive = false
        isNavigationGestureBlocked = false
        isStatusBarGestureBlocked = false
        isBottomSwipeBlocked = false
        return super.onUnbind(intent)
    }
}
