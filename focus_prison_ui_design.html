<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专注监狱APP - UI/UX设计方案</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
            color: #fff;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        h1 {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 30px;
            background: linear-gradient(45deg, #00ffff, #ff00ff);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
        }
        
        .design-principles {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .mockups-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }
        
        .phone-mockup {
            background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
            border-radius: 30px;
            padding: 20px;
            position: relative;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
            border: 2px solid rgba(0, 255, 255, 0.2);
        }
        
        .phone-screen {
            background: #000;
            border-radius: 25px;
            padding: 20px;
            height: 600px;
            position: relative;
            overflow: hidden;
            border: 3px solid #333;
        }
        
        .screen-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .app-title {
            font-size: 1.5em;
            font-weight: 700;
            color: #00ffff;
            text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
        }
        
        .prison-icon {
            font-size: 1.5em;
            color: #ff6b6b;
        }
        
        .main-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            height: 100%;
            justify-content: center;
        }
        
        .big-timer {
            font-size: 4em;
            font-weight: 900;
            color: #00ffff;
            text-shadow: 0 0 30px rgba(0, 255, 255, 0.8);
            margin-bottom: 20px;
            font-family: 'Orbitron', monospace;
        }
        
        .progress-bar {
            width: 80%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            margin-bottom: 30px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00ffff, #ff00ff);
            border-radius: 4px;
            animation: pulse 2s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .status-text {
            font-size: 1.2em;
            color: #fff;
            margin-bottom: 10px;
            text-align: center;
        }
        
        .motivation-text {
            font-size: 1em;
            color: #888;
            text-align: center;
            font-style: italic;
            margin-bottom: 20px;
        }
        
        .stats-text {
            font-size: 0.9em;
            color: #666;
            text-align: center;
        }
        
        .button-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            width: 100%;
            margin-top: 30px;
        }
        
        .task-button {
            background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
            border: 2px solid rgba(0, 255, 255, 0.3);
            border-radius: 15px;
            padding: 20px;
            color: #fff;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9em;
        }
        
        .task-button:hover {
            background: linear-gradient(145deg, #3a3a3a, #2a2a2a);
            border-color: rgba(0, 255, 255, 0.6);
            transform: translateY(-2px);
        }
        
        .task-icon {
            font-size: 1.5em;
            margin-bottom: 10px;
            display: block;
        }
        
        .primary-button {
            background: linear-gradient(45deg, #00ffff, #ff00ff);
            border: none;
            border-radius: 25px;
            padding: 15px 30px;
            color: #000;
            font-weight: 700;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-top: 20px;
        }
        
        .primary-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 255, 255, 0.3);
        }
        
        .time-selector {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
            gap: 10px;
        }
        
        .time-option {
            flex: 1;
            padding: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            text-align: center;
            color: #fff;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9em;
        }
        
        .time-option.selected {
            background: rgba(0, 255, 255, 0.2);
            border-color: #00ffff;
        }
        
        .emergency-hint {
            position: absolute;
            top: 10px;
            left: 10px;
            width: 40px;
            height: 40px;
            background: rgba(255, 0, 0, 0.1);
            border-radius: 50%;
            opacity: 0;
            animation: hint-pulse 3s ease-in-out infinite 5s;
        }
        
        @keyframes hint-pulse {
            0%, 90%, 100% { opacity: 0; }
            5%, 85% { opacity: 0.3; }
        }
        
        .screen-title {
            font-size: 1.2em;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
            color: #00ffff;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: 700;
            color: #00ffff;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9em;
            color: #888;
        }
        
        .feature-list {
            list-style: none;
            margin: 20px 0;
        }
        
        .feature-list li {
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            color: #ccc;
        }
        
        .feature-list li:before {
            content: "✓";
            color: #00ffff;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .design-section {
            margin-bottom: 40px;
        }
        
        .design-section h3 {
            color: #00ffff;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        
        .color-palette {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .color-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .color-swatch {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }
        
        .typography-example {
            margin: 20px 0;
        }
        
        .typography-example h4 {
            color: #00ffff;
            margin-bottom: 10px;
        }
        
        .mockup-title {
            text-align: center;
            margin-bottom: 20px;
            font-size: 1.3em;
            color: #00ffff;
        }
        
        .floating-action {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #00ffff, #ff00ff);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 10px 30px rgba(0, 255, 255, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .floating-action:hover {
            transform: scale(1.1);
            box-shadow: 0 15px 40px rgba(0, 255, 255, 0.5);
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 2000;
            align-items: center;
            justify-content: center;
        }
        
        .modal-content {
            background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
            border-radius: 20px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            border: 1px solid rgba(0, 255, 255, 0.3);
        }
        
        .close-modal {
            float: right;
            font-size: 1.5em;
            cursor: pointer;
            color: #888;
        }
        
        .close-modal:hover {
            color: #fff;
        }
        
        @media (max-width: 768px) {
            .mockups-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 10px;
            }
            
            h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 锁手机APP - UI/UX设计方案</h1>
        
        <div class="design-principles">
            <h3>设计原则</h3>
            <ul class="feature-list">
                <li>直观明了 - "锁手机"概念简单易懂</li>
                <li>强制性 - 真正锁住手机，无法逃脱</li>
                <li>极简操作 - 3步即可开始锁定</li>
                <li>安全保障 - 紧急解锁机制</li>
                <li>数据记录 - 完整的锁机历史</li>
            </ul>
        </div>
        
        <div class="design-section">
            <h3>🎨 视觉设计原则</h3>
            
            <ul class="feature-list">
                <li>科技感 - 霓虹蓝色调体现数字化锁定</li>
                <li>极简主义 - 每个界面最多3个主要元素</li>
                <li>强对比 - 黑白灰主色调配霓虹蓝点缀</li>
                <li>大字体 - 倒计时数字占屏幕30%以上</li>
                <li>仪式感 - 强化锁定开始和结束的体验</li>
            </ul>
            
            <div class="typography-example">
                <h4>字体层级</h4>
                <div style="font-size: 2.5em; font-weight: 900; color: #00ffff; margin-bottom: 10px;">大标题 - 时间显示</div>
                <div style="font-size: 1.5em; font-weight: 600; color: #fff; margin-bottom: 10px;">中标题 - 页面标题</div>
                <div style="font-size: 1.2em; color: #fff; margin-bottom: 10px;">正文 - 状态描述</div>
                <div style="font-size: 1em; color: #888;">小字 - 辅助信息</div>
            </div>
            
            <div class="color-palette">
                <div class="color-item">
                    <div class="color-swatch" style="background: #000;"></div>
                    <span>主背景 #000</span>
                </div>
                <div class="color-item">
                    <div class="color-swatch" style="background: #00ffff;"></div>
                    <span>主色调 #00ffff</span>
                </div>
                <div class="color-item">
                    <div class="color-swatch" style="background: #ff00ff;"></div>
                    <span>辅助色 #ff00ff</span>
                </div>
                <div class="color-item">
                    <div class="color-swatch" style="background: #fff;"></div>
                    <span>主文字 #fff</span>
                </div>
                <div class="color-item">
                    <div class="color-swatch" style="background: #888;"></div>
                    <span>辅助文字 #888</span>
                </div>
            </div>
        </div>
        
        <div class="mockups-grid">
            <!-- 主界面 -->
            <div class="phone-mockup">
                <div class="mockup-title">主界面</div>
                <div class="phone-screen">
                    <div class="screen-header">
                        <div class="app-title">🔒 锁手机</div>
                        <div class="prison-icon">⚙️</div>
                    </div>
                    <div class="main-content">
                        <button class="primary-button">🔒 开始锁手机</button>
                        
                        <div class="stats-grid" style="margin-top: 40px;">
                            <div class="stat-card">
                                <div class="stat-value">2</div>
                                <div class="stat-label">今日锁机</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">45</div>
                                <div class="stat-label">分钟</div>
                            </div>
                        </div>
                        
                        <div class="stat-card" style="margin-top: 20px; width: 100%;">
                            <div class="stat-value">3</div>
                            <div class="stat-label">连续锁机天数</div>
                        </div>
                        
                        <div class="button-grid" style="margin-top: 30px;">
                            <div class="task-button">
                                <span class="task-icon">📊</span>
                                <div>我的记录</div>
                            </div>
                            <div class="task-button">
                                <span class="task-icon">🏆</span>
                                <div>成就系统</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 任务创建界面 -->
            <div class="phone-mockup">
                <div class="mockup-title">任务创建</div>
                <div class="phone-screen">
                    <div class="screen-title">🔒 锁手机设置</div>
                    
                    <div style="margin-bottom: 30px;">
                        <h4 style="color: #00ffff; margin-bottom: 15px;">🎯 锁机目标</h4>
                        <div class="button-grid">
                            <div class="task-button">
                                <span class="task-icon">💼</span>
                                <div>工作专注</div>
                            </div>
                            <div class="task-button">
                                <span class="task-icon">📚</span>
                                <div>学习时间</div>
                            </div>
                            <div class="task-button">
                                <span class="task-icon">🧘</span>
                                <div>冥想放松</div>
                            </div>
                            <div class="task-button">
                                <span class="task-icon">🎯</span>
                                <div>深度思考</div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="margin-bottom: 30px;">
                        <h4 style="color: #00ffff; margin-bottom: 15px;">⏰ 锁定时长</h4>
                        <div class="time-selector">
                            <div class="time-option selected">25分钟</div>
                            <div class="time-option">45分钟</div>
                            <div class="time-option">90分钟</div>
                            <div class="time-option">自定义</div>
                        </div>
                    </div>
                    
                    <button class="primary-button" style="width: 100%;">🔒 开始锁定</button>
                </div>
            </div>
            
            <!-- 专注界面 -->
            <div class="phone-mockup">
                <div class="mockup-title">专注状态</div>
                <div class="phone-screen">
                    <div class="emergency-hint"></div>
                    <div class="main-content">
                        <div class="big-timer">24:35</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 65%;"></div>
                        </div>
                        <div class="status-text">手机已锁定</div>
                        <div class="motivation-text">"专注当下，拒绝干扰"</div>
                        <div class="stats-text">锁定中 · 第2次 · 今日45分钟</div>
                    </div>
                </div>
            </div>
            
            <!-- 完成界面 -->
            <div class="phone-mockup">
                <div class="mockup-title">完成庆祝</div>
                <div class="phone-screen">
                    <div class="main-content">
                        <div style="font-size: 3em; margin-bottom: 20px;">🎉</div>
                        <div class="status-text">手机解锁成功！</div>
                        <div class="motivation-text">你已成功保持专注25分钟</div>
                        
                        <div class="stat-card" style="margin: 30px 0; width: 100%;">
                            <div class="stat-value">25:00</div>
                            <div class="stat-label">本次锁定时长</div>
                        </div>
                        
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">3</div>
                                <div class="stat-label">今日完成</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">70</div>
                                <div class="stat-label">总分钟数</div>
                            </div>
                        </div>
                        
                        <div class="button-grid" style="margin-top: 30px;">
                            <div class="task-button">
                                <span class="task-icon">🔄</span>
                                <div>再来一次</div>
                            </div>
                            <div class="task-button">
                                <span class="task-icon">📊</span>
                                <div>查看详情</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 紧急退出界面 -->
            <div class="phone-mockup">
                <div class="mockup-title">紧急退出</div>
                <div class="phone-screen">
                    <div class="main-content">
                        <div style="font-size: 3em; margin-bottom: 20px; color: #ff6b6b;">🚨</div>
                        <div class="status-text" style="color: #ff6b6b;">强制解锁申请</div>
                        <div class="motivation-text">确定要强制解锁手机吗？</div>
                        
                        <div class="stat-card" style="margin: 30px 0; width: 100%; border-color: #ff6b6b;">
                            <div class="stat-value" style="color: #ff6b6b;">15</div>
                            <div class="stat-label">冷却倒计时</div>
                        </div>
                        
                        <div class="motivation-text" style="margin-bottom: 30px;">
                            已完成 18:32 / 25:00<br>
                            再坚持 6 分钟就能获得成就
                        </div>
                        
                        <div class="button-grid">
                            <div class="task-button" style="border-color: #888;">
                                <span class="task-icon">🔒</span>
                                <div>继续锁定</div>
                            </div>
                            <div class="task-button" style="border-color: #ff6b6b;">
                                <span class="task-icon">🔓</span>
                                <div>强制解锁</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 统计界面 -->
            <div class="phone-mockup">
                <div class="mockup-title">专注统计</div>
                <div class="phone-screen">
                    <div class="screen-title">我的锁机记录</div>
                    
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value">12</div>
                            <div class="stat-label">总锁机次数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">5.2</div>
                            <div class="stat-label">小时</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">7</div>
                            <div class="stat-label">连续天数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">85%</div>
                            <div class="stat-label">完成率</div>
                        </div>
                    </div>
                    
                    <div style="margin-top: 30px;">
                        <h4 style="color: #00ffff; margin-bottom: 15px;">🏆 解锁成就</h4>
                        <div class="feature-list">
                            <li>首次锁机 - 迈出第一步</li>
                            <li>连续3天 - 习惯养成</li>
                            <li>单次45分钟 - 深度专注</li>
                            <li>总计5小时 - 自律达人</li>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="design-section">
            <h3>🎯 交互设计要点</h3>
            <ul class="feature-list">
                <li>3-2-1倒计时开始仪式，增强锁定启动的仪式感</li>
                <li>隐藏的强制解锁入口（左上角连击10次）</li>
                <li>15秒冷却期防止冲动解锁</li>
                <li>解锁后的成就展示和数据统计</li>
                <li>渐进式权限申请，每步都有清晰说明</li>
                <li>可视化展示锁机成果和自律进步</li>
            </ul>
        </div>
        
        <div class="design-section">
            <h3>📱 响应式适配</h3>
            <ul class="feature-list">
                <li>支持iPhone 6及以上所有尺寸</li>
                <li>Android 5.0+全面屏适配</li>
                <li>横屏模式的专注界面优化</li>
                <li>动态字体大小调整</li>
            </ul>
        </div>
        
        <div class="design-section">
            <h3>🎨 动效设计</h3>
            <ul class="feature-list">
                <li>倒计时数字的呼吸效果</li>
                <li>进度条的脉冲动画</li>
                <li>页面切换的锁定/解锁动效</li>
                <li>完成时的解锁成功动画</li>
                <li>按钮的霓虹灯光效果</li>
                <li>锁定图标的旋转锁紧动画</li>
            </ul>
        </div>
    </div>
    
    <div class="floating-action" onclick="toggleModal()">
        <span style="font-size: 1.5em;">🎨</span>
    </div>
    
    <div class="modal" id="designModal">
        <div class="modal-content">
            <span class="close-modal" onclick="toggleModal()">&times;</span>
            <h3 style="color: #00ffff; margin-bottom: 20px;">🔒 "锁手机"设计理念</h3>
            
            <h4>产品定位</h4>
            <p>"锁手机"直击用户痛点，通过强制性手机锁定帮助用户专注：</p>
            <ul class="feature-list">
                <li>锁定手机 = 物理隔离干扰源</li>
                <li>倒计时 = 明确的专注时长</li>
                <li>强制解锁 = 应急安全机制</li>
                <li>数据记录 = 自律成果可视化</li>
            </ul>
            
            <h4>用户价值</h4>
            <p>通过简单粗暴的锁定机制，解决现代人的专注力问题：</p>
            <ul class="feature-list">
                <li>真正有效 - 物理层面阻断手机使用</li>
                <li>简单易用 - 一键锁定，无需复杂设置</li>
                <li>数据驱动 - 量化自律成果</li>
                <li>习惯养成 - 通过数据反馈强化行为</li>
            </ul>
        </div>
    </div>
    
    <script>
        function toggleModal() {
            const modal = document.getElementById('designModal');
            modal.style.display = modal.style.display === 'flex' ? 'none' : 'flex';
        }
        
        // 点击模态框背景关闭
        document.getElementById('designModal').addEventListener('click', function(e) {
            if (e.target === this) {
                toggleModal();
            }
        });
        
        // 为按钮添加交互效果
        document.querySelectorAll('.task-button').forEach(button => {
            button.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });
        