package com.example.lockphone

import android.app.Activity
import android.app.admin.DeviceAdminReceiver
import android.app.admin.DevicePolicyManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log

/**
 * 设备管理员Kiosk模式管理器
 * 使用设备管理员权限实现真正的Kiosk模式
 */
class DeviceAdminKioskManager(private val activity: Activity) {
    
    companion object {
        private const val TAG = "DeviceAdminKioskManager"
        const val REQUEST_ENABLE_ADMIN = 2001
    }
    
    private val devicePolicyManager = activity.getSystemService(Context.DEVICE_POLICY_SERVICE) as DevicePolicyManager
    private val adminComponent = ComponentName(activity, YoYoDeviceAdminReceiver::class.java)
    
    /**
     * 检查是否有设备管理员权限
     */
    fun isDeviceAdminActive(): Boolean {
        return try {
            devicePolicyManager.isAdminActive(adminComponent)
        } catch (e: Exception) {
            Log.e(TAG, "检查设备管理员权限失败: ${e.message}")
            false
        }
    }
    
    /**
     * 请求设备管理员权限
     */
    fun requestDeviceAdminPermission(): Boolean {
        return try {
            if (isDeviceAdminActive()) {
                Log.d(TAG, "设备管理员权限已激活")
                return true
            }
            
            val intent = Intent(DevicePolicyManager.ACTION_ADD_DEVICE_ADMIN)
            intent.putExtra(DevicePolicyManager.EXTRA_DEVICE_ADMIN, adminComponent)
            intent.putExtra(DevicePolicyManager.EXTRA_ADD_EXPLANATION, 
                "需要设备管理员权限来实现真正的专注模式锁定")
            
            activity.startActivityForResult(intent, REQUEST_ENABLE_ADMIN)
            Log.d(TAG, "已请求设备管理员权限")
            true
        } catch (e: Exception) {
            Log.e(TAG, "请求设备管理员权限失败: ${e.message}")
            false
        }
    }
    
    /**
     * 启用Kiosk模式
     */
    fun enableKioskMode(): Boolean {
        if (!isDeviceAdminActive()) {
            Log.w(TAG, "设备管理员权限未激活，无法启用Kiosk模式")
            return false
        }
        
        return try {
            Log.d(TAG, "🔒 启用设备管理员Kiosk模式")
            
            // 1. 锁定任务（如果支持）
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                enableTaskLocking()
            }
            
            // 2. 禁用状态栏
            disableStatusBar()
            
            // 3. 禁用键盘保护
            disableKeyguard()
            
            // 4. 设置锁屏策略
            setLockScreenPolicy()
            
            Log.d(TAG, "✅ 设备管理员Kiosk模式已启用")
            true
        } catch (e: Exception) {
            Log.e(TAG, "启用Kiosk模式失败: ${e.message}")
            false
        }
    }
    
    /**
     * 禁用Kiosk模式
     */
    fun disableKioskMode(): Boolean {
        if (!isDeviceAdminActive()) {
            return true
        }
        
        return try {
            Log.d(TAG, "禁用设备管理员Kiosk模式")
            
            // 1. 停止任务锁定
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                disableTaskLocking()
            }
            
            // 2. 恢复状态栏
            enableStatusBar()
            
            // 3. 恢复键盘保护
            enableKeyguard()
            
            Log.d(TAG, "设备管理员Kiosk模式已禁用")
            true
        } catch (e: Exception) {
            Log.e(TAG, "禁用Kiosk模式失败: ${e.message}")
            false
        }
    }
    
    /**
     * 启用任务锁定（Android 5.0+）
     */
    private fun enableTaskLocking() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                // 设置锁定任务包
                val packages = arrayOf(activity.packageName)
                devicePolicyManager.setLockTaskPackages(adminComponent, packages)
                
                // 启动锁定任务模式
                activity.startLockTask()
                
                Log.d(TAG, "任务锁定已启用")
            }
        } catch (e: Exception) {
            Log.e(TAG, "启用任务锁定失败: ${e.message}")
        }
    }
    
    /**
     * 禁用任务锁定
     */
    private fun disableTaskLocking() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                // 停止锁定任务模式
                activity.stopLockTask()
                
                // 清除锁定任务包
                devicePolicyManager.setLockTaskPackages(adminComponent, arrayOf())
                
                Log.d(TAG, "任务锁定已禁用")
            }
        } catch (e: Exception) {
            Log.e(TAG, "禁用任务锁定失败: ${e.message}")
        }
    }
    
    /**
     * 禁用状态栏
     */
    private fun disableStatusBar() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                // Android 6.0+ 使用系统UI策略
                val flags = DevicePolicyManager.KEYGUARD_DISABLE_FEATURES_ALL
                devicePolicyManager.setKeyguardDisabledFeatures(adminComponent, flags)
            }
            Log.d(TAG, "状态栏已禁用")
        } catch (e: Exception) {
            Log.e(TAG, "禁用状态栏失败: ${e.message}")
        }
    }
    
    /**
     * 启用状态栏
     */
    private fun enableStatusBar() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                devicePolicyManager.setKeyguardDisabledFeatures(adminComponent, 
                    DevicePolicyManager.KEYGUARD_DISABLE_FEATURES_NONE)
            }
            Log.d(TAG, "状态栏已启用")
        } catch (e: Exception) {
            Log.e(TAG, "启用状态栏失败: ${e.message}")
        }
    }
    
    /**
     * 禁用键盘保护
     */
    private fun disableKeyguard() {
        try {
            // 设置密码策略
            devicePolicyManager.setPasswordQuality(adminComponent, 
                DevicePolicyManager.PASSWORD_QUALITY_UNSPECIFIED)
            Log.d(TAG, "键盘保护已禁用")
        } catch (e: Exception) {
            Log.e(TAG, "禁用键盘保护失败: ${e.message}")
        }
    }
    
    /**
     * 启用键盘保护
     */
    private fun enableKeyguard() {
        try {
            // 恢复默认密码策略
            devicePolicyManager.setPasswordQuality(adminComponent, 
                DevicePolicyManager.PASSWORD_QUALITY_UNSPECIFIED)
            Log.d(TAG, "键盘保护已启用")
        } catch (e: Exception) {
            Log.e(TAG, "启用键盘保护失败: ${e.message}")
        }
    }
    
    /**
     * 设置锁屏策略
     */
    private fun setLockScreenPolicy() {
        try {
            // 设置最大锁屏时间
            devicePolicyManager.setMaximumTimeToLock(adminComponent, 0)
            
            // 禁用相机
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH) {
                devicePolicyManager.setCameraDisabled(adminComponent, true)
            }
            
            Log.d(TAG, "锁屏策略已设置")
        } catch (e: Exception) {
            Log.e(TAG, "设置锁屏策略失败: ${e.message}")
        }
    }
    
    /**
     * 强制锁屏
     */
    fun forceLockScreen() {
        try {
            if (isDeviceAdminActive()) {
                devicePolicyManager.lockNow()
                Log.d(TAG, "已强制锁屏")
            }
        } catch (e: Exception) {
            Log.e(TAG, "强制锁屏失败: ${e.message}")
        }
    }

    /**
     * 启用专注模式系统锁屏
     * 专为专注模式设计的增强锁屏功能
     */
    fun enableFocusSystemLock(): Boolean {
        if (!isDeviceAdminActive()) {
            Log.w(TAG, "设备管理员权限未激活，无法启用专注模式系统锁屏")
            return false
        }

        return try {
            Log.d(TAG, "🔒 启用专注模式系统锁屏")

            // 1. 设置专注模式锁屏策略
            setFocusLockScreenPolicy()

            // 2. 强制激活系统锁屏
            devicePolicyManager.lockNow()

            // 3. 设置锁屏消息（Android 7.0+）
            setFocusLockScreenMessage()

            Log.d(TAG, "✅ 专注模式系统锁屏已启用")
            true
        } catch (e: Exception) {
            Log.e(TAG, "❌ 启用专注模式系统锁屏失败: ${e.message}")
            false
        }
    }

    /**
     * 设置专注模式锁屏策略
     */
    private fun setFocusLockScreenPolicy() {
        try {
            // 设置立即锁屏（0表示立即锁屏）
            devicePolicyManager.setMaximumTimeToLock(adminComponent, 0)

            // 禁用锁屏上的相机快捷方式
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH) {
                devicePolicyManager.setCameraDisabled(adminComponent, true)
            }

            // 设置锁屏超时策略
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                // 禁用指纹解锁（可选，根据需求）
                // devicePolicyManager.setKeyguardDisabledFeatures(adminComponent,
                //     DevicePolicyManager.KEYGUARD_DISABLE_FINGERPRINT)
            }

            Log.d(TAG, "专注模式锁屏策略已设置")
        } catch (e: Exception) {
            Log.e(TAG, "设置专注模式锁屏策略失败: ${e.message}")
        }
    }

    /**
     * 设置专注模式锁屏消息
     */
    private fun setFocusLockScreenMessage() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                // 设置短消息（显示在锁屏上）
                devicePolicyManager.setShortSupportMessage(adminComponent,
                    "🧘 专注模式激活中 - 保持专注，远离干扰")

                // 设置长消息（显示在设置中）
                devicePolicyManager.setLongSupportMessage(adminComponent,
                    "您正在使用专注模式。此模式将帮助您保持专注，避免手机干扰。如需退出，请完成专注任务或使用紧急退出功能。")
            }

            Log.d(TAG, "专注模式锁屏消息已设置")
        } catch (e: Exception) {
            Log.e(TAG, "设置专注模式锁屏消息失败: ${e.message}")
        }
    }

    /**
     * 禁用专注模式系统锁屏
     */
    fun disableFocusSystemLock(): Boolean {
        if (!isDeviceAdminActive()) {
            return true
        }

        return try {
            Log.d(TAG, "🔒 禁用专注模式系统锁屏")

            // 1. 恢复正常锁屏策略
            restoreNormalLockScreenPolicy()

            // 2. 清除锁屏消息
            clearFocusLockScreenMessage()

            Log.d(TAG, "✅ 专注模式系统锁屏已禁用")
            true
        } catch (e: Exception) {
            Log.e(TAG, "❌ 禁用专注模式系统锁屏失败: ${e.message}")
            false
        }
    }

    /**
     * 恢复正常锁屏策略
     */
    private fun restoreNormalLockScreenPolicy() {
        try {
            // 恢复正常锁屏超时（例如30秒）
            devicePolicyManager.setMaximumTimeToLock(adminComponent, 30000)

            // 恢复相机功能
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH) {
                devicePolicyManager.setCameraDisabled(adminComponent, false)
            }

            Log.d(TAG, "正常锁屏策略已恢复")
        } catch (e: Exception) {
            Log.e(TAG, "恢复正常锁屏策略失败: ${e.message}")
        }
    }

    /**
     * 清除专注模式锁屏消息
     */
    private fun clearFocusLockScreenMessage() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                devicePolicyManager.setShortSupportMessage(adminComponent, null)
                devicePolicyManager.setLongSupportMessage(adminComponent, null)
            }

            Log.d(TAG, "专注模式锁屏消息已清除")
        } catch (e: Exception) {
            Log.e(TAG, "清除专注模式锁屏消息失败: ${e.message}")
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            disableKioskMode()
        } catch (e: Exception) {
            Log.e(TAG, "清理资源失败: ${e.message}")
        }
    }
}

/**
 * 设备管理员接收器
 */
class YoYoDeviceAdminReceiver : DeviceAdminReceiver() {
    
    companion object {
        private const val TAG = "YoYoDeviceAdminReceiver"
    }
    
    override fun onEnabled(context: Context, intent: Intent) {
        super.onEnabled(context, intent)
        Log.d(TAG, "设备管理员已启用")
    }
    
    override fun onDisabled(context: Context, intent: Intent) {
        super.onDisabled(context, intent)
        Log.d(TAG, "设备管理员已禁用")
    }
    
    override fun onLockTaskModeEntering(context: Context, intent: Intent, pkg: String) {
        super.onLockTaskModeEntering(context, intent, pkg)
        Log.d(TAG, "进入锁定任务模式: $pkg")
    }
    
    override fun onLockTaskModeExiting(context: Context, intent: Intent) {
        super.onLockTaskModeExiting(context, intent)
        Log.d(TAG, "退出锁定任务模式")
    }
}
