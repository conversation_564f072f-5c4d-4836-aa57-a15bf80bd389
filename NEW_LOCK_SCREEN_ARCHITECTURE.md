# 基于YoYo日常的新锁屏功能架构设计

## 1. 设计原则

### 1.1 核心理念
- **简洁至上**：界面简洁，功能聚焦
- **用户友好**：操作简单，引导清晰
- **渐进增强**：基础功能 + 可选增强
- **安全可控**：用户始终保持控制权

### 1.2 设计目标
- 提供可靠的专注锁屏功能
- 简化权限申请流程
- 优化用户体验
- 确保功能稳定性

## 2. 功能架构设计

### 2.1 功能层次划分

#### 基础层（必需功能）
- 专注计时器
- 基础锁屏界面
- 紧急退出机制
- 专注记录保存

#### 增强层（可选功能）
- 无障碍服务锁定
- 悬浮窗阻止
- 手势拦截
- 高级统计

#### 扩展层（未来功能）
- 多种专注模式
- 社交功能
- 智能建议
- 个性化主题

### 2.2 权限管理策略

#### 渐进式权限申请
1. **首次使用**：仅申请基础权限
2. **功能需要时**：按需申请特定权限
3. **用户选择**：允许跳过非必需权限
4. **清晰说明**：每个权限都有明确用途

#### 权限分类
- **必需权限**：存储、网络（基础功能）
- **增强权限**：悬浮窗（锁屏界面）
- **高级权限**：无障碍服务（深度锁定）
- **可选权限**：通知、设备管理

## 3. 技术架构设计

### 3.1 整体架构

```
┌─────────────────────────────────────┐
│           Flutter UI Layer          │
├─────────────────────────────────────┤
│         Business Logic Layer        │
├─────────────────────────────────────┤
│        Platform Service Layer       │
├─────────────────────────────────────┤
│         Android Native Layer        │
└─────────────────────────────────────┘
```

### 3.2 模块设计

#### Flutter端模块
- **FocusManager**：专注会话管理
- **PermissionManager**：权限管理
- **UIController**：界面控制
- **DataManager**：数据存储
- **SettingsManager**：设置管理

#### Android端模块
- **LockScreenService**：锁屏服务
- **PermissionHelper**：权限辅助
- **OverlayManager**：悬浮窗管理
- **AccessibilityService**：无障碍服务（可选）

### 3.3 数据流设计

```
用户操作 → Flutter UI → Business Logic → Platform Service → Native Implementation
    ↓
数据存储 ← Data Manager ← Service Response ← Platform Channel ← Native Response
```

## 4. 核心功能实现

### 4.1 专注会话管理

#### 会话状态
- **IDLE**：空闲状态
- **PREPARING**：准备中
- **FOCUSING**：专注中
- **PAUSED**：暂停中
- **COMPLETED**：已完成
- **CANCELLED**：已取消

#### 状态转换
```
IDLE → PREPARING → FOCUSING → COMPLETED
  ↑        ↓           ↓         ↓
  └────────┴───────────┴─────────┘
           CANCELLED
```

### 4.2 锁屏实现策略

#### 基础锁屏（Level 1）
- Flutter全屏界面
- 禁用返回键
- 隐藏系统UI
- 计时器显示

#### 增强锁屏（Level 2）
- 悬浮窗覆盖
- 系统手势拦截
- 应用切换阻止
- 通知屏蔽

#### 深度锁屏（Level 3）
- 无障碍服务
- 系统级拦截
- 硬件按键处理
- 多层保护

### 4.3 权限申请流程

#### 首次启动
1. 显示欢迎界面
2. 基础功能介绍
3. 申请存储权限
4. 进入主界面

#### 功能使用时
1. 检查所需权限
2. 显示权限说明
3. 引导用户授权
4. 验证权限状态

#### 权限引导界面
```
┌─────────────────────────────────┐
│  🔒 专注锁屏需要以下权限          │
│                                 │
│  📱 悬浮窗权限                   │
│  用于显示专注界面，防止切换应用    │
│                                 │
│  [立即授权] [稍后设置]           │
└─────────────────────────────────┘
```

## 5. 用户界面设计

### 5.1 主界面设计

#### 布局结构
```
┌─────────────────────────────────┐
│  🏠 专注空间                     │
├─────────────────────────────────┤
│                                 │
│         [开始专注]               │
│                                 │
│  今日专注: 2次 | 45分钟          │
│  连续专注: 3天                   │
│                                 │
│  📊 统计  🏆 成就  ⚙️ 设置      │
└─────────────────────────────────┘
```

### 5.2 专注界面设计

#### 专注中界面
```
┌─────────────────────────────────┐
│                                 │
│           24:35                 │
│        ●●●●●●●○○○               │
│                                 │
│      深度工作进行中              │
│                                 │
│    "保持专注，你正在变得更好"     │
│                                 │
│     第2次专注 | 今日45分钟       │
│                                 │
└─────────────────────────────────┘
```

### 5.3 设置界面设计

#### 专注设置
```
┌─────────────────────────────────┐
│  专注设置                        │
├─────────────────────────────────┤
│  🔒 锁定级别                     │
│  ○ 基础锁定                     │
│  ● 增强锁定                     │
│  ○ 深度锁定                     │
│                                 │
│  ⏰ 默认时长: 25分钟             │
│  🔔 完成提醒: 开启               │
│  📱 紧急退出: 连续点击10次        │
└─────────────────────────────────┘
```

## 6. 实现计划

### 6.1 第一阶段：基础功能
- [ ] 专注计时器
- [ ] 基础锁屏界面
- [ ] 数据存储
- [ ] 基础权限管理

### 6.2 第二阶段：增强功能
- [ ] 悬浮窗锁屏
- [ ] 权限引导优化
- [ ] 统计功能
- [ ] 设置界面

### 6.3 第三阶段：高级功能
- [ ] 无障碍服务
- [ ] 深度锁定
- [ ] 成就系统
- [ ] 个性化设置

## 7. 技术要点

### 7.1 Flutter端关键技术
- Provider状态管理
- Method Channel通信
- 本地数据存储
- 系统UI控制

### 7.2 Android端关键技术
- Service生命周期管理
- 悬浮窗权限处理
- 无障碍服务实现
- 系统事件拦截

### 7.3 兼容性考虑
- Android版本适配
- 厂商ROM适配
- 权限变化处理
- 性能优化

## 8. 质量保证

### 8.1 测试策略
- 单元测试：核心逻辑
- 集成测试：模块交互
- UI测试：界面功能
- 兼容性测试：多设备

### 8.2 性能要求
- 启动时间 < 2秒
- 内存占用 < 50MB
- 电量消耗最小化
- 稳定性 > 99%

### 8.3 安全考虑
- 权限最小化原则
- 数据本地存储
- 用户隐私保护
- 紧急退出保障
