package com.example.lockphone

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.provider.Settings
import android.util.Log
import java.util.*

/**
 * 厂商ROM适配器
 * 检测并适配OPPO ColorOS、小米MIUI、华为EMUI等主流厂商ROM
 * 
 * 主要功能：
 * 1. 智能检测厂商ROM类型和版本
 * 2. 提供厂商特定的锁定策略
 * 3. 处理厂商特有的系统限制
 * 4. 优化专注锁定效果
 */
class VendorROMAdapter(private val activity: Activity) {
    
    companion object {
        private const val TAG = "VendorROMAdapter"
        
        // 厂商标识
        private const val VENDOR_OPPO = "OPPO"
        private const val VENDOR_ONEPLUS = "OnePlus"
        private const val VENDOR_XIAOMI = "Xiaomi"
        private const val VENDOR_REDMI = "Redmi"
        private const val VENDOR_HUAWEI = "HUAWEI"
        private const val VENDOR_HONOR = "HONOR"
        private const val VENDOR_SAMSUNG = "samsung"
        private const val VENDOR_VIVO = "vivo"
        private const val VENDOR_REALME = "realme"
        
        // ROM标识
        private const val ROM_COLOROS = "ColorOS"
        private const val ROM_MIUI = "MIUI"
        private const val ROM_EMUI = "EMUI"
        private const val ROM_HARMONYOS = "HarmonyOS"
        private const val ROM_ONEUI = "OneUI"
        private const val ROM_FUNTOUCH = "Funtouch"
        private const val ROM_REALME_UI = "RealmeUI"
    }
    
    private var detectedROMInfo: ROMInfo? = null
    
    /**
     * 检测厂商ROM类型
     */
    fun detectROMType(): ROMInfo {
        if (detectedROMInfo != null) {
            return detectedROMInfo!!
        }
        
        Log.d(TAG, "🔍 开始检测厂商ROM类型")
        
        val romInfo = when {
            isOPPOColorOS() -> createOPPOROMInfo()
            isXiaomiMIUI() -> createXiaomiROMInfo()
            isHuaweiEMUI() -> createHuaweiROMInfo()
            isSamsungOneUI() -> createSamsungROMInfo()
            isVivoFuntouch() -> createVivoROMInfo()
            isRealmeUI() -> createRealmeROMInfo()
            else -> createStockAndroidInfo()
        }
        
        detectedROMInfo = romInfo
        Log.d(TAG, "✅ ROM检测完成: ${romInfo.vendor} ${romInfo.romName} ${romInfo.version}")
        
        return romInfo
    }
    
    /**
     * 根据ROM类型创建优化的锁定策略
     */
    fun createOptimizedLockStrategy(): VendorLockStrategy {
        val romInfo = detectROMType()
        
        return when (romInfo.vendor) {
            ROMVendor.OPPO -> createOPPOLockStrategy(romInfo)
            ROMVendor.XIAOMI -> createXiaomiLockStrategy(romInfo)
            ROMVendor.HUAWEI -> createHuaweiLockStrategy(romInfo)
            ROMVendor.SAMSUNG -> createSamsungLockStrategy(romInfo)
            ROMVendor.VIVO -> createVivoLockStrategy(romInfo)
            ROMVendor.REALME -> createRealmeLockStrategy(romInfo)
            else -> createStandardLockStrategy(romInfo)
        }
    }
    
    /**
     * 检测是否为OPPO ColorOS
     */
    private fun isOPPOColorOS(): Boolean {
        return try {
            val brand = Build.BRAND.uppercase(Locale.getDefault())
            val manufacturer = Build.MANUFACTURER.uppercase(Locale.getDefault())
            
            // 检查品牌
            val isOPPOBrand = brand.contains(VENDOR_OPPO) || 
                             brand.contains(VENDOR_ONEPLUS) ||
                             manufacturer.contains(VENDOR_OPPO)
            
            // 检查ColorOS特征
            val hasColorOSFeature = hasSystemProperty("ro.build.version.opporom") ||
                                   hasSystemProperty("ro.oppo.version") ||
                                   Build.DISPLAY.contains(ROM_COLOROS, true)
            
            isOPPOBrand || hasColorOSFeature
        } catch (e: Exception) {
            Log.e(TAG, "检测OPPO ColorOS失败: ${e.message}")
            false
        }
    }
    
    /**
     * 检测是否为小米MIUI
     */
    private fun isXiaomiMIUI(): Boolean {
        return try {
            val brand = Build.BRAND.uppercase(Locale.getDefault())
            val manufacturer = Build.MANUFACTURER.uppercase(Locale.getDefault())
            
            // 检查品牌
            val isXiaomiBrand = brand.contains(VENDOR_XIAOMI) || 
                               brand.contains(VENDOR_REDMI) ||
                               manufacturer.contains(VENDOR_XIAOMI)
            
            // 检查MIUI特征
            val hasMIUIFeature = hasSystemProperty("ro.miui.ui.version.name") ||
                                hasSystemProperty("ro.miui.ui.version.code") ||
                                Build.DISPLAY.contains(ROM_MIUI, true)
            
            isXiaomiBrand || hasMIUIFeature
        } catch (e: Exception) {
            Log.e(TAG, "检测小米MIUI失败: ${e.message}")
            false
        }
    }
    
    /**
     * 检测是否为华为EMUI/HarmonyOS
     */
    private fun isHuaweiEMUI(): Boolean {
        return try {
            val brand = Build.BRAND.uppercase(Locale.getDefault())
            val manufacturer = Build.MANUFACTURER.uppercase(Locale.getDefault())
            
            // 检查品牌
            val isHuaweiBrand = brand.contains(VENDOR_HUAWEI) || 
                               brand.contains(VENDOR_HONOR) ||
                               manufacturer.contains(VENDOR_HUAWEI)
            
            // 检查EMUI/HarmonyOS特征
            val hasEMUIFeature = hasSystemProperty("ro.build.version.emui") ||
                                hasSystemProperty("hw_sc.build.platform.version") ||
                                Build.DISPLAY.contains(ROM_EMUI, true) ||
                                Build.DISPLAY.contains(ROM_HARMONYOS, true)
            
            isHuaweiBrand || hasEMUIFeature
        } catch (e: Exception) {
            Log.e(TAG, "检测华为EMUI失败: ${e.message}")
            false
        }
    }
    
    /**
     * 检测是否为三星OneUI
     */
    private fun isSamsungOneUI(): Boolean {
        return try {
            val brand = Build.BRAND.uppercase(Locale.getDefault())
            val manufacturer = Build.MANUFACTURER.uppercase(Locale.getDefault())
            
            brand.contains(VENDOR_SAMSUNG) || manufacturer.contains(VENDOR_SAMSUNG)
        } catch (e: Exception) {
            Log.e(TAG, "检测三星OneUI失败: ${e.message}")
            false
        }
    }
    
    /**
     * 检测是否为Vivo Funtouch
     */
    private fun isVivoFuntouch(): Boolean {
        return try {
            val brand = Build.BRAND.uppercase(Locale.getDefault())
            val manufacturer = Build.MANUFACTURER.uppercase(Locale.getDefault())
            
            brand.contains(VENDOR_VIVO) || manufacturer.contains(VENDOR_VIVO)
        } catch (e: Exception) {
            Log.e(TAG, "检测Vivo Funtouch失败: ${e.message}")
            false
        }
    }
    
    /**
     * 检测是否为Realme UI
     */
    private fun isRealmeUI(): Boolean {
        return try {
            val brand = Build.BRAND.uppercase(Locale.getDefault())
            val manufacturer = Build.MANUFACTURER.uppercase(Locale.getDefault())
            
            brand.contains(VENDOR_REALME) || manufacturer.contains(VENDOR_REALME)
        } catch (e: Exception) {
            Log.e(TAG, "检测Realme UI失败: ${e.message}")
            false
        }
    }
    
    /**
     * 检查系统属性
     */
    private fun hasSystemProperty(property: String): Boolean {
        return try {
            val systemProperties = Class.forName("android.os.SystemProperties")
            val getMethod = systemProperties.getMethod("get", String::class.java)
            val value = getMethod.invoke(null, property) as String
            value.isNotEmpty()
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 获取系统属性值
     */
    private fun getSystemProperty(property: String, defaultValue: String = ""): String {
        return try {
            val systemProperties = Class.forName("android.os.SystemProperties")
            val getMethod = systemProperties.getMethod("get", String::class.java, String::class.java)
            getMethod.invoke(null, property, defaultValue) as String
        } catch (e: Exception) {
            defaultValue
        }
    }
    
    /**
     * 创建OPPO ROM信息
     */
    private fun createOPPOROMInfo(): ROMInfo {
        val version = getSystemProperty("ro.build.version.opporom", "Unknown")
        return ROMInfo(
            vendor = ROMVendor.OPPO,
            romName = ROM_COLOROS,
            version = version,
            androidVersion = Build.VERSION.RELEASE,
            apiLevel = Build.VERSION.SDK_INT,
            brand = Build.BRAND,
            model = Build.MODEL
        )
    }
    
    /**
     * 创建小米ROM信息
     */
    private fun createXiaomiROMInfo(): ROMInfo {
        val version = getSystemProperty("ro.miui.ui.version.name", "Unknown")
        return ROMInfo(
            vendor = ROMVendor.XIAOMI,
            romName = ROM_MIUI,
            version = version,
            androidVersion = Build.VERSION.RELEASE,
            apiLevel = Build.VERSION.SDK_INT,
            brand = Build.BRAND,
            model = Build.MODEL
        )
    }
    
    /**
     * 创建华为ROM信息
     */
    private fun createHuaweiROMInfo(): ROMInfo {
        val emuiVersion = getSystemProperty("ro.build.version.emui", "")
        val harmonyVersion = getSystemProperty("hw_sc.build.platform.version", "")
        
        val romName = if (harmonyVersion.isNotEmpty()) ROM_HARMONYOS else ROM_EMUI
        val version = if (harmonyVersion.isNotEmpty()) harmonyVersion else emuiVersion
        
        return ROMInfo(
            vendor = ROMVendor.HUAWEI,
            romName = romName,
            version = version,
            androidVersion = Build.VERSION.RELEASE,
            apiLevel = Build.VERSION.SDK_INT,
            brand = Build.BRAND,
            model = Build.MODEL
        )
    }
    
    /**
     * 创建三星ROM信息
     */
    private fun createSamsungROMInfo(): ROMInfo {
        return ROMInfo(
            vendor = ROMVendor.SAMSUNG,
            romName = ROM_ONEUI,
            version = "Unknown",
            androidVersion = Build.VERSION.RELEASE,
            apiLevel = Build.VERSION.SDK_INT,
            brand = Build.BRAND,
            model = Build.MODEL
        )
    }
    
    /**
     * 创建Vivo ROM信息
     */
    private fun createVivoROMInfo(): ROMInfo {
        return ROMInfo(
            vendor = ROMVendor.VIVO,
            romName = ROM_FUNTOUCH,
            version = "Unknown",
            androidVersion = Build.VERSION.RELEASE,
            apiLevel = Build.VERSION.SDK_INT,
            brand = Build.BRAND,
            model = Build.MODEL
        )
    }
    
    /**
     * 创建Realme ROM信息
     */
    private fun createRealmeROMInfo(): ROMInfo {
        return ROMInfo(
            vendor = ROMVendor.REALME,
            romName = ROM_REALME_UI,
            version = "Unknown",
            androidVersion = Build.VERSION.RELEASE,
            apiLevel = Build.VERSION.SDK_INT,
            brand = Build.BRAND,
            model = Build.MODEL
        )
    }
    
    /**
     * 创建原生Android信息
     */
    private fun createStockAndroidInfo(): ROMInfo {
        return ROMInfo(
            vendor = ROMVendor.STOCK_ANDROID,
            romName = "Android",
            version = Build.VERSION.RELEASE,
            androidVersion = Build.VERSION.RELEASE,
            apiLevel = Build.VERSION.SDK_INT,
            brand = Build.BRAND,
            model = Build.MODEL
        )
    }

    /**
     * 创建OPPO锁定策略
     */
    private fun createOPPOLockStrategy(romInfo: ROMInfo): VendorLockStrategy {
        Log.d(TAG, "🔧 创建OPPO ColorOS锁定策略")

        return VendorLockStrategy(
            vendor = romInfo.vendor,
            useSystemLock = true,
            overlayType = OverlayType.TYPE_APPLICATION_OVERLAY,
            gestureBlocking = GestureBlockingLevel.ENHANCED,
            accessibilityRequired = true,
            deviceAdminRequired = true,
            specialPermissions = listOf(
                "oppo.permission.OPPO_COMPONENT_SAFE",
                "com.coloros.safecenter.permission.startup",
                "android.permission.SYSTEM_ALERT_WINDOW"
            ),
            vendorOptimizations = OPPOOptimizations(
                disableColorOSGestures = true,
                blockSmartSidebar = true,
                disableGameSpace = true,
                preventAutoFreeze = true
            ),
            fallbackStrategy = createStandardLockStrategy(romInfo)
        )
    }

    /**
     * 创建小米锁定策略
     */
    private fun createXiaomiLockStrategy(romInfo: ROMInfo): VendorLockStrategy {
        Log.d(TAG, "🔧 创建小米MIUI锁定策略")

        return VendorLockStrategy(
            vendor = romInfo.vendor,
            useSystemLock = true,
            overlayType = OverlayType.TYPE_APPLICATION_OVERLAY,
            gestureBlocking = GestureBlockingLevel.MAXIMUM,
            accessibilityRequired = true,
            deviceAdminRequired = true,
            specialPermissions = listOf(
                "miui.permission.USE_INTERNAL_GENERAL_API",
                "android.permission.SYSTEM_ALERT_WINDOW",
                "android.permission.WRITE_SECURE_SETTINGS"
            ),
            vendorOptimizations = MIUIOptimizations(
                disableGestureNavigation = true,
                blockMIUIControlCenter = true,
                disableQuickSettings = true,
                preventMIUIOptimization = true,
                blockGameTurbo = true
            ),
            fallbackStrategy = createStandardLockStrategy(romInfo)
        )
    }

    /**
     * 创建华为锁定策略
     */
    private fun createHuaweiLockStrategy(romInfo: ROMInfo): VendorLockStrategy {
        Log.d(TAG, "🔧 创建华为EMUI/HarmonyOS锁定策略")

        return VendorLockStrategy(
            vendor = romInfo.vendor,
            useSystemLock = false, // 华为设备管理员限制较多
            overlayType = OverlayType.MULTIPLE_LAYERS,
            gestureBlocking = GestureBlockingLevel.MAXIMUM,
            accessibilityRequired = true,
            deviceAdminRequired = false, // 华为限制较严
            specialPermissions = listOf(
                "com.huawei.permission.external_app_settings.USE_COMPONENT",
                "android.permission.SYSTEM_ALERT_WINDOW",
                "com.huawei.android.launcher.permission.CHANGE_BADGE"
            ),
            vendorOptimizations = EMUIOptimizations(
                useHuaweiFloatingWindow = true,
                blockEMUINavigationGestures = true,
                disableHuaweiAssistant = true,
                preventPowerGenie = true,
                blockSmartAssist = true
            ),
            fallbackStrategy = createStandardLockStrategy(romInfo)
        )
    }

    /**
     * 创建三星锁定策略
     */
    private fun createSamsungLockStrategy(romInfo: ROMInfo): VendorLockStrategy {
        Log.d(TAG, "🔧 创建三星OneUI锁定策略")

        return VendorLockStrategy(
            vendor = romInfo.vendor,
            useSystemLock = true,
            overlayType = OverlayType.TYPE_APPLICATION_OVERLAY,
            gestureBlocking = GestureBlockingLevel.ENHANCED,
            accessibilityRequired = true,
            deviceAdminRequired = true,
            specialPermissions = listOf(
                "android.permission.SYSTEM_ALERT_WINDOW",
                "com.samsung.android.providers.context.permission.WRITE_USE_APP_FEATURE_SURVEY"
            ),
            vendorOptimizations = OneUIOptimizations(
                disableEdgePanel = true,
                blockBixby = true,
                disableSmartSelect = true,
                preventGameLauncher = true
            ),
            fallbackStrategy = createStandardLockStrategy(romInfo)
        )
    }

    /**
     * 创建Vivo锁定策略
     */
    private fun createVivoLockStrategy(romInfo: ROMInfo): VendorLockStrategy {
        Log.d(TAG, "🔧 创建Vivo Funtouch锁定策略")

        return VendorLockStrategy(
            vendor = romInfo.vendor,
            useSystemLock = true,
            overlayType = OverlayType.TYPE_APPLICATION_OVERLAY,
            gestureBlocking = GestureBlockingLevel.ENHANCED,
            accessibilityRequired = true,
            deviceAdminRequired = true,
            specialPermissions = listOf(
                "android.permission.SYSTEM_ALERT_WINDOW",
                "com.vivo.permissionmanager.permission.ACCESS_PERMISSION_MANAGER"
            ),
            vendorOptimizations = FuntouchOptimizations(
                disableSmartMotion = true,
                blockJoviAssistant = true,
                preventVivoOptimization = true
            ),
            fallbackStrategy = createStandardLockStrategy(romInfo)
        )
    }

    /**
     * 创建Realme锁定策略
     */
    private fun createRealmeLockStrategy(romInfo: ROMInfo): VendorLockStrategy {
        Log.d(TAG, "🔧 创建Realme UI锁定策略")

        return VendorLockStrategy(
            vendor = romInfo.vendor,
            useSystemLock = true,
            overlayType = OverlayType.TYPE_APPLICATION_OVERLAY,
            gestureBlocking = GestureBlockingLevel.ENHANCED,
            accessibilityRequired = true,
            deviceAdminRequired = true,
            specialPermissions = listOf(
                "android.permission.SYSTEM_ALERT_WINDOW"
            ),
            vendorOptimizations = RealmeUIOptimizations(
                preventRealmeOptimization = true,
                blockGameSpace = true
            ),
            fallbackStrategy = createStandardLockStrategy(romInfo)
        )
    }

    /**
     * 创建标准Android锁定策略
     */
    private fun createStandardLockStrategy(romInfo: ROMInfo): VendorLockStrategy {
        Log.d(TAG, "🔧 创建标准Android锁定策略")

        return VendorLockStrategy(
            vendor = romInfo.vendor,
            useSystemLock = true,
            overlayType = OverlayType.TYPE_APPLICATION_OVERLAY,
            gestureBlocking = GestureBlockingLevel.STANDARD,
            accessibilityRequired = true,
            deviceAdminRequired = true,
            specialPermissions = listOf(
                "android.permission.SYSTEM_ALERT_WINDOW"
            ),
            vendorOptimizations = StandardOptimizations(),
            fallbackStrategy = null
        )
    }
}
