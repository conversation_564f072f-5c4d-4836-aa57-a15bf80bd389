package com.example.lockphone

import android.app.Activity
import android.os.Build
import android.util.Log
import android.view.View
import android.view.WindowManager
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat

/**
 * 系统UI控制器 - 现代化沉浸式体验
 * 使用最新的WindowInsets API实现完全的系统UI隐藏
 */
class SystemUIController(private val activity: Activity) {
    
    companion object {
        private const val TAG = "SystemUIController"
    }
    
    private var isImmersiveModeActive = false
    private val windowInsetsController: WindowInsetsControllerCompat by lazy {
        WindowCompat.getInsetsController(activity.window, activity.window.decorView)
    }
    
    /**
     * 启用完全沉浸式模式
     */
    fun enableFullImmersiveMode(): Boolean {
        try {
            Log.d(TAG, "🔒 SystemUIController: 启用完全沉浸式模式")
            
            // 使用现代API（Android 11+）
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                enableModernImmersiveMode()
            } else {
                enableLegacyImmersiveMode()
            }
            
            // 设置窗口标志
            setWindowFlags()
            
            // 设置系统栏行为
            configureSystemBars()
            
            isImmersiveModeActive = true
            Log.d(TAG, "✅ SystemUIController: 完全沉浸式模式已启用")
            return true
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ SystemUIController: 启用沉浸式模式失败: ${e.message}")
            return false
        }
    }
    
    /**
     * 使用现代API启用沉浸式模式（Android 11+）
     */
    private fun enableModernImmersiveMode() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            Log.d(TAG, "🔒 SystemUIController: 使用现代WindowInsets API")
            
            // 隐藏系统栏
            windowInsetsController.hide(
                WindowInsetsCompat.Type.systemBars() or
                WindowInsetsCompat.Type.navigationBars() or
                WindowInsetsCompat.Type.statusBars()
            )
            
            // 设置系统栏行为
            windowInsetsController.systemBarsBehavior = 
                WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        }
    }
    
    /**
     * 使用传统API启用沉浸式模式（Android 10及以下）
     */
    @Suppress("DEPRECATION")
    private fun enableLegacyImmersiveMode() {
        Log.d(TAG, "🔒 SystemUIController: 使用传统SystemUiVisibility API")
        
        activity.window.decorView.systemUiVisibility = (
            View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
            View.SYSTEM_UI_FLAG_FULLSCREEN or
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY or
            View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE
        )
    }
    
    /**
     * 设置窗口标志
     */
    private fun setWindowFlags() {
        Log.d(TAG, "🔒 SystemUIController: 设置窗口标志")
        
        activity.window.apply {
            // 添加窗口标志
            addFlags(
                WindowManager.LayoutParams.FLAG_FULLSCREEN or
                WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON or
                WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD or
                WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED or
                WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON
            )
            
            // 清除可能干扰的标志
            clearFlags(
                WindowManager.LayoutParams.FLAG_FORCE_NOT_FULLSCREEN
            )
        }
    }
    
    /**
     * 配置系统栏行为
     */
    private fun configureSystemBars() {
        Log.d(TAG, "🔒 SystemUIController: 配置系统栏行为")
        
        // 设置状态栏和导航栏为透明
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            activity.window.apply {
                statusBarColor = android.graphics.Color.TRANSPARENT
                navigationBarColor = android.graphics.Color.TRANSPARENT
            }
        }
        
        // 确保内容延伸到系统栏下方
        WindowCompat.setDecorFitsSystemWindows(activity.window, false)
    }
    
    /**
     * 监听系统UI可见性变化
     */
    fun setupSystemUIVisibilityListener() {
        Log.d(TAG, "🔒 SystemUIController: 设置系统UI可见性监听器")
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // 使用现代API监听
            activity.window.decorView.setOnApplyWindowInsetsListener { _, insets ->
                val systemBarsVisible = insets.isVisible(WindowInsetsCompat.Type.systemBars())
                if (systemBarsVisible && isImmersiveModeActive) {
                    Log.w(TAG, "⚠️ SystemUIController: 检测到系统栏显示，重新隐藏")
                    // 延迟重新隐藏，避免与系统冲突
                    activity.window.decorView.postDelayed({
                        enableFullImmersiveMode()
                    }, 100)
                }
                insets
            }
        } else {
            // 使用传统API监听
            @Suppress("DEPRECATION")
            activity.window.decorView.setOnSystemUiVisibilityChangeListener { visibility ->
                if ((visibility and View.SYSTEM_UI_FLAG_FULLSCREEN) == 0 && isImmersiveModeActive) {
                    Log.w(TAG, "⚠️ SystemUIController: 检测到系统UI显示，重新隐藏")
                    // 延迟重新隐藏
                    activity.window.decorView.postDelayed({
                        enableFullImmersiveMode()
                    }, 100)
                }
            }
        }
    }
    
    /**
     * 禁用沉浸式模式
     */
    fun disableImmersiveMode(): Boolean {
        try {
            Log.d(TAG, "🔒 SystemUIController: 禁用沉浸式模式")
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                windowInsetsController.show(WindowInsetsCompat.Type.systemBars())
                windowInsetsController.systemBarsBehavior = 
                    WindowInsetsControllerCompat.BEHAVIOR_DEFAULT
            } else {
                @Suppress("DEPRECATION")
                activity.window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_VISIBLE
            }
            
            // 清除窗口标志
            activity.window.clearFlags(
                WindowManager.LayoutParams.FLAG_FULLSCREEN or
                WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
            )
            
            isImmersiveModeActive = false
            Log.d(TAG, "✅ SystemUIController: 沉浸式模式已禁用")
            return true
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ SystemUIController: 禁用沉浸式模式失败: ${e.message}")
            return false
        }
    }
    
    /**
     * 检查沉浸式模式状态
     */
    fun isImmersiveModeActive(): Boolean = isImmersiveModeActive
    
    /**
     * 强制刷新系统UI状态
     */
    fun refreshSystemUI() {
        if (isImmersiveModeActive) {
            Log.d(TAG, "🔒 SystemUIController: 强制刷新系统UI状态")
            enableFullImmersiveMode()
        }
    }
    
    /**
     * 处理窗口焦点变化
     */
    fun onWindowFocusChanged(hasFocus: Boolean) {
        Log.d(TAG, "🔒 SystemUIController: 窗口焦点变化 - hasFocus: $hasFocus")
        
        if (hasFocus && isImmersiveModeActive) {
            // 窗口获得焦点时重新启用沉浸式模式
            activity.window.decorView.postDelayed({
                enableFullImmersiveMode()
            }, 50)
        }
    }
}
