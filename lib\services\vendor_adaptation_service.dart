import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

/// 厂商适配服务
/// 处理不同厂商ROM的适配和权限管理
///
/// 主要功能：
/// 1. 检测厂商ROM类型和版本
/// 2. 获取厂商适配状态
/// 3. 申请厂商特定权限
/// 4. 提供权限设置引导
class VendorAdaptationService {
  static const MethodChannel _channel = MethodChannel('yoyo_lock_screen');

  /// 单例实例
  static final VendorAdaptationService _instance =
      VendorAdaptationService._internal();
  factory VendorAdaptationService() => _instance;
  VendorAdaptationService._internal();

  /// 当前厂商适配状态
  VendorAdaptationStatus? _currentStatus;

  /// 状态变化回调
  Function(VendorAdaptationStatus)? _onStatusChanged;

  /// 设置状态变化监听器
  void setStatusChangeListener(Function(VendorAdaptationStatus) listener) {
    _onStatusChanged = listener;
  }

  /// 获取厂商适配状态
  Future<VendorAdaptationStatus> getVendorAdaptationStatus() async {
    try {
      debugPrint('📱 VendorAdaptationService: 获取厂商适配状态');

      final Map<dynamic, dynamic> result =
          await _channel.invokeMethod('getVendorAdaptationStatus');

      final status =
          VendorAdaptationStatus.fromMap(Map<String, dynamic>.from(result));

      // 更新本地状态
      _currentStatus = status;

      debugPrint('✅ VendorAdaptationService: 厂商适配状态获取成功');
      debugPrint('   - 厂商: ${status.vendor}');
      debugPrint('   - ROM: ${status.romName} ${status.romVersion}');
      debugPrint('   - 适配级别: ${status.adaptationLevel}');
      debugPrint('   - 已授权权限: ${status.grantedPermissions.length}');
      debugPrint('   - 缺失权限: ${status.missingPermissions.length}');

      return status;
    } catch (e) {
      debugPrint('❌ VendorAdaptationService: 获取厂商适配状态异常: $e');
      return VendorAdaptationStatus.error(e.toString());
    }
  }

  /// 申请厂商权限
  Future<bool> requestVendorPermissions() async {
    try {
      debugPrint('🔐 VendorAdaptationService: 申请厂商权限');

      final bool result =
          await _channel.invokeMethod('requestVendorPermissions');

      if (result) {
        debugPrint('✅ VendorAdaptationService: 厂商权限申请成功');

        // 重新获取状态
        final status = await getVendorAdaptationStatus();
        _onStatusChanged?.call(status);
      } else {
        debugPrint('❌ VendorAdaptationService: 厂商权限申请失败');
      }

      return result;
    } catch (e) {
      debugPrint('❌ VendorAdaptationService: 申请厂商权限异常: $e');
      return false;
    }
  }

  /// 检查特定权限状态
  Future<VendorPermissionStatus> checkVendorPermission(
      String permissionType) async {
    try {
      debugPrint('🔍 VendorAdaptationService: 检查权限 - $permissionType');

      final Map<dynamic, dynamic> result =
          await _channel.invokeMethod('checkVendorPermission', {
        'permissionType': permissionType,
      });

      return VendorPermissionStatus.fromMap(Map<String, dynamic>.from(result));
    } catch (e) {
      debugPrint('❌ VendorAdaptationService: 检查权限异常: $e');
      return VendorPermissionStatus.error(permissionType, e.toString());
    }
  }

  /// 获取权限设置引导信息
  List<PermissionGuideStep> getPermissionGuideSteps() {
    if (_currentStatus == null) {
      return [];
    }

    final steps = <PermissionGuideStep>[];

    for (final permission in _currentStatus!.requiredPermissions) {
      if (_currentStatus!.missingPermissions.contains(permission.type)) {
        steps.add(PermissionGuideStep(
          title: permission.displayName,
          description: permission.description,
          permissionType: permission.type,
          isRequired: permission.isRequired,
          steps: _generateStepsForPermission(permission),
        ));
      }
    }

    return steps;
  }

  /// 生成特定权限的设置步骤
  List<String> _generateStepsForPermission(VendorPermissionInfo permission) {
    switch (_currentStatus!.vendor) {
      case 'OPPO':
        return _generateOPPOSteps(permission.type);
      case 'XIAOMI':
        return _generateXiaomiSteps(permission.type);
      case 'HUAWEI':
        return _generateHuaweiSteps(permission.type);
      case 'SAMSUNG':
        return _generateSamsungSteps(permission.type);
      case 'VIVO':
        return _generateVivoSteps(permission.type);
      case 'REALME':
        return _generateRealmeSteps(permission.type);
      default:
        return _generateStandardSteps(permission.type);
    }
  }

  /// 生成OPPO权限设置步骤
  List<String> _generateOPPOSteps(String permissionType) {
    switch (permissionType) {
      case 'AUTO_START':
        return [
          '1. 打开"手机管家"应用',
          '2. 点击"权限隐私"',
          '3. 选择"自启动管理"',
          '4. 找到"专注锁屏"应用',
          '5. 开启自启动权限',
        ];
      case 'FLOATING_WINDOW':
        return [
          '1. 打开"设置"应用',
          '2. 点击"权限与隐私"',
          '3. 选择"悬浮窗"',
          '4. 找到"专注锁屏"应用',
          '5. 开启悬浮窗权限',
        ];
      case 'BACKGROUND_RUN':
        return [
          '1. 打开"手机管家"应用',
          '2. 点击"权限隐私"',
          '3. 选择"应用权限管理"',
          '4. 找到"专注锁屏"应用',
          '5. 开启后台运行权限',
        ];
      default:
        return ['请在设置中手动开启相关权限'];
    }
  }

  /// 生成小米权限设置步骤
  List<String> _generateXiaomiSteps(String permissionType) {
    switch (permissionType) {
      case 'AUTO_START':
        return [
          '1. 打开"安全中心"应用',
          '2. 点击"应用管理"',
          '3. 选择"自启动管理"',
          '4. 找到"专注锁屏"应用',
          '5. 开启自启动权限',
        ];
      case 'FLOATING_WINDOW':
        return [
          '1. 打开"设置"应用',
          '2. 点击"应用设置"',
          '3. 选择"系统应用设置"',
          '4. 点击"悬浮窗管理"',
          '5. 找到"专注锁屏"应用并开启',
        ];
      case 'BACKGROUND_RUN':
        return [
          '1. 打开"安全中心"应用',
          '2. 点击"应用管理"',
          '3. 选择"权限管理"',
          '4. 找到"专注锁屏"应用',
          '5. 开启后台弹出界面权限',
        ];
      default:
        return ['请在MIUI设置中手动开启相关权限'];
    }
  }

  /// 生成华为权限设置步骤
  List<String> _generateHuaweiSteps(String permissionType) {
    switch (permissionType) {
      case 'AUTO_START':
        return [
          '1. 打开"手机管家"应用',
          '2. 点击"应用启动管理"',
          '3. 找到"专注锁屏"应用',
          '4. 关闭"自动管理"',
          '5. 手动开启"自动启动"、"关联启动"、"后台活动"',
        ];
      case 'FLOATING_WINDOW':
        return [
          '1. 打开"设置"应用',
          '2. 点击"应用和服务"',
          '3. 选择"权限管理"',
          '4. 点击"悬浮窗"',
          '5. 找到"专注锁屏"应用并开启',
        ];
      case 'BATTERY_OPTIMIZATION':
        return [
          '1. 打开"手机管家"应用',
          '2. 点击"电池"',
          '3. 选择"更多电池设置"',
          '4. 点击"休眠时始终保持网络连接"',
          '5. 将"专注锁屏"加入白名单',
        ];
      default:
        return ['请在华为设置中手动开启相关权限'];
    }
  }

  /// 生成三星权限设置步骤
  List<String> _generateSamsungSteps(String permissionType) {
    switch (permissionType) {
      case 'FLOATING_WINDOW':
        return [
          '1. 打开"设置"应用',
          '2. 点击"应用"',
          '3. 选择"专注锁屏"',
          '4. 点击"权限"',
          '5. 开启"在其他应用上层显示"权限',
        ];
      case 'BATTERY_OPTIMIZATION':
        return [
          '1. 打开"设置"应用',
          '2. 点击"设备保养"',
          '3. 选择"电池"',
          '4. 点击"更多电池设置"',
          '5. 选择"优化电池使用"',
          '6. 将"专注锁屏"设为"不优化"',
        ];
      default:
        return ['请在三星设置中手动开启相关权限'];
    }
  }

  /// 生成Vivo权限设置步骤
  List<String> _generateVivoSteps(String permissionType) {
    switch (permissionType) {
      case 'AUTO_START':
        return [
          '1. 打开"i管家"应用',
          '2. 点击"应用管理"',
          '3. 选择"自启动管理"',
          '4. 找到"专注锁屏"应用',
          '5. 开启自启动权限',
        ];
      case 'FLOATING_WINDOW':
        return [
          '1. 打开"设置"应用',
          '2. 点击"权限管理"',
          '3. 选择"悬浮窗"',
          '4. 找到"专注锁屏"应用',
          '5. 开启悬浮窗权限',
        ];
      default:
        return ['请在Vivo设置中手动开启相关权限'];
    }
  }

  /// 生成Realme权限设置步骤
  List<String> _generateRealmeSteps(String permissionType) {
    switch (permissionType) {
      case 'FLOATING_WINDOW':
        return [
          '1. 打开"设置"应用',
          '2. 点击"权限管理"',
          '3. 选择"悬浮窗权限"',
          '4. 找到"专注锁屏"应用',
          '5. 开启悬浮窗权限',
        ];
      case 'BATTERY_OPTIMIZATION':
        return [
          '1. 打开"设置"应用',
          '2. 点击"电池"',
          '3. 选择"电池优化"',
          '4. 找到"专注锁屏"应用',
          '5. 设置为"不优化"',
        ];
      default:
        return ['请在Realme设置中手动开启相关权限'];
    }
  }

  /// 生成标准Android权限设置步骤
  List<String> _generateStandardSteps(String permissionType) {
    switch (permissionType) {
      case 'FLOATING_WINDOW':
        return [
          '1. 打开"设置"应用',
          '2. 点击"应用和通知"',
          '3. 选择"特殊应用权限"',
          '4. 点击"显示在其他应用上层"',
          '5. 找到"专注锁屏"应用并开启',
        ];
      case 'DEVICE_ADMIN':
        return [
          '1. 打开"设置"应用',
          '2. 点击"安全"',
          '3. 选择"设备管理器"',
          '4. 找到"专注锁屏"',
          '5. 激活设备管理员权限',
        ];
      case 'ACCESSIBILITY':
        return [
          '1. 打开"设置"应用',
          '2. 点击"无障碍"',
          '3. 选择"已下载的应用"',
          '4. 找到"专注锁屏无障碍服务"',
          '5. 开启无障碍服务',
        ];
      default:
        return ['请在系统设置中手动开启相关权限'];
    }
  }

  /// 获取当前适配状态
  VendorAdaptationStatus? get currentStatus => _currentStatus;

  /// 检查是否需要权限设置
  bool get needsPermissionSetup {
    return _currentStatus?.missingPermissions.isNotEmpty ?? true;
  }

  /// 获取适配级别描述
  String getAdaptationLevelDescription() {
    if (_currentStatus == null) return '未知';

    switch (_currentStatus!.adaptationLevel) {
      case 'FULL':
        return '完全适配 - 所有功能正常工作';
      case 'PARTIAL':
        return '部分适配 - 大部分功能正常工作';
      case 'BASIC':
        return '基础适配 - 基本功能可用';
      case 'LIMITED':
        return '受限适配 - 功能可能受限';
      default:
        return '未知适配级别';
    }
  }
}

/// 厂商适配状态数据类
class VendorAdaptationStatus {
  final String vendor;
  final String romName;
  final String romVersion;
  final String androidVersion;
  final int apiLevel;
  final String brand;
  final String model;
  final String adaptationLevel;
  final List<VendorPermissionInfo> requiredPermissions;
  final List<String> grantedPermissions;
  final List<String> missingPermissions;
  final List<String> recommendations;
  final String? error;

  const VendorAdaptationStatus({
    required this.vendor,
    required this.romName,
    required this.romVersion,
    required this.androidVersion,
    required this.apiLevel,
    required this.brand,
    required this.model,
    required this.adaptationLevel,
    required this.requiredPermissions,
    required this.grantedPermissions,
    required this.missingPermissions,
    required this.recommendations,
    this.error,
  });

  factory VendorAdaptationStatus.fromMap(Map<String, dynamic> map) {
    return VendorAdaptationStatus(
      vendor: map['vendor'] ?? 'UNKNOWN',
      romName: map['romName'] ?? 'Unknown',
      romVersion: map['romVersion'] ?? 'Unknown',
      androidVersion: map['androidVersion'] ?? 'Unknown',
      apiLevel: map['apiLevel'] ?? 0,
      brand: map['brand'] ?? 'Unknown',
      model: map['model'] ?? 'Unknown',
      adaptationLevel: map['adaptationLevel'] ?? 'LIMITED',
      requiredPermissions: (map['requiredPermissions'] as List<dynamic>?)
              ?.map((item) =>
                  VendorPermissionInfo.fromMap(Map<String, dynamic>.from(item)))
              .toList() ??
          [],
      grantedPermissions: List<String>.from(map['grantedPermissions'] ?? []),
      missingPermissions: List<String>.from(map['missingPermissions'] ?? []),
      recommendations: List<String>.from(map['recommendations'] ?? []),
      error: map['error'],
    );
  }

  factory VendorAdaptationStatus.error(String error) {
    return VendorAdaptationStatus(
      vendor: 'UNKNOWN',
      romName: 'Unknown',
      romVersion: 'Unknown',
      androidVersion: 'Unknown',
      apiLevel: 0,
      brand: 'Unknown',
      model: 'Unknown',
      adaptationLevel: 'LIMITED',
      requiredPermissions: [],
      grantedPermissions: [],
      missingPermissions: [],
      recommendations: [],
      error: error,
    );
  }

  @override
  String toString() {
    return 'VendorAdaptationStatus(vendor: $vendor, romName: $romName, '
        'adaptationLevel: $adaptationLevel, granted: ${grantedPermissions.length}, '
        'missing: ${missingPermissions.length})';
  }
}

/// 厂商权限信息数据类
class VendorPermissionInfo {
  final String type;
  final String permission;
  final String displayName;
  final String description;
  final bool isRequired;
  final String requestMethod;

  const VendorPermissionInfo({
    required this.type,
    required this.permission,
    required this.displayName,
    required this.description,
    required this.isRequired,
    required this.requestMethod,
  });

  factory VendorPermissionInfo.fromMap(Map<String, dynamic> map) {
    return VendorPermissionInfo(
      type: map['type'] ?? '',
      permission: map['permission'] ?? '',
      displayName: map['displayName'] ?? '',
      description: map['description'] ?? '',
      isRequired: map['isRequired'] ?? false,
      requestMethod: map['requestMethod'] ?? 'MANUAL_GUIDE',
    );
  }
}

/// 厂商权限状态数据类
class VendorPermissionStatus {
  final String permissionType;
  final String status;
  final String message;
  final String? error;

  const VendorPermissionStatus({
    required this.permissionType,
    required this.status,
    required this.message,
    this.error,
  });

  factory VendorPermissionStatus.fromMap(Map<String, dynamic> map) {
    return VendorPermissionStatus(
      permissionType: map['permissionType'] ?? '',
      status: map['status'] ?? 'UNKNOWN',
      message: map['message'] ?? '',
      error: map['error'],
    );
  }

  factory VendorPermissionStatus.error(String permissionType, String error) {
    return VendorPermissionStatus(
      permissionType: permissionType,
      status: 'ERROR',
      message: '检查权限时发生错误',
      error: error,
    );
  }
}

/// 权限引导步骤数据类
class PermissionGuideStep {
  final String title;
  final String description;
  final String permissionType;
  final bool isRequired;
  final List<String> steps;

  const PermissionGuideStep({
    required this.title,
    required this.description,
    required this.permissionType,
    required this.isRequired,
    required this.steps,
  });
}
