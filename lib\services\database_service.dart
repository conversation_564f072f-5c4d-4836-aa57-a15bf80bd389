import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/focus_session.dart';
import '../models/user_settings.dart';
import '../models/achievement.dart';

/// 数据库服务 - Web兼容版本
/// 在Web平台使用SharedPreferences，在移动平台也使用SharedPreferences以保持一致性
class DatabaseService {
  static DatabaseService? _instance;
  static DatabaseService get instance => _instance ??= DatabaseService._();

  DatabaseService._();

  SharedPreferences? _prefs;
  bool _initialized = false;

  Future<void> init() async {
    if (_initialized) return;

    _prefs = await SharedPreferences.getInstance();
    _initialized = true;

    // 初始化默认设置
    await _initializeDefaultSettings();
  }

  SharedPreferences get prefs {
    if (_prefs == null || !_initialized) {
      throw Exception('Database not initialized. Call init() first.');
    }
    return _prefs!;
  }

  /// 初始化默认设置
  Future<void> _initializeDefaultSettings() async {
    // 设置默认用户设置
    final defaultSettings = {
      'default_duration': '25',
      'break_duration': '5',
      'long_break_duration': '15',
      'sessions_until_long_break': '4',
      'sound_enabled': 'true',
      'vibration_enabled': 'true',
      'theme_mode': 'system',
    };

    for (final entry in defaultSettings.entries) {
      final key = 'setting_${entry.key}';
      if (!prefs.containsKey(key)) {
        await prefs.setString(key, entry.value);
      }
    }

    // 初始化成就数据
    await _initializeAchievements();
  }

  /// 初始化成就数据
  Future<void> _initializeAchievements() async {
    const achievementsKey = 'achievements_initialized';
    if (prefs.getBool(achievementsKey) == true) {
      return; // 已经初始化过了
    }

    // 创建默认成就
    final defaultAchievements = [
      Achievement(
        id: 'first_session',
        title: '初次专注',
        description: '完成第一次专注会话',
        icon: '🎯',
        type: AchievementType.firstSession,
        targetValue: 1,
        isUnlocked: false,
      ),
      Achievement(
        id: 'focus_master',
        title: '专注大师',
        description: '累计完成100次专注会话',
        icon: '🏆',
        type: AchievementType.sessionCount,
        targetValue: 100,
        isUnlocked: false,
      ),
      Achievement(
        id: 'time_warrior',
        title: '时间战士',
        description: '累计专注时间达到50小时',
        icon: '⏰',
        type: AchievementType.totalTime,
        targetValue: 3000, // 50小时 = 3000分钟
        isUnlocked: false,
      ),
    ];

    // 保存成就数据
    for (final achievement in defaultAchievements) {
      await saveAchievement(achievement);
    }

    await prefs.setBool(achievementsKey, true);
  }

  // Focus Sessions CRUD operations
  Future<String> insertFocusSession(FocusSession session) async {
    final sessions = await getFocusSessions();
    final sessionWithId =
        session.copyWith(id: DateTime.now().millisecondsSinceEpoch.toString());
    sessions.add(sessionWithId);

    final sessionsJson = sessions.map((s) => s.toMap()).toList();
    await prefs.setString('focus_sessions', jsonEncode(sessionsJson));

    return sessionWithId.id;
  }

  Future<List<FocusSession>> getFocusSessions() async {
    final sessionsJson = prefs.getString('focus_sessions');
    if (sessionsJson == null) return [];

    final sessionsList = jsonDecode(sessionsJson) as List;
    return sessionsList.map((json) => FocusSession.fromMap(json)).toList();
  }

  Future<List<FocusSession>> getRecentSessions({int limit = 10}) async {
    final sessions = await getFocusSessions();
    sessions.sort((a, b) => b.startTime.compareTo(a.startTime));
    return sessions.take(limit).toList();
  }

  Future<FocusSession?> getFocusSession(String id) async {
    final sessions = await getFocusSessions();
    try {
      return sessions.firstWhere((s) => s.id == id);
    } catch (e) {
      return null;
    }
  }

  Future<void> updateFocusSession(FocusSession session) async {
    final sessions = await getFocusSessions();
    final index = sessions.indexWhere((s) => s.id == session.id);
    if (index != -1) {
      sessions[index] = session;
      final sessionsJson = sessions.map((s) => s.toMap()).toList();
      await prefs.setString('focus_sessions', jsonEncode(sessionsJson));
    }
  }

  Future<void> deleteFocusSession(String id) async {
    final sessions = await getFocusSessions();
    sessions.removeWhere((s) => s.id == id);
    final sessionsJson = sessions.map((s) => s.toMap()).toList();
    await prefs.setString('focus_sessions', jsonEncode(sessionsJson));
  }

  // User Settings CRUD operations
  Future<void> setSetting(String key, String value) async {
    await prefs.setString('setting_$key', value);
  }

  Future<String?> getSetting(String key) async {
    return prefs.getString('setting_$key');
  }

  Future<UserSettings> getUserSettings() async {
    return UserSettings(
      defaultDuration:
          int.tryParse(await getSetting('default_duration') ?? '25') ?? 25,
      breakDuration:
          int.tryParse(await getSetting('break_duration') ?? '5') ?? 5,
      longBreakDuration:
          int.tryParse(await getSetting('long_break_duration') ?? '15') ?? 15,
      sessionsUntilLongBreak:
          int.tryParse(await getSetting('sessions_until_long_break') ?? '4') ??
              4,
      soundEnabled: (await getSetting('sound_enabled') ?? 'true') == 'true',
      vibrationEnabled:
          (await getSetting('vibration_enabled') ?? 'true') == 'true',
      themeMode: await getSetting('theme_mode') ?? 'system',
    );
  }

  Future<void> updateUserSettings(UserSettings settings) async {
    await setSetting('default_duration', settings.defaultDuration.toString());
    await setSetting('break_duration', settings.breakDuration.toString());
    await setSetting(
        'long_break_duration', settings.longBreakDuration.toString());
    await setSetting('sessions_until_long_break',
        settings.sessionsUntilLongBreak.toString());
    await setSetting('sound_enabled', settings.soundEnabled.toString());
    await setSetting('vibration_enabled', settings.vibrationEnabled.toString());
    await setSetting('theme_mode', settings.themeMode);
  }

  // Achievements CRUD operations
  Future<void> saveAchievement(Achievement achievement) async {
    final achievements = await getAchievements();
    final index = achievements.indexWhere((a) => a.id == achievement.id);
    if (index != -1) {
      achievements[index] = achievement;
    } else {
      achievements.add(achievement);
    }

    final achievementsJson = achievements.map((a) => a.toMap()).toList();
    await prefs.setString('achievements', jsonEncode(achievementsJson));
  }

  Future<List<Achievement>> getAchievements() async {
    final achievementsJson = prefs.getString('achievements');
    if (achievementsJson == null) return [];

    final achievementsList = jsonDecode(achievementsJson) as List;
    return achievementsList.map((json) => Achievement.fromMap(json)).toList();
  }

  Future<List<Achievement>> getUnlockedAchievements() async {
    final achievements = await getAchievements();
    return achievements.where((a) => a.isUnlocked).toList();
  }

  Future<void> unlockAchievement(String achievementId) async {
    final achievements = await getAchievements();
    final index = achievements.indexWhere((a) => a.id == achievementId);
    if (index != -1 && !achievements[index].isUnlocked) {
      achievements[index] = achievements[index].copyWith(
        isUnlocked: true,
        unlockedAt: DateTime.now(),
      );
      await saveAchievement(achievements[index]);
    }
  }

  // Statistics methods
  Future<int> getTotalCompletedSessions() async {
    final sessions = await getFocusSessions();
    return sessions.where((s) => s.completed).length;
  }

  Future<int> getTotalFocusTime() async {
    final sessions = await getFocusSessions();
    return sessions
        .where((s) => s.completed)
        .fold<int>(0, (sum, session) => sum + session.durationMinutes);
  }

  Future<Map<String, int>> getSessionsByTaskType() async {
    final sessions = await getFocusSessions();
    final Map<String, int> result = {};

    for (final session in sessions.where((s) => s.completed)) {
      final taskType = session.taskType.toString().split('.').last;
      result[taskType] = (result[taskType] ?? 0) + 1;
    }

    return result;
  }

  Future<List<FocusSession>> getSessionsInDateRange(
      DateTime start, DateTime end) async {
    final sessions = await getFocusSessions();
    return sessions.where((session) {
      return session.startTime.isAfter(start) &&
          session.startTime.isBefore(end);
    }).toList();
  }

  // Cleanup methods
  Future<void> clearAllData() async {
    await prefs.clear();
    _initialized = false;
  }

  Future<void> clearSessions() async {
    await prefs.remove('focus_sessions');
  }

  Future<void> clearSettings() async {
    final keys =
        prefs.getKeys().where((key) => key.startsWith('setting_')).toList();
    for (final key in keys) {
      await prefs.remove(key);
    }
  }
}
