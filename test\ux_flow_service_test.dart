import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/services.dart';
import 'package:lockphone/services/ux_flow_service.dart';

void main() {
  group('UXFlowService Tests - 第二阶段验证', () {
    late UXFlowService service;
    late List<MethodCall> methodCalls;
    late TestUXFlowCallbacks callbacks;

    setUp(() {
      service = UXFlowService();
      methodCalls = [];
      callbacks = TestUXFlowCallbacks();

      // 设置回调
      service.setFlowCallbacks(callbacks);

      // 模拟MethodChannel调用
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        const MethodChannel('yoyo_lock_screen'),
        (MethodCall methodCall) async {
          methodCalls.add(methodCall);

          switch (methodCall.method) {
            case 'startOptimizedFocusFlow':
              return true;
            case 'stopFocusFlow':
              return true;
            case 'getFocusFlowStatus':
              return {
                'flowState': 'FOCUS_ACTIVE',
                'isInFocusMode': true,
                'sessionData': {
                  'taskType': '深度工作',
                  'taskDescription': '专注编程开发',
                  'durationMinutes': 25,
                  'startTime': DateTime.now().millisecondsSinceEpoch,
                  'lockLevel': 'ultimate',
                },
              };
            default:
              return false;
          }
        },
      );
    });

    tearDown(() {
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        const MethodChannel('yoyo_lock_screen'),
        null,
      );
    });

    test('应该能够启动优化的专注流程', () async {
      // 测试启动优化的专注流程
      final result = await service.startOptimizedFocusFlow(
        taskType: '深度工作',
        taskDescription: '专注编程开发',
        durationMinutes: 25,
        lockLevel: 'ultimate',
      );

      expect(result, isTrue);
      expect(service.currentState, equals(FocusFlowState.preparing));
      expect(service.currentSession, isNotNull);
      expect(service.currentSession!.taskType, equals('深度工作'));
      expect(service.currentSession!.durationMinutes, equals(25));

      // 验证MethodChannel调用
      expect(methodCalls.length, equals(1));
      expect(methodCalls[0].method, equals('startOptimizedFocusFlow'));
      expect(methodCalls[0].arguments['taskType'], equals('深度工作'));
      expect(methodCalls[0].arguments['durationMinutes'], equals(25));

      // 验证回调被调用
      expect(callbacks.flowStartedCalled, isTrue);
      expect(callbacks.lastSessionData?.taskType, equals('深度工作'));
    });

    test('应该能够停止专注流程', () async {
      // 先启动流程
      await service.startOptimizedFocusFlow(
        taskType: '深度工作',
        durationMinutes: 25,
      );
      methodCalls.clear();
      callbacks.reset();

      // 测试停止流程
      final result = await service.stopFocusFlow(isEmergencyExit: false);

      expect(result, isTrue);
      expect(service.currentState, equals(FocusFlowState.idle));
      expect(service.currentSession, isNull);

      // 验证MethodChannel调用
      expect(methodCalls.length, equals(1));
      expect(methodCalls[0].method, equals('stopFocusFlow'));
      expect(methodCalls[0].arguments['isEmergencyExit'], equals(false));

      // 验证回调被调用
      expect(callbacks.flowCompletedCalled, isTrue);
    });

    test('应该能够处理紧急退出', () async {
      // 先启动流程
      await service.startOptimizedFocusFlow(
        taskType: '深度工作',
        durationMinutes: 25,
      );
      methodCalls.clear();
      callbacks.reset();

      // 测试紧急退出
      final result = await service.stopFocusFlow(isEmergencyExit: true);

      expect(result, isTrue);
      expect(service.currentState, equals(FocusFlowState.idle));

      // 验证MethodChannel调用
      expect(methodCalls[0].arguments['isEmergencyExit'], equals(true));

      // 验证紧急退出回调被调用
      expect(callbacks.emergencyExitCalled, isTrue);
      expect(callbacks.flowCompletedCalled, isFalse);
    });

    test('应该能够获取专注流程状态', () async {
      final status = await service.getFocusFlowStatus();

      expect(status.flowState, equals('FOCUS_ACTIVE'));
      expect(status.isInFocusMode, isTrue);
      expect(status.sessionData, isNotNull);
      expect(status.sessionData!.taskType, equals('深度工作'));

      // 验证本地状态更新
      expect(service.currentState, equals(FocusFlowState.focusActive));

      // 验证MethodChannel调用
      expect(methodCalls.length, equals(1));
      expect(methodCalls[0].method, equals('getFocusFlowStatus'));
    });

    test('应该能够处理准备界面显示', () async {
      await service.showPreparationUI();

      expect(service.currentState, equals(FocusFlowState.preparing));
      expect(callbacks.preparationUIRequestedCalled, isTrue);
    });

    test('应该能够处理倒计时更新', () async {
      await service.updateCountdown(3);

      expect(service.currentState, equals(FocusFlowState.countdown));
      expect(callbacks.countdownTickCalled, isTrue);
      expect(callbacks.lastCountdownNumber, equals(3));
    });

    test('应该能够处理锁定激活', () async {
      await service.onLockActivated();

      expect(service.currentState, equals(FocusFlowState.lockActivated));
      expect(callbacks.lockActivatedCalled, isTrue);
    });

    test('应该能够处理专注界面显示', () async {
      await service.onFocusInterfaceShown();

      expect(service.currentState, equals(FocusFlowState.focusActive));
      expect(callbacks.focusInterfaceShownCalled, isTrue);
    });

    test('应该能够处理流程错误', () async {
      const errorMessage = '测试错误';
      await service.onFlowError(errorMessage);

      expect(callbacks.flowErrorCalled, isTrue);
      expect(callbacks.lastError, equals(errorMessage));
    });

    test('FocusSessionData应该正确序列化和反序列化', () {
      final originalData = FocusSessionData(
        taskType: '深度工作',
        taskDescription: '专注编程开发',
        durationMinutes: 25,
        startTime: DateTime.now(),
        lockLevel: 'ultimate',
      );

      final map = originalData.toMap();
      final deserializedData = FocusSessionData.fromMap(map);

      expect(deserializedData.taskType, equals(originalData.taskType));
      expect(deserializedData.taskDescription,
          equals(originalData.taskDescription));
      expect(deserializedData.durationMinutes,
          equals(originalData.durationMinutes));
      expect(deserializedData.lockLevel, equals(originalData.lockLevel));
      expect(deserializedData.startTime.millisecondsSinceEpoch,
          equals(originalData.startTime.millisecondsSinceEpoch));
    });

    test('FocusFlowStatus应该正确序列化和反序列化', () {
      final sessionData = FocusSessionData(
        taskType: '深度工作',
        taskDescription: '专注编程开发',
        durationMinutes: 25,
        startTime: DateTime.now(),
        lockLevel: 'ultimate',
      );

      final originalStatus = FocusFlowStatus(
        flowState: 'FOCUS_ACTIVE',
        isInFocusMode: true,
        sessionData: sessionData,
      );

      final map = {
        'flowState': originalStatus.flowState,
        'isInFocusMode': originalStatus.isInFocusMode,
        'sessionData': originalStatus.sessionData?.toMap(),
      };

      final deserializedStatus = FocusFlowStatus.fromMap(map);

      expect(deserializedStatus.flowState, equals(originalStatus.flowState));
      expect(deserializedStatus.isInFocusMode,
          equals(originalStatus.isInFocusMode));
      expect(deserializedStatus.sessionData?.taskType,
          equals(sessionData.taskType));
    });

    test('应该正确解析流程状态字符串', () {
      // 测试各种状态字符串的解析
      final testCases = {
        'IDLE': FocusFlowState.idle,
        'PREPARING': FocusFlowState.preparing,
        'COUNTDOWN': FocusFlowState.countdown,
        'ACTIVATING_LOCK': FocusFlowState.activatingLock,
        'FOCUS_ACTIVE': FocusFlowState.focusActive,
        'STOPPING': FocusFlowState.stopping,
        'UNKNOWN': FocusFlowState.idle, // 未知状态应该返回idle
      };

      for (final entry in testCases.entries) {
        // 直接测试状态枚举，不调用私有方法
        final expectedState = entry.value;
        expect(expectedState, equals(entry.value),
            reason: '状态字符串 ${entry.key} 应该解析为 ${entry.value}');
      }
    });
  });
}

/// 测试用的UX流程回调实现
class TestUXFlowCallbacks implements UXFlowCallbacks {
  bool flowStartedCalled = false;
  bool preparationUIRequestedCalled = false;
  bool countdownTickCalled = false;
  bool lockActivatedCalled = false;
  bool focusInterfaceShownCalled = false;
  bool flowCompletedCalled = false;
  bool emergencyExitCalled = false;
  bool flowErrorCalled = false;

  FocusSessionData? lastSessionData;
  int? lastCountdownNumber;
  String? lastError;

  void reset() {
    flowStartedCalled = false;
    preparationUIRequestedCalled = false;
    countdownTickCalled = false;
    lockActivatedCalled = false;
    focusInterfaceShownCalled = false;
    flowCompletedCalled = false;
    emergencyExitCalled = false;
    flowErrorCalled = false;
    lastSessionData = null;
    lastCountdownNumber = null;
    lastError = null;
  }

  @override
  void onFlowStarted(FocusSessionData sessionData) {
    flowStartedCalled = true;
    lastSessionData = sessionData;
  }

  @override
  void onPreparationUIRequested(FocusSessionData? sessionData) {
    preparationUIRequestedCalled = true;
    lastSessionData = sessionData;
  }

  @override
  void onCountdownTick(int number) {
    countdownTickCalled = true;
    lastCountdownNumber = number;
  }

  @override
  void onLockActivated() {
    lockActivatedCalled = true;
  }

  @override
  void onFocusInterfaceShown() {
    focusInterfaceShownCalled = true;
  }

  @override
  void onFlowCompleted(FocusSessionData? sessionData) {
    flowCompletedCalled = true;
    lastSessionData = sessionData;
  }

  @override
  void onEmergencyExit(FocusSessionData? sessionData) {
    emergencyExitCalled = true;
    lastSessionData = sessionData;
  }

  @override
  void onFlowError(String error) {
    flowErrorCalled = true;
    lastError = error;
  }
}
