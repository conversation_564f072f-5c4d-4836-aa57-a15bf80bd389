import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/services.dart';
import 'package:lockphone/services/vendor_adaptation_service.dart';

void main() {
  group('VendorAdaptationService Tests - 第三阶段验证', () {
    late VendorAdaptationService service;
    late List<MethodCall> methodCalls;

    setUp(() {
      service = VendorAdaptationService();
      methodCalls = [];

      // 模拟MethodChannel调用
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        const MethodChannel('yoyo_lock_screen'),
        (MethodCall methodCall) async {
          methodCalls.add(methodCall);

          switch (methodCall.method) {
            case 'getVendorAdaptationStatus':
              return {
                'vendor': 'XIAOMI',
                'romName': 'MIUI',
                'romVersion': 'V14.0.3.0',
                'androidVersion': '13',
                'apiLevel': 33,
                'brand': 'Xiaomi',
                'model': 'Mi 11',
                'adaptationLevel': 'PARTIAL',
                'requiredPermissions': [
                  {
                    'type': 'AUTO_START',
                    'permission': 'miui.permission.USE_INTERNAL_GENERAL_API',
                    'displayName': '自启动管理',
                    'description': '允许应用自动启动，确保专注功能正常',
                    'isRequired': true,
                    'requestMethod': 'SETTINGS_INTENT',
                  },
                  {
                    'type': 'FLOATING_WINDOW',
                    'permission': 'android.permission.SYSTEM_ALERT_WINDOW',
                    'displayName': '悬浮窗权限',
                    'description': '允许应用显示悬浮窗',
                    'isRequired': true,
                    'requestMethod': 'STANDARD_REQUEST',
                  },
                ],
                'grantedPermissions': ['FLOATING_WINDOW'],
                'missingPermissions': ['AUTO_START'],
                'recommendations': [
                  '建议开启自启动权限，确保专注模式能够正常启动',
                ],
              };
            case 'requestVendorPermissions':
              return true;
            case 'checkVendorPermission':
              return {
                'permissionType': methodCall.arguments['permissionType'],
                'status': 'NEED_MANUAL',
                'message': '需要手动设置此权限',
              };
            default:
              return false;
          }
        },
      );
    });

    tearDown(() {
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        const MethodChannel('yoyo_lock_screen'),
        null,
      );
    });

    test('应该能够获取厂商适配状态', () async {
      // 测试获取厂商适配状态
      final status = await service.getVendorAdaptationStatus();

      expect(status.vendor, equals('XIAOMI'));
      expect(status.romName, equals('MIUI'));
      expect(status.romVersion, equals('V14.0.3.0'));
      expect(status.androidVersion, equals('13'));
      expect(status.apiLevel, equals(33));
      expect(status.brand, equals('Xiaomi'));
      expect(status.model, equals('Mi 11'));
      expect(status.adaptationLevel, equals('PARTIAL'));
      expect(status.requiredPermissions.length, equals(2));
      expect(status.grantedPermissions, contains('FLOATING_WINDOW'));
      expect(status.missingPermissions, contains('AUTO_START'));
      expect(status.recommendations.length, equals(1));

      // 验证MethodChannel调用
      expect(methodCalls.length, equals(1));
      expect(methodCalls[0].method, equals('getVendorAdaptationStatus'));
    });

    test('应该能够申请厂商权限', () async {
      // 测试申请厂商权限
      final result = await service.requestVendorPermissions();

      expect(result, isTrue);

      // 验证MethodChannel调用
      expect(methodCalls.length,
          equals(2)); // requestVendorPermissions + getVendorAdaptationStatus
      expect(methodCalls[0].method, equals('requestVendorPermissions'));
      expect(methodCalls[1].method, equals('getVendorAdaptationStatus'));
    });

    test('应该能够检查特定权限状态', () async {
      // 测试检查特定权限
      const permissionType = 'AUTO_START';
      final status = await service.checkVendorPermission(permissionType);

      expect(status.permissionType, equals(permissionType));
      expect(status.status, equals('NEED_MANUAL'));
      expect(status.message, equals('需要手动设置此权限'));

      // 验证MethodChannel调用
      expect(methodCalls.length, equals(1));
      expect(methodCalls[0].method, equals('checkVendorPermission'));
      expect(
          methodCalls[0].arguments['permissionType'], equals(permissionType));
    });

    test('应该能够生成权限设置引导步骤', () async {
      // 先获取适配状态
      await service.getVendorAdaptationStatus();

      // 获取权限引导步骤
      final guideSteps = service.getPermissionGuideSteps();

      expect(guideSteps.length, equals(1)); // 只有AUTO_START权限缺失

      final autoStartStep = guideSteps.first;
      expect(autoStartStep.title, equals('自启动管理'));
      expect(autoStartStep.permissionType, equals('AUTO_START'));
      expect(autoStartStep.isRequired, isTrue);
      expect(autoStartStep.steps.length, greaterThan(0));

      // 验证小米MIUI的步骤
      expect(autoStartStep.steps.first, contains('安全中心'));
    });

    test('应该能够获取适配级别描述', () async {
      // 先获取适配状态
      await service.getVendorAdaptationStatus();

      // 获取适配级别描述
      final description = service.getAdaptationLevelDescription();

      expect(description, equals('部分适配 - 大部分功能正常工作'));
    });

    test('应该能够检查是否需要权限设置', () async {
      // 先获取适配状态
      await service.getVendorAdaptationStatus();

      // 检查是否需要权限设置
      final needsSetup = service.needsPermissionSetup;

      expect(needsSetup, isTrue); // 因为有缺失的AUTO_START权限
    });

    test('VendorAdaptationStatus应该正确序列化和反序列化', () {
      final originalData = {
        'vendor': 'OPPO',
        'romName': 'ColorOS',
        'romVersion': '13.1',
        'androidVersion': '13',
        'apiLevel': 33,
        'brand': 'OPPO',
        'model': 'Find X5',
        'adaptationLevel': 'FULL',
        'requiredPermissions': [
          {
            'type': 'AUTO_START',
            'permission': 'oppo.permission.OPPO_COMPONENT_SAFE',
            'displayName': '自启动管理',
            'description': '允许应用在后台自动启动',
            'isRequired': true,
            'requestMethod': 'SETTINGS_INTENT',
          }
        ],
        'grantedPermissions': ['AUTO_START', 'FLOATING_WINDOW'],
        'missingPermissions': [],
        'recommendations': [],
      };

      final status = VendorAdaptationStatus.fromMap(originalData);

      expect(status.vendor, equals('OPPO'));
      expect(status.romName, equals('ColorOS'));
      expect(status.romVersion, equals('13.1'));
      expect(status.adaptationLevel, equals('FULL'));
      expect(status.requiredPermissions.length, equals(1));
      expect(status.grantedPermissions, contains('AUTO_START'));
      expect(status.missingPermissions.isEmpty, isTrue);
    });

    test('VendorPermissionInfo应该正确序列化和反序列化', () {
      final originalData = {
        'type': 'FLOATING_WINDOW',
        'permission': 'android.permission.SYSTEM_ALERT_WINDOW',
        'displayName': '悬浮窗权限',
        'description': '允许应用显示悬浮窗',
        'isRequired': true,
        'requestMethod': 'STANDARD_REQUEST',
      };

      final permissionInfo = VendorPermissionInfo.fromMap(originalData);

      expect(permissionInfo.type, equals('FLOATING_WINDOW'));
      expect(permissionInfo.permission,
          equals('android.permission.SYSTEM_ALERT_WINDOW'));
      expect(permissionInfo.displayName, equals('悬浮窗权限'));
      expect(permissionInfo.description, equals('允许应用显示悬浮窗'));
      expect(permissionInfo.isRequired, isTrue);
      expect(permissionInfo.requestMethod, equals('STANDARD_REQUEST'));
    });

    test('VendorPermissionStatus应该正确序列化和反序列化', () {
      final originalData = {
        'permissionType': 'AUTO_START',
        'status': 'GRANTED',
        'message': '权限已授权',
      };

      final permissionStatus = VendorPermissionStatus.fromMap(originalData);

      expect(permissionStatus.permissionType, equals('AUTO_START'));
      expect(permissionStatus.status, equals('GRANTED'));
      expect(permissionStatus.message, equals('权限已授权'));
      expect(permissionStatus.error, isNull);
    });

    test('应该能够处理错误情况', () async {
      // 重新设置MethodChannel以模拟错误
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        const MethodChannel('yoyo_lock_screen'),
        (MethodCall methodCall) async {
          throw PlatformException(code: 'ERROR', message: '测试错误');
        },
      );

      // 测试获取适配状态时的错误处理
      final status = await service.getVendorAdaptationStatus();

      expect(status.vendor, equals('UNKNOWN'));
      expect(status.adaptationLevel, equals('LIMITED'));
      expect(status.error, isNotNull);
    });

    test('应该为不同厂商生成正确的权限设置步骤', () async {
      // 先获取适配状态以设置厂商信息
      await service.getVendorAdaptationStatus();

      // 获取权限引导步骤
      final guideSteps = service.getPermissionGuideSteps();

      // 验证引导步骤包含预期的厂商特定信息
      expect(guideSteps.length, greaterThanOrEqualTo(0));

      // 如果有引导步骤，验证其包含厂商特定的关键词
      if (guideSteps.isNotEmpty) {
        final firstStep = guideSteps.first;
        expect(firstStep.steps.length, greaterThan(0));

        // 根据当前测试的厂商（XIAOMI）验证步骤
        expect(firstStep.steps.first, contains('安全中心'));
      }
    });
  });
}

/// 第三阶段手动测试指南
/// 
/// 在不同厂商设备上进行以下测试：
/// 
/// 1. **厂商ROM检测测试**
///    - 在OPPO设备上验证ColorOS检测
///    - 在小米设备上验证MIUI检测
///    - 在华为设备上验证EMUI/HarmonyOS检测
///    - 在三星设备上验证OneUI检测
///    - 在Vivo设备上验证Funtouch检测
///    - 在Realme设备上验证Realme UI检测
/// 
/// 2. **权限检测测试**
///    - 验证自启动权限检测准确性
///    - 验证悬浮窗权限检测准确性
///    - 验证后台运行权限检测准确性
///    - 验证电池优化权限检测准确性
/// 
/// 3. **权限申请测试**
///    - 测试厂商特定权限申请流程
///    - 验证权限申请页面跳转正确性
///    - 测试权限申请失败的降级方案
/// 
/// 4. **锁定策略测试**
///    - 验证厂商优化的锁定策略执行
///    - 测试厂商特定功能的禁用效果
///    - 验证降级策略的有效性
/// 
/// 5. **兼容性测试**
///    - 在不同Android版本上测试（Android 7-14）
///    - 在不同厂商ROM版本上测试
///    - 验证未知厂商设备的降级处理
/// 
/// 6. **用户体验测试**
///    - 测试权限引导界面的易用性
///    - 验证权限设置步骤的准确性
///    - 测试适配状态显示的清晰度
/// 
/// 测试通过标准：
/// ✅ 所有单元测试通过
/// ✅ 厂商ROM检测准确率 > 95%
/// ✅ 权限检测准确率 > 90%
/// ✅ 权限申请成功率 > 85%
/// ✅ 锁定策略执行成功率 > 90%
/// ✅ 在主流厂商设备上兼容性良好
/// ✅ 用户权限设置引导清晰易懂
