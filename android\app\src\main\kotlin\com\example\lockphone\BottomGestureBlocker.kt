package com.example.lockphone

import android.app.Activity
import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.*
import android.widget.FrameLayout

/**
 * 专门的底部手势阻止器
 * 使用多层覆盖和实时监控来彻底阻止底部上划手势
 */
class BottomGestureBlocker(private val activity: Activity) {
    
    companion object {
        private const val TAG = "BottomGestureBlocker"
    }
    
    private val windowManager = activity.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    private var bottomOverlayView: View? = null
    private var fullOverlayView: View? = null
    private var isActive = false
    private var gestureMonitorHandler: Handler? = null
    private var gestureAttempts = 0
    
    /**
     * 启用底部手势阻止
     */
    fun enable(): Boolean {
        if (isActive) {
            Log.d(TAG, "底部手势阻止器已经启用")
            return true
        }

        try {
            Log.d(TAG, "🚫 启用底部手势阻止器")

            // 检查悬浮窗权限
            val hasOverlayPermission = Settings.canDrawOverlays(activity)
            Log.d(TAG, "悬浮窗权限状态: ${if (hasOverlayPermission) "✅ 已授权" else "❌ 未授权"}")

            if (hasOverlayPermission) {
                // 1. 创建底部专用覆盖层
                createBottomOverlay()

                // 2. 创建全屏监控覆盖层
                createFullScreenOverlay()

                Log.d(TAG, "✅ 覆盖层模式已启用")
            } else {
                Log.w(TAG, "⚠️ 无悬浮窗权限，使用替代防护模式")
                // 使用替代的手势防护方法
                enableAlternativeGestureProtection()
            }

            // 3. 启动手势监控（无论是否有权限都启动）
            startGestureMonitoring()

            isActive = true
            Log.d(TAG, "✅ 底部手势阻止器已启用")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "启用底部手势阻止器失败: ${e.message}")
            return false
        }
    }
    
    /**
     * 禁用底部手势阻止
     */
    fun disable() {
        if (!isActive) return
        
        try {
            Log.d(TAG, "禁用底部手势阻止器")
            
            // 停止监控
            gestureMonitorHandler?.removeCallbacksAndMessages(null)
            
            // 移除覆盖层
            removeOverlays()
            
            isActive = false
            gestureAttempts = 0
            Log.d(TAG, "底部手势阻止器已禁用")
        } catch (e: Exception) {
            Log.e(TAG, "禁用底部手势阻止器失败: ${e.message}")
        }
    }
    
    /**
     * 创建底部专用覆盖层
     */
    private fun createBottomOverlay() {
        removeBottomOverlay()
        
        val screenHeight = activity.resources.displayMetrics.heightPixels
        val bottomHeight = (screenHeight * 0.25f).toInt() // 底部25%区域
        
        bottomOverlayView = createBottomOverlayView()
        val params = createBottomOverlayParams(bottomHeight)
        
        try {
            windowManager.addView(bottomOverlayView, params)
            Log.d(TAG, "底部专用覆盖层已创建，高度: $bottomHeight")
        } catch (e: Exception) {
            Log.e(TAG, "创建底部覆盖层失败: ${e.message}")
            bottomOverlayView = null
        }
    }
    
    /**
     * 创建底部覆盖层视图
     */
    private fun createBottomOverlayView(): View {
        val view = FrameLayout(activity)
        view.setBackgroundColor(0x01000000) // 几乎透明
        
        // 超强的触摸事件拦截
        view.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    gestureAttempts++
                    Log.w(TAG, "🚫 底部区域触摸被拦截 - 第${gestureAttempts}次尝试")
                    
                    // 立即触发强化防护
                    triggerEnhancedProtection()
                }
                MotionEvent.ACTION_MOVE -> {
                    Log.w(TAG, "🚫 底部区域滑动被拦截")
                    triggerEnhancedProtection()
                }
                MotionEvent.ACTION_UP -> {
                    Log.w(TAG, "🚫 底部区域手势完全拦截")
                    triggerEnhancedProtection()
                }
            }
            true // 完全消费事件
        }
        
        view.isClickable = true
        view.isFocusable = false // 不抢夺焦点
        view.isFocusableInTouchMode = false
        
        return view
    }
    
    /**
     * 创建底部覆盖层参数 - 使用最高优先级
     */
    private fun createBottomOverlayParams(height: Int): WindowManager.LayoutParams {
        // 使用最高优先级的窗口类型
        val type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            @Suppress("DEPRECATION")
            WindowManager.LayoutParams.TYPE_SYSTEM_ERROR
        } else {
            @Suppress("DEPRECATION")
            WindowManager.LayoutParams.TYPE_SYSTEM_OVERLAY
        }

        return WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            height,
            type,
            // 关键：移除FLAG_NOT_FOCUSABLE，使覆盖层能够真正拦截事件
            WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                    WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS or
                    WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED or
                    WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH or
                    WindowManager.LayoutParams.FLAG_SPLIT_TOUCH or
                    WindowManager.LayoutParams.FLAG_LOCAL_FOCUS_MODE or
                    WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON,
            PixelFormat.TRANSLUCENT
        ).apply {
            gravity = Gravity.BOTTOM
            y = 0
            x = 0
            // 设置最高优先级
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
            }
        }
    }
    
    /**
     * 创建全屏监控覆盖层
     */
    private fun createFullScreenOverlay() {
        removeFullOverlay()
        
        fullOverlayView = createFullOverlayView()
        val params = createFullOverlayParams()
        
        try {
            windowManager.addView(fullOverlayView, params)
            Log.d(TAG, "全屏监控覆盖层已创建")
        } catch (e: Exception) {
            Log.e(TAG, "创建全屏覆盖层失败: ${e.message}")
            fullOverlayView = null
        }
    }
    
    /**
     * 创建全屏覆盖层视图
     */
    private fun createFullOverlayView(): View {
        val view = FrameLayout(activity)
        view.setBackgroundColor(0x00000000) // 完全透明
        
        // 监控所有触摸事件，特别关注向上滑动
        view.setOnTouchListener { _, event ->
            val screenHeight = activity.resources.displayMetrics.heightPixels
            val bottomZone = screenHeight * 0.3f
            
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    if (event.y > screenHeight - bottomZone) {
                        Log.w(TAG, "🔍 全屏监控检测到底部区域触摸")
                    }
                }
                MotionEvent.ACTION_MOVE -> {
                    // 检测向上滑动
                    if (event.historySize > 0) {
                        val deltaY = event.getHistoricalY(0) - event.y
                        if (deltaY > 20 && event.y > screenHeight - bottomZone) {
                            Log.w(TAG, "🚫 全屏监控拦截向上滑动手势")
                            triggerEnhancedProtection()
                            return@setOnTouchListener true
                        }
                    }
                }
            }
            false // 不消费事件，让底层处理
        }
        
        return view
    }
    
    /**
     * 创建全屏覆盖层参数
     */
    private fun createFullOverlayParams(): WindowManager.LayoutParams {
        val type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else {
            @Suppress("DEPRECATION")
            WindowManager.LayoutParams.TYPE_PHONE
        }
        
        return WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            type,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                    WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                    WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS or
                    WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH,
            PixelFormat.TRANSLUCENT
        ).apply {
            gravity = Gravity.TOP or Gravity.LEFT
        }
    }
    
    /**
     * 启动手势监控
     */
    private fun startGestureMonitoring() {
        gestureMonitorHandler = Handler(Looper.getMainLooper())
        
        val runnable = object : Runnable {
            override fun run() {
                if (isActive) {
                    // 每50ms检查一次
                    checkGestureAttempts()
                    gestureMonitorHandler?.postDelayed(this, 50)
                }
            }
        }
        
        gestureMonitorHandler?.post(runnable)
        Log.d(TAG, "手势监控已启动")
    }
    
    /**
     * 检查手势尝试
     */
    private fun checkGestureAttempts() {
        if (gestureAttempts > 0) {
            // 如果有手势尝试，重新创建覆盖层以确保完全覆盖
            if (gestureAttempts % 3 == 0) {
                Log.w(TAG, "🔄 重新创建覆盖层以强化防护")
                createBottomOverlay()
                createFullScreenOverlay()
            }
        }
    }
    
    /**
     * 触发增强防护
     */
    private fun triggerEnhancedProtection() {
        try {
            Log.w(TAG, "🛡️ 触发增强防护")
            
            // 立即重新创建覆盖层
            createBottomOverlay()
            
            // 通知LockScreenManager强化锁定
            val lockManager = LockScreenManager.getInstance()
            lockManager?.reinforceLock()
            
        } catch (e: Exception) {
            Log.e(TAG, "触发增强防护失败: ${e.message}")
        }
    }
    
    /**
     * 移除所有覆盖层
     */
    private fun removeOverlays() {
        removeBottomOverlay()
        removeFullOverlay()
    }
    
    /**
     * 移除底部覆盖层
     */
    private fun removeBottomOverlay() {
        bottomOverlayView?.let {
            try {
                windowManager.removeView(it)
            } catch (e: Exception) {
                Log.e(TAG, "移除底部覆盖层失败: ${e.message}")
            }
            bottomOverlayView = null
        }
    }
    
    /**
     * 移除全屏覆盖层
     */
    private fun removeFullOverlay() {
        fullOverlayView?.let {
            try {
                windowManager.removeView(it)
            } catch (e: Exception) {
                Log.e(TAG, "移除全屏覆盖层失败: ${e.message}")
            }
            fullOverlayView = null
        }
    }

    /**
     * 启用替代的手势防护模式（无需悬浮窗权限）
     */
    private fun enableAlternativeGestureProtection() {
        try {
            Log.d(TAG, "🛡️ 启用替代手势防护模式")

            // 1. 设置Activity的窗口标志来阻止手势
            activity.window.apply {
                // 隐藏导航栏和状态栏
                decorView.systemUiVisibility = (
                    View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
                    View.SYSTEM_UI_FLAG_FULLSCREEN or
                    View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY or
                    View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or
                    View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                )

                // 设置窗口标志阻止手势
                addFlags(
                    WindowManager.LayoutParams.FLAG_FULLSCREEN or
                    WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON or
                    WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD or
                    WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED or
                    WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON
                )

                // 尝试阻止手势导航
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                    attributes = attributes.apply {
                        layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
                    }
                }
            }

            // 2. 设置Activity的触摸拦截
            activity.findViewById<View>(android.R.id.content)?.setOnTouchListener { _, event ->
                val y = event.y
                val screenHeight = activity.resources.displayMetrics.heightPixels
                val bottomThreshold = screenHeight * 0.9f // 底部10%区域

                if (y > bottomThreshold) {
                    gestureAttempts++
                    Log.w(TAG, "🚫 替代模式：底部手势被拦截 - 第${gestureAttempts}次尝试")
                    triggerEnhancedProtection()
                    return@setOnTouchListener true // 消费事件
                }
                false // 允许其他区域的触摸
            }

            Log.d(TAG, "✅ 替代手势防护模式已启用")
        } catch (e: Exception) {
            Log.e(TAG, "启用替代手势防护失败: ${e.message}")
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        disable()
    }
}
