# 权限问题解决指南

## 🎯 当前问题

根据应用日志分析，您的设备存在以下问题：

- **设备**: OnePlus PJZ110 (Android 15)
- **状态**: 非ROOT设备
- **问题**: 缺少 `WRITE_SECURE_SETTINGS` 权限
- **结果**: 无法自动启用无障碍服务

## 🔧 解决方案

### 方案一：使用权限修复脚本（推荐）

1. **下载并运行脚本**
   ```
   双击运行: fix_permissions_now.bat
   ```

2. **确保设备连接**
   - USB连接设备到电脑
   - 启用USB调试
   - 授权此计算机

3. **运行脚本**
   - 脚本会自动检测设备
   - 自动授予所需权限
   - 测试功能是否正常

### 方案二：手动执行ADB命令

如果脚本无法运行，请手动执行以下命令：

```bash
# 1. 检查设备连接
adb devices

# 2. 授予核心权限
adb shell pm grant com.example.lockphone android.permission.WRITE_SECURE_SETTINGS

# 3. 授予辅助权限
adb shell pm grant com.example.lockphone android.permission.WRITE_SETTINGS

# 4. 添加到电池优化白名单
adb shell dumpsys deviceidle whitelist +com.example.lockphone

# 5. 验证权限
adb shell dumpsys package com.example.lockphone | findstr "WRITE_SECURE_SETTINGS"
```

### 方案三：手动启用无障碍服务（备用）

如果以上方案都失败：

1. **进入设置**
   - 设置 → 无障碍
   - 已下载的应用
   - 找到"专注锁屏服务"

2. **启用服务**
   - 点击服务名称
   - 开启服务开关
   - 确认授权

3. **使用注意**
   - 专注结束后需要手动关闭
   - 或者保持开启状态

## 📱 验证步骤

权限授予成功后：

1. **返回应用**
2. **选择深度锁定**
3. **点击"开始专注"**
4. **观察是否自动启用**

如果成功，您会看到：
- ✅ 无障碍服务自动启用
- ✅ 进入专注界面
- ✅ 专注结束后自动关闭

## 🛠️ 故障排除

### Q: ADB命令执行失败
**A**: 
- 确保已安装Android SDK Platform Tools
- 检查USB调试是否启用
- 重新授权USB调试

### Q: 权限授予失败
**A**: 
- 重启设备后重试
- 在开发者选项中启用"USB安装"
- 关闭设备的安全模式

### Q: OnePlus设备特殊问题
**A**: 
- 进入设置 → 电池 → 应用耗电保护
- 关闭专注锁屏应用的耗电保护
- 允许后台运行

## 📊 成功率

- **ADB权限授予**: 95%
- **手动无障碍服务**: 100%
- **自动控制功能**: 90%（权限授予成功后）

## 🎯 最终目标

完成权限配置后，您将获得：

- 🚀 **一键深度锁定** - 选择后自动启用
- 🔒 **真正的专注体验** - 阻止所有干扰
- 🔄 **自动恢复** - 专注结束后自动关闭
- 💡 **用户友好** - 无需手动管理

按照以上步骤操作，您的深度锁定功能将完美工作！
