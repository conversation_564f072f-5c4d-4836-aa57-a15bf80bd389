import 'package:flutter/material.dart';
import '../services/super_enhanced_lock.dart';
import '../services/focus_manager.dart' as focus_service;

/// 超级增强锁定测试页面
class SuperEnhancedLockTestScreen extends StatefulWidget {
  const SuperEnhancedLockTestScreen({super.key});

  @override
  State<SuperEnhancedLockTestScreen> createState() =>
      _SuperEnhancedLockTestScreenState();
}

class _SuperEnhancedLockTestScreenState
    extends State<SuperEnhancedLockTestScreen> {
  bool _isLockActive = false;
  int _backPressCount = 0;
  int _escapeAttempts = 0;
  String _statusMessage = '准备测试超级增强锁定';

  @override
  void initState() {
    super.initState();
    _updateStatus();
  }

  void _updateStatus() {
    setState(() {
      _isLockActive = SuperEnhancedLock.instance.isLockActive;
      _backPressCount = SuperEnhancedLock.instance.backPressCount;
      _escapeAttempts = SuperEnhancedLock.instance.escapeAttempts;
    });
  }

  Future<void> _enableSuperEnhancedLock() async {
    setState(() {
      _statusMessage = '正在启用超级增强锁定...';
    });

    try {
      final success = await SuperEnhancedLock.instance.enableLock();
      setState(() {
        if (success) {
          _statusMessage = '✅ 超级增强锁定已启用！';
        } else {
          _statusMessage = '❌ 超级增强锁定启用失败';
        }
      });
      _updateStatus();
    } catch (e) {
      setState(() {
        _statusMessage = '❌ 启用失败: $e';
      });
    }
  }

  Future<void> _disableSuperEnhancedLock() async {
    setState(() {
      _statusMessage = '正在禁用超级增强锁定...';
    });

    try {
      await SuperEnhancedLock.instance.disableLock();
      setState(() {
        _statusMessage = '✅ 超级增强锁定已禁用';
      });
      _updateStatus();
    } catch (e) {
      setState(() {
        _statusMessage = '❌ 禁用失败: $e';
      });
    }
  }

  Future<void> _testFocusManager() async {
    setState(() {
      _statusMessage = '正在测试专注管理器...';
    });

    try {
      // 测试增强锁定（会自动使用超级增强锁定）
      final success = await focus_service.FocusManager.instance.startFocus(
        taskType: '测试任务',
        durationMinutes: 1,
      );

      setState(() {
        if (success) {
          _statusMessage = '✅ 专注管理器测试成功！';
        } else {
          _statusMessage = '❌ 专注管理器测试失败';
        }
      });
      _updateStatus();
    } catch (e) {
      setState(() {
        _statusMessage = '❌ 测试失败: $e';
      });
    }
  }

  void _resetBackPressCount() {
    SuperEnhancedLock.instance.resetBackPressCount();
    _updateStatus();
    setState(() {
      _statusMessage = '返回键计数已重置';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('超级增强锁定测试'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 状态卡片
            Card(
              color: _isLockActive ? Colors.red.shade50 : Colors.green.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Icon(
                      _isLockActive ? Icons.lock : Icons.lock_open,
                      size: 48,
                      color: _isLockActive ? Colors.red : Colors.green,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _isLockActive ? '🔒 锁定已激活' : '🔓 锁定未激活',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: _isLockActive ? Colors.red : Colors.green,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _statusMessage,
                      style: const TextStyle(fontSize: 14),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // 统计信息
            Row(
              children: [
                Expanded(
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Column(
                        children: [
                          const Icon(Icons.keyboard_return,
                              color: Colors.orange),
                          const SizedBox(height: 4),
                          Text(
                            '$_backPressCount',
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.orange,
                            ),
                          ),
                          const Text(
                            '返回键次数',
                            style: TextStyle(fontSize: 12),
                          ),
                          Text(
                            '/ ${SuperEnhancedLock.instance.backPressThreshold}',
                            style: const TextStyle(
                                fontSize: 10, color: Colors.grey),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Column(
                        children: [
                          const Icon(Icons.warning, color: Colors.red),
                          const SizedBox(height: 4),
                          Text(
                            '$_escapeAttempts',
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.red,
                            ),
                          ),
                          const Text(
                            '逃逸尝试',
                            style: TextStyle(fontSize: 12),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // 控制按钮
            if (!_isLockActive) ...[
              ElevatedButton.icon(
                onPressed: _enableSuperEnhancedLock,
                icon: const Icon(Icons.security),
                label: const Text('启用超级增强锁定'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.all(16),
                ),
              ),
              const SizedBox(height: 12),
              ElevatedButton.icon(
                onPressed: _testFocusManager,
                icon: const Icon(Icons.psychology),
                label: const Text('测试专注管理器'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.deepPurple,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.all(16),
                ),
              ),
            ] else ...[
              ElevatedButton.icon(
                onPressed: _disableSuperEnhancedLock,
                icon: const Icon(Icons.lock_open),
                label: const Text('禁用超级增强锁定'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.all(16),
                ),
              ),
            ],

            const SizedBox(height: 12),

            // 重置按钮
            OutlinedButton.icon(
              onPressed: _resetBackPressCount,
              icon: const Icon(Icons.refresh),
              label: const Text('重置返回键计数'),
            ),

            const SizedBox(height: 20),

            // 说明文本
            Card(
              color: Colors.blue.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '📋 测试说明',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '1. 点击"启用超级增强锁定"测试直接锁定功能\n'
                      '2. 点击"测试专注管理器"测试集成锁定功能\n'
                      '3. 锁定启用后，尝试按返回键或使用手势退出\n'
                      '4. 需要连续按返回键15次才能退出\n'
                      '5. 观察逃逸尝试次数的变化',
                      style: TextStyle(fontSize: 14),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.orange.shade100,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: const Text(
                        '⚠️ 注意：超级增强锁定会阻止大部分退出手势，请确保了解如何退出',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.orange,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
