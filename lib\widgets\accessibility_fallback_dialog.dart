import 'package:flutter/material.dart';
import '../services/accessibility_fallback_service.dart';
import '../utils/constants.dart';

/// 无障碍服务回退选项对话框
class AccessibilityFallbackDialog extends StatefulWidget {
  final AccessibilityEnableResult result;
  final VoidCallback? onOptionSelected;
  final VoidCallback? onCancel;

  const AccessibilityFallbackDialog({
    super.key,
    required this.result,
    this.onOptionSelected,
    this.onCancel,
  });

  @override
  State<AccessibilityFallbackDialog> createState() =>
      _AccessibilityFallbackDialogState();
}

class _AccessibilityFallbackDialogState
    extends State<AccessibilityFallbackDialog> {
  FallbackOption? selectedOption;
  bool isExecuting = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Row(
        children: [
          Icon(Icons.build, color: AppColors.primary),
          SizedBox(width: 8),
          Text('选择解决方案'),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildStatusInfo(),
            const SizedBox(height: 16),
            _buildOptionsList(),
          ],
        ),
      ),
      actions: _buildActions(),
    );
  }

  Widget _buildStatusInfo() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.info, color: Colors.orange),
              const SizedBox(width: 8),
              Text(
                '自动启用失败',
                style: AppTextStyles.labelMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Colors.orange.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            widget.result.message,
            style: AppTextStyles.bodySmall,
          ),
          if (widget.result.systemPermissions != null) ...[
            const SizedBox(height: 8),
            _buildPermissionStatus(),
          ],
        ],
      ),
    );
  }

  Widget _buildPermissionStatus() {
    final permissions = widget.result.systemPermissions!;
    final hasWriteSecure =
        permissions['hasWriteSecureSettings'] as bool? ?? false;
    final isRootAvailable = permissions['isRootAvailable'] as bool? ?? false;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '权限状态:',
          style: AppTextStyles.bodySmall.copyWith(fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            Icon(
              hasWriteSecure ? Icons.check_circle : Icons.cancel,
              size: 16,
              color: hasWriteSecure ? Colors.green : Colors.red,
            ),
            const SizedBox(width: 4),
            const Text(
              '系统设置权限',
              style: AppTextStyles.bodySmall,
            ),
          ],
        ),
        Row(
          children: [
            Icon(
              isRootAvailable ? Icons.check_circle : Icons.cancel,
              size: 16,
              color: isRootAvailable ? Colors.green : Colors.red,
            ),
            const SizedBox(width: 4),
            const Text(
              'Root权限',
              style: AppTextStyles.bodySmall,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOptionsList() {
    if (widget.result.fallbackOptions.isEmpty) {
      return const Text('暂无可用的解决方案');
    }

    return Flexible(
      child: ListView.builder(
        shrinkWrap: true,
        itemCount: widget.result.fallbackOptions.length,
        itemBuilder: (context, index) {
          final option = widget.result.fallbackOptions[index];
          return _buildOptionCard(option);
        },
      ),
    );
  }

  Widget _buildOptionCard(FallbackOption option) {
    final isSelected = selectedOption == option;

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () {
          setState(() {
            selectedOption = option;
          });
        },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? AppColors.primary : Colors.transparent,
              width: 2,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    option.difficultyIcon,
                    color: option.difficultyColor,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      option.title,
                      style: AppTextStyles.labelMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: option.difficultyColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      option.difficultyText,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: option.difficultyColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                option.description,
                style: AppTextStyles.bodySmall,
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: Colors.grey.shade600,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    option.estimatedTime,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                  if (option.requiresComputer) ...[
                    const SizedBox(width: 16),
                    Icon(
                      Icons.computer,
                      size: 16,
                      color: Colors.grey.shade600,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '需要电脑',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ],
              ),
              if (option.warning != null) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.warning,
                        size: 16,
                        color: Colors.red,
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          option.warning!,
                          style: AppTextStyles.bodySmall.copyWith(
                            color: Colors.red.shade700,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              if (isSelected && option.steps.isNotEmpty) ...[
                const SizedBox(height: 12),
                const Divider(),
                const SizedBox(height: 8),
                Text(
                  '操作步骤:',
                  style: AppTextStyles.labelSmall.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                ...option.steps.asMap().entries.map((entry) {
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: 16,
                          height: 16,
                          decoration: BoxDecoration(
                            color: AppColors.primary.withOpacity(0.2),
                            shape: BoxShape.circle,
                          ),
                          child: Center(
                            child: Text(
                              '${entry.key + 1}',
                              style: const TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            entry.value,
                            style: AppTextStyles.bodySmall,
                          ),
                        ),
                      ],
                    ),
                  );
                }),
              ],
            ],
          ),
        ),
      ),
    );
  }

  List<Widget> _buildActions() {
    if (isExecuting) {
      return [
        const Padding(
          padding: EdgeInsets.all(16),
          child: CircularProgressIndicator(color: AppColors.primary),
        ),
      ];
    }

    return [
      TextButton(
        onPressed: () {
          Navigator.of(context).pop();
          widget.onCancel?.call();
        },
        child: const Text('取消'),
      ),
      if (selectedOption != null)
        ElevatedButton(
          onPressed: _executeSelectedOption,
          child: Text(_getActionButtonText()),
        ),
    ];
  }

  String _getActionButtonText() {
    if (selectedOption == null) return '选择方案';

    switch (selectedOption!.type) {
      case FallbackType.adbCommands:
        return '查看ADB命令';
      case FallbackType.rootAccess:
        return '尝试Root授权';
      case FallbackType.manualSetup:
        return '打开设置';
      case FallbackType.vendorSpecific:
        return '查看详细指导';
      case FallbackType.degradedMode:
        return '启用降级模式';
    }
  }

  Future<void> _executeSelectedOption() async {
    if (selectedOption == null) return;

    setState(() {
      isExecuting = true;
    });

    try {
      final success = await AccessibilityFallbackService.instance
          .executeFallbackOption(selectedOption!.type);

      setState(() {
        isExecuting = false;
      });

      if (mounted) {
        Navigator.of(context).pop();
        widget.onOptionSelected?.call();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success ? '操作成功' : '请按照指导完成设置'),
            backgroundColor: success ? Colors.green : Colors.orange,
          ),
        );
      }
    } catch (e) {
      setState(() {
        isExecuting = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('操作失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
