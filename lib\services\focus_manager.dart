import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'permission_manager.dart';
import 'optimized_permission_flow.dart';
import 'unified_lock_manager.dart';
import 'enhanced_basic_lock.dart';
import 'super_enhanced_lock.dart';
import '../models/focus_session.dart';
import 'database_service.dart';

/// 专注状态枚举
enum FocusState {
  idle, // 空闲状态
  preparing, // 准备中
  focusing, // 专注中
  paused, // 暂停中
  completed, // 已完成
  cancelled, // 已取消
}

/// 锁定级别枚举
enum LockLevel {
  basic, // 基础锁定：Flutter界面 + 返回键拦截
  enhanced, // 增强锁定：悬浮窗 + 手势拦截
  deep, // 深度锁定：无障碍服务 + 系统级拦截
}

/// YoYo日常风格的专注管理器
/// 提供简洁、可靠的专注功能
class FocusManager extends ChangeNotifier {
  static const MethodChannel _channel = MethodChannel('yoyo_lock_screen');

  static FocusManager? _instance;
  static FocusManager get instance => _instance ??= FocusManager._();

  FocusManager._();

  // 状态变量
  FocusState _state = FocusState.idle;
  FocusSession? _currentSession;
  Timer? _timer;
  int _remainingSeconds = 0;
  int _totalSeconds = 0;
  LockLevel _lockLevel = LockLevel.basic;

  // 紧急退出相关
  int _emergencyTapCount = 0;
  Timer? _emergencyTimer;
  static const int _emergencyTapThreshold = 10;
  static const Duration _emergencyTapWindow = Duration(seconds: 3);

  // Getters
  FocusState get state => _state;
  FocusSession? get currentSession => _currentSession;
  int get remainingSeconds => _remainingSeconds;
  int get totalSeconds => _totalSeconds;
  LockLevel get lockLevel => _lockLevel;
  double get progress => _totalSeconds > 0
      ? (_totalSeconds - _remainingSeconds) / _totalSeconds
      : 0.0;
  bool get isFocusing => _state == FocusState.focusing;
  bool get canStartFocus =>
      _state == FocusState.idle ||
      _state == FocusState.completed ||
      _state == FocusState.cancelled;

  /// 设置锁定级别
  void setLockLevel(LockLevel level) {
    _lockLevel = level;
    notifyListeners();
  }

  /// 开始专注会话（使用统一锁定管理器）
  Future<bool> startFocusWithUnifiedLock(
    BuildContext context, {
    required String taskType,
    required int durationMinutes,
    required LockLevel lockLevel,
  }) async {
    debugPrint(
        '开始专注会话（统一锁定）: $taskType, 时长: $durationMinutes分钟, 锁定级别: ${lockLevel.name}');

    // 如果当前状态是completed或cancelled，立即重置到idle
    if (_state == FocusState.completed || _state == FocusState.cancelled) {
      debugPrint('检测到完成/取消状态，自动重置到空闲状态');
      resetToIdle();
      debugPrint('重置后状态: $_state');
    }

    debugPrint('检查canStartFocus: $canStartFocus');
    if (!canStartFocus) {
      debugPrint('当前状态不允许开始专注: $_state');
      return false;
    }

    try {
      _state = FocusState.preparing;
      _lockLevel = lockLevel;
      notifyListeners();

      // 使用统一锁定管理器
      final lockResult =
          await UnifiedLockManager.instance.enableLock(context, lockLevel);

      if (!lockResult.success) {
        debugPrint('锁定启用失败: ${lockResult.message}');
        _state = FocusState.idle;
        notifyListeners();
        return false;
      }

      // 显示实际使用的锁定级别
      if (lockResult.actualLevel != lockResult.requestedLevel) {
        debugPrint(
            '锁定级别已降级: ${lockResult.requestedLevel?.name} -> ${lockResult.actualLevel.name}');
      }

      // 设置专注参数
      _totalSeconds = durationMinutes * 60;
      _remainingSeconds = _totalSeconds;

      // 启动计时器
      _startTimer();

      _state = FocusState.focusing;
      notifyListeners();

      debugPrint('专注会话启动成功，实际锁定级别: ${lockResult.actualLevel.name}');
      return true;
    } catch (e) {
      debugPrint('启动专注会话失败: $e');
      _state = FocusState.idle;
      notifyListeners();
      return false;
    }
  }

  /// 开始专注会话（原版）
  Future<bool> startFocus({
    required String taskType,
    required int durationMinutes,
  }) async {
    debugPrint('startFocus被调用，当前状态: $_state');

    // 如果当前状态是completed或cancelled，立即重置到idle
    if (_state == FocusState.completed || _state == FocusState.cancelled) {
      debugPrint('检测到完成/取消状态，自动重置到空闲状态');
      resetToIdle();
      debugPrint('重置后状态: $_state');
    }

    debugPrint('检查canStartFocus: $canStartFocus');
    if (!canStartFocus) {
      debugPrint('当前状态不允许开始专注: $_state');
      return false;
    }

    try {
      _state = FocusState.preparing;
      notifyListeners();

      // 检查权限
      final hasBasic = await PermissionManager.instance.hasBasicPermissions();
      if (!hasBasic) {
        _state = FocusState.idle;
        notifyListeners();
        throw Exception('缺少基础权限');
      }

      // 根据锁定级别检查相应权限
      bool canLock = true;
      switch (_lockLevel) {
        case LockLevel.enhanced:
          canLock = await PermissionManager.instance.hasEnhancedPermissions();
          break;
        case LockLevel.deep:
          // 优化：如果无障碍服务未启用，自动降级到超级增强锁定
          debugPrint('检查深度锁定权限');

          // 首先快速检查无障碍服务状态
          final accessibilityCheck = await OptimizedPermissionFlow.instance
              .quickPermissionCheck(PermissionType.accessibility);

          if (accessibilityCheck) {
            // 无障碍服务可用，使用真正的深度锁定
            canLock = true;
            debugPrint('无障碍服务可用，启用深度锁定');
          } else {
            // 无障碍服务不可用，检查是否可以使用超级增强锁定
            final overlayCheck =
                await PermissionManager.instance.hasEnhancedPermissions();
            if (overlayCheck) {
              // 降级到超级增强锁定（不需要无障碍服务）
              _lockLevel = LockLevel.enhanced; // 内部标记为增强锁定，但使用超级增强模式
              canLock = true;
              debugPrint('无障碍服务不可用，降级到超级增强锁定模式');
            } else {
              // 连悬浮窗权限都没有，降级到基础锁定
              _lockLevel = LockLevel.basic;
              canLock = true;
              debugPrint('权限不足，降级到基础锁定');
            }
          }
          break;
        case LockLevel.basic:
          canLock = true;
          break;
      }

      if (!canLock) {
        debugPrint('当前锁定级别所需权限不足，降级到基础锁定');
        _lockLevel = LockLevel.basic;
      }

      // 创建专注会话
      _currentSession = FocusSession(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        taskType: taskType,
        durationMinutes: durationMinutes,
        completed: false,
        startTime: DateTime.now(),
      );

      _totalSeconds = durationMinutes * 60;
      _remainingSeconds = _totalSeconds;

      // 启用锁屏
      final lockEnabled = await _enableLockScreen();
      if (!lockEnabled) {
        _state = FocusState.idle;
        _currentSession = null;
        notifyListeners();
        throw Exception('无法启用锁屏功能');
      }

      // 开始计时
      _state = FocusState.focusing;
      _startTimer();

      notifyListeners();
      debugPrint('专注会话开始: $taskType, $durationMinutes分钟');
      return true;
    } catch (e) {
      _state = FocusState.idle;
      _currentSession = null;
      notifyListeners();
      debugPrint('开始专注失败: $e');
      return false;
    }
  }

  /// 启用锁屏功能
  Future<bool> _enableLockScreen() async {
    try {
      debugPrint('🔒 FocusManager: 开始启用锁屏功能');
      debugPrint('🔒 FocusManager: 目标锁定级别: $_lockLevel');

      // 根据锁定级别选择不同的实现
      bool success = false;

      switch (_lockLevel) {
        case LockLevel.basic:
          debugPrint('🔒 FocusManager: 启用基础锁定模式');
          // 使用增强基础锁定
          success = await EnhancedBasicLock.instance.enableLock();
          debugPrint('🔒 FocusManager: 基础锁定结果: $success');
          break;

        case LockLevel.enhanced:
          debugPrint('🔒 FocusManager: 启用增强锁定模式');
          // 优先尝试超级增强锁定
          success = await SuperEnhancedLock.instance.enableLock();
          debugPrint('🔒 FocusManager: 超级增强锁定结果: $success');
          if (!success) {
            debugPrint('🔒 FocusManager: 超级增强锁定失败，降级到原生增强锁定');
            // 降级到原生增强锁定
            final result = await _channel.invokeMethod('enableLockScreen', {
              'level': 'enhanced',
            });
            success = result as bool? ?? false;
            debugPrint('🔒 FocusManager: 原生增强锁定结果: $success');
          }
          break;

        case LockLevel.deep:
          debugPrint('🔒 FocusManager: 启用深度锁定模式');
          // 深度锁定：同时启用Flutter层和原生层的最强保护
          debugPrint('🔒 FocusManager: 启用深度锁定：双重保护模式');

          // 1. 启用Flutter层的超级增强锁定作为额外保护
          final flutterSuccess = await SuperEnhancedLock.instance.enableLock();
          debugPrint(
              '🔒 FocusManager: Flutter层超级增强锁定: ${flutterSuccess ? "✅成功" : "❌失败"}');

          // 2. 启用原生层的深度锁定
          final result = await _channel.invokeMethod('enableLockScreen', {
            'level': 'deep',
          });
          final nativeSuccess = result as bool? ?? false;
          debugPrint(
              '🔒 FocusManager: 原生层深度锁定: ${nativeSuccess ? "✅成功" : "❌失败"}');

          // 只要有一个成功就算成功（双重保护）
          success = flutterSuccess || nativeSuccess;

          if (success) {
            debugPrint('✅ FocusManager: 深度锁定启用成功 - 双重保护已激活');
          } else {
            debugPrint('❌ FocusManager: 深度锁定启用失败 - 双重保护均失败');
          }
          break;
      }

      if (success) {
        debugPrint('✅ FocusManager: 锁屏启用成功: $_lockLevel');
      } else {
        debugPrint('❌ FocusManager: 锁屏启用失败: $_lockLevel');
      }

      return success;
    } catch (e) {
      debugPrint('❌ FocusManager: 启用锁屏异常: $e');
      return false;
    }
  }

  /// 禁用锁屏功能
  Future<bool> _disableLockScreen() async {
    try {
      debugPrint('禁用锁屏功能，级别: $_lockLevel');

      bool success = true;

      // 根据当前锁定级别禁用相应的锁定
      switch (_lockLevel) {
        case LockLevel.basic:
          await EnhancedBasicLock.instance.disableLock();
          break;

        case LockLevel.enhanced:
          // 检查是否使用了超级增强锁定
          if (SuperEnhancedLock.instance.isLockActive) {
            await SuperEnhancedLock.instance.disableLock();
          }
          // 同时禁用原生层锁定
          final result = await _channel.invokeMethod('disableLockScreen');
          success = result as bool? ?? false;
          break;

        case LockLevel.deep:
          // 深度锁定：禁用双重保护
          debugPrint('禁用深度锁定：双重保护模式');

          // 1. 禁用Flutter层的超级增强锁定
          if (SuperEnhancedLock.instance.isLockActive) {
            await SuperEnhancedLock.instance.disableLock();
            debugPrint('Flutter层超级增强锁定已禁用');
          }

          // 2. 禁用原生层的深度锁定
          final result = await _channel.invokeMethod('disableLockScreen');
          success = result as bool? ?? false;
          debugPrint('原生层深度锁定: ${success ? "禁用成功" : "禁用失败"}');
          break;
      }

      if (success) {
        debugPrint('锁屏禁用成功: $_lockLevel');
      } else {
        debugPrint('锁屏禁用失败: $_lockLevel');
      }

      return success;
    } catch (e) {
      debugPrint('禁用锁屏异常: $e');
      return false;
    }
  }

  /// 开始计时器
  void _startTimer() {
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_remainingSeconds > 0) {
        _remainingSeconds--;
        notifyListeners();
      } else {
        _completeSession();
      }
    });
  }

  /// 暂停专注
  Future<void> pauseFocus() async {
    if (_state != FocusState.focusing) return;

    _timer?.cancel();
    _state = FocusState.paused;
    notifyListeners();
    debugPrint('专注已暂停');
  }

  /// 恢复专注
  Future<void> resumeFocus() async {
    if (_state != FocusState.paused) return;

    _state = FocusState.focusing;
    _startTimer();
    notifyListeners();
    debugPrint('专注已恢复');
  }

  /// 完成专注会话
  Future<void> _completeSession() async {
    _timer?.cancel();

    if (_currentSession != null) {
      _currentSession!.completed = true;
      _currentSession!.endTime = DateTime.now();

      // 保存到数据库
      await DatabaseService.instance.insertFocusSession(_currentSession!);
      debugPrint('专注会话完成并保存');
    }

    await _disableLockScreen();

    // 如果使用了深度锁定，自动禁用无障碍服务
    if (_lockLevel == LockLevel.deep) {
      debugPrint('专注完成，尝试自动禁用无障碍服务');
      final autoDisabled =
          await PermissionManager.instance.disableAccessibilityServiceAuto();
      debugPrint('自动禁用无障碍服务结果: $autoDisabled');
    }

    _state = FocusState.completed;
    notifyListeners();

    // 延迟重置状态到空闲，允许用户看到完成状态
    Future.delayed(const Duration(milliseconds: 1500), () {
      if (_state == FocusState.completed) {
        resetToIdle();
      }
    });
  }

  /// 取消专注会话
  Future<void> cancelFocus() async {
    _timer?.cancel();

    if (_currentSession != null) {
      _currentSession!.completed = false;
      _currentSession!.endTime = DateTime.now();

      // 标记为取消并保存
      await DatabaseService.instance.insertFocusSession(_currentSession!);
      debugPrint('专注会话已取消');
    }

    await _disableLockScreen();

    // 如果使用了深度锁定，自动禁用无障碍服务
    if (_lockLevel == LockLevel.deep) {
      debugPrint('专注取消，尝试自动禁用无障碍服务');
      final autoDisabled =
          await PermissionManager.instance.disableAccessibilityServiceAuto();
      debugPrint('自动禁用无障碍服务结果: $autoDisabled');
    }

    _state = FocusState.cancelled;
    notifyListeners();

    // 延迟重置状态到空闲，允许用户看到取消状态
    Future.delayed(const Duration(milliseconds: 1500), () {
      if (_state == FocusState.cancelled) {
        resetToIdle();
      }
    });
  }

  /// 重置状态到空闲
  void resetToIdle() {
    _timer?.cancel();
    _emergencyTimer?.cancel();
    _state = FocusState.idle;
    _currentSession = null;
    _remainingSeconds = 0;
    _totalSeconds = 0;
    _emergencyTapCount = 0;
    notifyListeners();
  }

  /// 处理紧急退出点击
  void handleEmergencyTap() {
    if (_state != FocusState.focusing) return;

    _emergencyTapCount++;
    debugPrint('紧急退出点击: $_emergencyTapCount/$_emergencyTapThreshold');

    // 重置计时器
    _emergencyTimer?.cancel();
    _emergencyTimer = Timer(_emergencyTapWindow, () {
      _emergencyTapCount = 0;
    });

    // 检查是否达到阈值
    if (_emergencyTapCount >= _emergencyTapThreshold) {
      _emergencyExit();
    }

    notifyListeners();
  }

  /// 紧急退出
  Future<void> _emergencyExit() async {
    debugPrint('触发紧急退出');
    _emergencyTapCount = 0;
    _emergencyTimer?.cancel();
    await cancelFocus();
  }

  /// 获取紧急退出进度
  double get emergencyExitProgress =>
      _emergencyTapCount / _emergencyTapThreshold;

  /// 格式化时间显示
  String formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  /// 获取当前专注时间显示
  String get currentTimeDisplay => formatTime(_remainingSeconds);

  /// 获取已专注时间
  int get focusedSeconds => _totalSeconds - _remainingSeconds;

  /// 获取已专注时间显示
  String get focusedTimeDisplay => formatTime(focusedSeconds);

  /// 获取状态描述
  String get stateDescription {
    switch (_state) {
      case FocusState.idle:
        return '准备开始专注';
      case FocusState.preparing:
        return '正在准备...';
      case FocusState.focusing:
        return '专注进行中';
      case FocusState.paused:
        return '专注已暂停';
      case FocusState.completed:
        return '专注已完成';
      case FocusState.cancelled:
        return '专注已取消';
    }
  }

  /// 获取锁定级别描述
  String get lockLevelDescription {
    switch (_lockLevel) {
      case LockLevel.basic:
        return '基础锁定';
      case LockLevel.enhanced:
        return '增强锁定';
      case LockLevel.deep:
        return '深度锁定';
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    _emergencyTimer?.cancel();
    super.dispose();
  }
}
