import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:lockphone/services/permission_cache_service.dart';
import 'package:lockphone/services/permission_manager.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  group('PermissionCacheService Tests', () {
    late PermissionCacheService cacheService;

    setUp(() async {
      // 设置测试环境
      SharedPreferences.setMockInitialValues({});
      cacheService = PermissionCacheService.instance;
      await cacheService.init();

      // 模拟方法通道
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        const MethodChannel('yoyo_lock_screen'),
        (MethodCall methodCall) async {
          switch (methodCall.method) {
            case 'checkPermission':
              // 默认返回拒绝状态用于测试
              return 'denied';
            default:
              return null;
          }
        },
      );
    });

    tearDown(() async {
      // 清理测试数据
      await cacheService.clearAllPermissionCache();
    });

    test('should cache permission status correctly', () async {
      // 测试权限状态缓存
      await cacheService.cachePermissionStatus(
        PermissionType.accessibility,
        PermissionStatus.granted,
      );

      final cachedStatus = await cacheService.getCachedPermissionStatus(
        PermissionType.accessibility,
      );

      expect(cachedStatus, PermissionStatus.granted);
    });

    test('should return null for expired cache', () async {
      // 测试缓存过期
      await cacheService.cachePermissionStatus(
        PermissionType.accessibility,
        PermissionStatus.granted,
      );

      // 验证缓存存在
      var cachedStatus = await cacheService.getCachedPermissionStatus(
        PermissionType.accessibility,
      );
      expect(cachedStatus, PermissionStatus.granted);

      // 模拟缓存过期（通过直接修改时间戳到过去）
      final prefs = await SharedPreferences.getInstance();
      final expiredTime =
          DateTime.now().millisecondsSinceEpoch - (6 * 60 * 1000); // 6分钟前
      await prefs.setInt('permission_last_check_accessibility', expiredTime);

      // 现在缓存应该过期，返回null
      cachedStatus = await cacheService.getCachedPermissionStatus(
        PermissionType.accessibility,
      );

      expect(cachedStatus, isNull);
    });

    test('should record and check user consent', () async {
      // 测试用户同意记录
      expect(
        await cacheService.hasUserConsent(PermissionType.accessibility),
        false,
      );

      await cacheService.recordUserConsent(PermissionType.accessibility);

      expect(
        await cacheService.hasUserConsent(PermissionType.accessibility),
        true,
      );
    });

    test('should record and check auto enable attempts', () async {
      // 测试自动启用尝试记录
      expect(
        await cacheService.hasAutoEnableAttempted(PermissionType.accessibility),
        false,
      );

      await cacheService.recordAutoEnableAttempt(PermissionType.accessibility);

      expect(
        await cacheService.hasAutoEnableAttempted(PermissionType.accessibility),
        true,
      );
    });

    test('should perform smart permission check correctly', () async {
      // 确保测试开始时状态是干净的
      await cacheService.resetPermissionState(PermissionType.accessibility);

      // 测试智能权限检查 - 新用户场景
      var result =
          await cacheService.smartPermissionCheck(PermissionType.accessibility);

      expect(result.currentStatus, PermissionStatus.denied); // 假设默认为拒绝
      expect(result.needsRequest, true);
      expect(result.shouldShowRationale, true);
      expect(result.hasUserConsent, false);
      expect(result.hasAutoEnableAttempted, false);

      // 模拟用户同意
      await cacheService.recordUserConsent(PermissionType.accessibility);

      result =
          await cacheService.smartPermissionCheck(PermissionType.accessibility);
      expect(result.hasUserConsent, true);
      expect(result.canAutoEnable, true);
      expect(result.shouldShowRationale, false);

      // 模拟自动启用尝试
      await cacheService.recordAutoEnableAttempt(PermissionType.accessibility);

      result =
          await cacheService.smartPermissionCheck(PermissionType.accessibility);
      expect(result.hasAutoEnableAttempted, true);
      expect(result.canAutoEnable, false);
    });

    test('should clear permission cache correctly', () async {
      // 测试缓存清除
      await cacheService.cachePermissionStatus(
        PermissionType.accessibility,
        PermissionStatus.granted,
      );
      await cacheService.recordUserConsent(PermissionType.accessibility);

      // 验证缓存存在
      expect(
        await cacheService
            .getCachedPermissionStatus(PermissionType.accessibility),
        PermissionStatus.granted,
      );
      expect(
        await cacheService.hasUserConsent(PermissionType.accessibility),
        true,
      );

      // 清除特定权限缓存
      await cacheService.clearPermissionCache(PermissionType.accessibility);

      // 验证权限状态缓存被清除，但用户同意状态保留
      expect(
        await cacheService
            .getCachedPermissionStatus(PermissionType.accessibility),
        isNull,
      );
      expect(
        await cacheService.hasUserConsent(PermissionType.accessibility),
        true,
      );
    });

    test('should reset permission state completely', () async {
      // 测试完全重置权限状态
      await cacheService.cachePermissionStatus(
        PermissionType.accessibility,
        PermissionStatus.granted,
      );
      await cacheService.recordUserConsent(PermissionType.accessibility);
      await cacheService.recordAutoEnableAttempt(PermissionType.accessibility);

      // 重置权限状态
      await cacheService.resetPermissionState(PermissionType.accessibility);

      // 验证所有状态都被清除
      expect(
        await cacheService
            .getCachedPermissionStatus(PermissionType.accessibility),
        isNull,
      );
      expect(
        await cacheService.hasUserConsent(PermissionType.accessibility),
        false,
      );
      expect(
        await cacheService.hasAutoEnableAttempted(PermissionType.accessibility),
        false,
      );
    });

    test('should handle multiple permission types independently', () async {
      // 测试多种权限类型的独立处理
      await cacheService.cachePermissionStatus(
        PermissionType.accessibility,
        PermissionStatus.granted,
      );
      await cacheService.cachePermissionStatus(
        PermissionType.deviceAdmin,
        PermissionStatus.denied,
      );

      expect(
        await cacheService
            .getCachedPermissionStatus(PermissionType.accessibility),
        PermissionStatus.granted,
      );
      expect(
        await cacheService
            .getCachedPermissionStatus(PermissionType.deviceAdmin),
        PermissionStatus.denied,
      );

      // 清除一个权限不应影响另一个
      await cacheService.clearPermissionCache(PermissionType.accessibility);

      expect(
        await cacheService
            .getCachedPermissionStatus(PermissionType.accessibility),
        isNull,
      );
      expect(
        await cacheService
            .getCachedPermissionStatus(PermissionType.deviceAdmin),
        PermissionStatus.denied,
      );
    });
  });

  group('Permission Cache Integration Tests', () {
    test('should improve user experience by avoiding repeated prompts',
        () async {
      // 这是一个集成测试的示例，验证用户体验改进
      final cacheService = PermissionCacheService.instance;
      await cacheService.init();

      // 模拟用户首次使用 - 需要显示说明
      var result =
          await cacheService.smartPermissionCheck(PermissionType.accessibility);
      expect(result.shouldShowRationale, true);

      // 模拟用户同意后 - 不再显示说明，可以尝试自动启用
      await cacheService.recordUserConsent(PermissionType.accessibility);
      result =
          await cacheService.smartPermissionCheck(PermissionType.accessibility);
      expect(result.shouldShowRationale, false);
      expect(result.canAutoEnable, true);

      // 模拟自动启用尝试后 - 直接跳转设置
      await cacheService.recordAutoEnableAttempt(PermissionType.accessibility);
      result =
          await cacheService.smartPermissionCheck(PermissionType.accessibility);
      expect(result.canAutoEnable, false);
      expect(result.needsRequest, true);
    });
  });
}
