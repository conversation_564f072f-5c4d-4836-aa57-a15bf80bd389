import 'package:flutter/material.dart';
import '../services/permission_manager.dart';
import '../services/permission_status_service.dart';
import '../services/focus_manager.dart' as focus_service;

/// 权限辅助工具类
/// 提供简化的权限检查和处理方法
class PermissionHelper {
  /// 检查是否可以启动专注模式
  static Future<bool> canStartFocusMode() async {
    final summary = await PermissionStatusService.instance.getPermissionSummary();
    return summary.canStartFocus;
  }

  /// 获取推荐的锁定级别
  static Future<focus_service.LockLevel> getRecommendedLockLevel() async {
    return await PermissionStatusService.instance.getMaxAvailableLockLevel();
  }

  /// 获取权限状态描述
  static Future<String> getPermissionStatusDescription() async {
    return await PermissionStatusService.instance.getProtectionDescription();
  }

  /// 检查特定锁定级别是否可用
  static Future<bool> isLockLevelAvailable(focus_service.LockLevel level) async {
    return await PermissionStatusService.instance.canUseLockLevel(level);
  }

  /// 获取缺失的必需权限
  static Future<List<PermissionType>> getMissingRequiredPermissions() async {
    final summary = await PermissionStatusService.instance.getPermissionSummary();
    return summary.missingPermissions;
  }

  /// 获取可选的增强权限
  static Future<List<PermissionType>> getOptionalEnhancementPermissions() async {
    final summary = await PermissionStatusService.instance.getPermissionSummary();
    return summary.optionalPermissions;
  }

  /// 显示权限状态摘要
  static Future<void> showPermissionSummary(BuildContext context) async {
    final summary = await PermissionStatusService.instance.getPermissionSummary();
    
    if (!context.mounted) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('权限状态'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('防护级别: ${summary.protectionDescription}'),
            const SizedBox(height: 8),
            Text('最高可用级别: ${_getLockLevelName(summary.maxAvailableLevel)}'),
            const SizedBox(height: 8),
            Text('可以开始专注: ${summary.canStartFocus ? "是" : "否"}'),
            const SizedBox(height: 16),
            const Text('权限详情:', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            _buildPermissionStatus('存储权限', summary.storage),
            _buildPermissionStatus('无障碍服务', summary.accessibility),
            _buildPermissionStatus('设备管理员', summary.deviceAdmin),
            _buildPermissionStatus('通知权限', summary.notification),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 构建权限状态显示
  static Widget _buildPermissionStatus(String name, PermissionStatus status) {
    final isGranted = status == PermissionStatus.granted;
    return Row(
      children: [
        Icon(
          isGranted ? Icons.check_circle : Icons.cancel,
          color: isGranted ? Colors.green : Colors.red,
          size: 16,
        ),
        const SizedBox(width: 8),
        Text('$name: ${isGranted ? "已授权" : "未授权"}'),
      ],
    );
  }

  /// 获取锁定级别名称
  static String _getLockLevelName(focus_service.LockLevel level) {
    switch (level) {
      case focus_service.LockLevel.basic:
        return '基础锁定';
      case focus_service.LockLevel.enhanced:
        return '增强锁定';
      case focus_service.LockLevel.deep:
        return '深度锁定';
    }
  }

  /// 获取权限类型名称
  static String getPermissionTypeName(PermissionType type) {
    switch (type) {
      case PermissionType.storage:
        return '存储权限';
      case PermissionType.accessibility:
        return '无障碍服务';
      case PermissionType.deviceAdmin:
        return '设备管理员权限';
      case PermissionType.notification:
        return '通知权限';
    }
  }

  /// 获取权限类型描述
  static String getPermissionTypeDescription(PermissionType type) {
    switch (type) {
      case PermissionType.storage:
        return '保存专注记录和应用设置';
      case PermissionType.accessibility:
        return '系统级手势拦截，增强防护效果';
      case PermissionType.deviceAdmin:
        return '终极Kiosk模式，最强锁定防护';
      case PermissionType.notification:
        return '发送专注完成提醒和重要通知';
    }
  }

  /// 获取权限类型图标
  static IconData getPermissionTypeIcon(PermissionType type) {
    switch (type) {
      case PermissionType.storage:
        return Icons.storage;
      case PermissionType.accessibility:
        return Icons.accessibility;
      case PermissionType.deviceAdmin:
        return Icons.admin_panel_settings;
      case PermissionType.notification:
        return Icons.notifications;
    }
  }

  /// 检查并请求必需权限
  static Future<bool> ensureRequiredPermissions(BuildContext context) async {
    final missing = await getMissingRequiredPermissions();
    
    if (missing.isEmpty) {
      return true; // 所有必需权限都已授权
    }

    if (!context.mounted) return false;

    // 显示权限请求对话框
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('需要权限'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('专注模式需要以下权限才能正常工作:'),
            const SizedBox(height: 16),
            ...missing.map((type) => ListTile(
              leading: Icon(getPermissionTypeIcon(type)),
              title: Text(getPermissionTypeName(type)),
              subtitle: Text(getPermissionTypeDescription(type)),
            )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              // 请求权限
              bool allGranted = true;
              for (final type in missing) {
                final granted = await PermissionManager.instance.requestPermission(type);
                if (!granted) {
                  allGranted = false;
                  break;
                }
              }
              
              if (context.mounted) {
                Navigator.of(context).pop(allGranted);
              }
            },
            child: const Text('授权'),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  /// 刷新权限状态
  static Future<void> refreshPermissionStatus() async {
    await PermissionStatusService.instance.refreshPermissionStatus();
  }

  /// 清除权限缓存
  static void clearPermissionCache() {
    PermissionStatusService.instance.clearCache();
  }
}
