import 'package:flutter/material.dart';
import '../services/permission_manager.dart';
import '../utils/constants.dart';

/// 增强版无障碍服务权限对话框
class EnhancedAccessibilityPermissionDialog extends StatefulWidget {
  final VoidCallback? onPermissionGranted;
  final VoidCallback? onPermissionDenied;

  const EnhancedAccessibilityPermissionDialog({
    super.key,
    this.onPermissionGranted,
    this.onPermissionDenied,
  });

  @override
  State<EnhancedAccessibilityPermissionDialog> createState() =>
      _EnhancedAccessibilityPermissionDialogState();
}

class _EnhancedAccessibilityPermissionDialogState
    extends State<EnhancedAccessibilityPermissionDialog> {
  bool _isLoading = false;
  AccessibilityServiceGuidanceResult? _guidanceResult;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Row(
        children: [
          Icon(Icons.accessibility_new, color: AppColors.primary),
          SizedBox(width: 8),
          Text('无障碍服务权限'),
        ],
      ),
      content: _buildContent(),
      actions: _buildActions(),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircularProgressIndicator(color: AppColors.primary),
          SizedBox(height: 16),
          Text('正在检测设备状态和权限...'),
        ],
      );
    }

    if (_guidanceResult == null) {
      return _buildInitialContent();
    }

    if (_guidanceResult!.success) {
      return _buildSuccessContent();
    }

    return _buildGuidanceContent();
  }

  Widget _buildInitialContent() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '深度锁定模式需要启用无障碍服务来确保专注效果：',
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 12),
        const Text('✅ 阻止所有系统手势和按键'),
        const Text('✅ 防止切换到其他应用'),
        const Text('✅ 确保真正的专注体验'),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            '我们会尝试自动启用服务，如果失败会提供详细的设置指导。',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.primary,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSuccessContent() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Icon(
          Icons.check_circle,
          color: Colors.green,
          size: 48,
        ),
        const SizedBox(height: 16),
        Text(
          '无障碍服务已成功启用！',
          style: AppTextStyles.titleMedium.copyWith(
            color: Colors.green,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          _guidanceResult!.autoEnableSuccess ? '系统已自动完成配置' : '感谢您完成手动设置',
          style: AppTextStyles.bodyMedium,
        ),
      ],
    );
  }

  Widget _buildGuidanceContent() {
    final result = _guidanceResult!;

    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (result.autoEnableAttempted && !result.autoEnableSuccess) ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(Icons.info, color: Colors.orange),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '自动启用失败，需要手动设置',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: Colors.orange.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],
          _buildDeviceInfo(),
          const SizedBox(height: 16),
          _buildBasicSteps(),
          if (result.vendorSpecificGuidance != null) ...[
            const SizedBox(height: 16),
            _buildVendorSpecificGuidance(),
          ],
          if (result.alternatives.isNotEmpty) ...[
            const SizedBox(height: 16),
            _buildAlternatives(),
          ],
        ],
      ),
    );
  }

  Widget _buildDeviceInfo() {
    final deviceInfo = _guidanceResult!.deviceInfo;
    if (deviceInfo == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.surface.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '设备信息',
            style: AppTextStyles.labelMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text('品牌: ${deviceInfo['manufacturer']} ${deviceInfo['brand']}'),
          Text('型号: ${deviceInfo['model']}'),
          Text('系统: Android ${deviceInfo['androidVersion']}'),
        ],
      ),
    );
  }

  Widget _buildBasicSteps() {
    final steps = _guidanceResult!.basicSteps;
    if (steps.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '设置步骤',
          style: AppTextStyles.titleSmall.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        ...steps.asMap().entries.map((entry) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 20,
                  height: 20,
                  decoration: const BoxDecoration(
                    color: AppColors.primary,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      '${entry.key + 1}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(child: Text(entry.value)),
              ],
            ),
          );
        }),
      ],
    );
  }

  Widget _buildVendorSpecificGuidance() {
    final vendorGuidance = _guidanceResult!.vendorSpecificGuidance!;
    final vendor = vendorGuidance['vendor'] as String;
    final additionalSteps = vendorGuidance['additionalSteps'] as List<dynamic>;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$vendor设备特别提醒',
            style: AppTextStyles.labelMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: Colors.blue.shade700,
            ),
          ),
          const SizedBox(height: 8),
          ...additionalSteps.map((step) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('• '),
                    Expanded(child: Text(step.toString())),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildAlternatives() {
    final alternatives = _guidanceResult!.alternatives;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '其他解决方案',
            style: AppTextStyles.labelMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          ...alternatives.map((alt) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('• '),
                    Expanded(child: Text(alt)),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  List<Widget> _buildActions() {
    if (_isLoading) {
      return [];
    }

    if (_guidanceResult == null) {
      return [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
            widget.onPermissionDenied?.call();
          },
          child: const Text('稍后设置'),
        ),
        ElevatedButton(
          onPressed: _requestPermissionWithGuidance,
          child: const Text('开始设置'),
        ),
      ];
    }

    if (_guidanceResult!.success) {
      return [
        ElevatedButton(
          onPressed: () {
            Navigator.of(context).pop();
            widget.onPermissionGranted?.call();
          },
          child: const Text('继续'),
        ),
      ];
    }

    return [
      TextButton(
        onPressed: () {
          Navigator.of(context).pop();
          widget.onPermissionDenied?.call();
        },
        child: const Text('取消'),
      ),
      if (_guidanceResult!.settingsIntentSuccess)
        ElevatedButton(
          onPressed: _checkPermissionStatus,
          child: const Text('我已设置完成'),
        ),
    ];
  }

  Future<void> _requestPermissionWithGuidance() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await PermissionManager.instance
          .requestAccessibilityServiceWithGuidance();

      setState(() {
        _guidanceResult = result;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('检测失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _checkPermissionStatus() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 强制刷新权限状态，绕过缓存
      final status = await PermissionManager.instance
          .forceCheckPermission(PermissionType.accessibility);

      debugPrint('强制检查权限状态结果: ${status.name}');

      if (status == PermissionStatus.granted) {
        setState(() {
          _guidanceResult = const AccessibilityServiceGuidanceResult(
            success: true,
            isCurrentlyEnabled: true,
            autoEnableAttempted: false,
            autoEnableSuccess: false,
            requiresManualSetup: false,
          );
          _isLoading = false;
        });

        // 显示成功提示
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('✅ 无障碍服务已成功启用！'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
        }

        // 延迟一下再关闭对话框，让用户看到成功提示
        await Future.delayed(const Duration(milliseconds: 1500));
        widget.onPermissionGranted?.call();
      } else {
        setState(() {
          _isLoading = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('❌ 权限尚未启用，请按照指导完成设置'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      debugPrint('检查权限状态失败: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('检查权限状态失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
