package com.example.lockphone

import android.app.Activity
import android.util.Log

/**
 * 集成专注锁定管理器
 * 协调系统锁屏、Kiosk模式、覆盖层等所有组件
 * 提供统一的专注模式管理接口
 */
class IntegratedFocusLockManager(private val activity: Activity) {
    
    companion object {
        private const val TAG = "IntegratedFocusLockManager"
    }
    
    // 核心组件
    private val deviceAdminKioskManager = DeviceAdminKioskManager(activity)
    private val focusSystemLockManager = FocusSystemLockManager(activity, deviceAdminKioskManager)
    private val modernOverlayManager = ModernOverlayManager.getInstance(activity)
    private val systemUIController = SystemUIController(activity)

    // 厂商适配组件
    private val vendorROMAdapter = VendorROMAdapter(activity)
    private val vendorPermissionManager = VendorPermissionManager(activity, vendorROMAdapter)
    private val vendorLockStrategyExecutor = VendorLockStrategyExecutor(activity, vendorROMAdapter)

    // 终极手势阻止器
    private val ultimateGestureBlocker = UltimateGestureBlocker(activity)
    
    // 状态管理
    private var isIntegratedLockActive = false
    private var currentLockLevel = "none"

    // UX流程回调
    private var unlockInterceptionCallback: (() -> Unit)? = null
    
    /**
     * 启用集成专注锁定模式
     * 结合系统锁屏 + Kiosk模式 + 覆盖层的多重防护
     *
     * @param level 锁定级别：basic, enhanced, ultimate
     * @param onUnlockInterception 解锁拦截回调
     * @return 是否成功启用
     */
    fun enableIntegratedFocusLock(level: String = "ultimate", onUnlockInterception: (() -> Unit)? = null): Boolean {
        return try {
            Log.d(TAG, "🔒 启用集成专注锁定模式 - 级别: $level")

            if (isIntegratedLockActive) {
                Log.w(TAG, "⚠️ 集成专注锁定已激活")
                return true
            }

            // 保存解锁拦截回调
            unlockInterceptionCallback = onUnlockInterception
            
            // 1. 检查厂商权限
            val permissionResult = checkVendorPermissions()
            if (!permissionResult) {
                Log.w(TAG, "⚠️ 厂商权限检查失败，可能影响锁定效果")
            }

            // 2. 启用终极手势阻止器
            val gestureBlockResult = ultimateGestureBlocker.enableUltimateGestureBlocking()

            // 3. 执行厂商优化的锁定策略
            val vendorResult = vendorLockStrategyExecutor.executeLockStrategy()

            // 4. 执行标准锁定流程（作为补充）
            val result = when (level) {
                "basic" -> enableBasicIntegratedLock()
                "enhanced" -> enableEnhancedIntegratedLock()
                "ultimate" -> enableUltimateIntegratedLock()
                else -> {
                    Log.w(TAG, "❌ 未知锁定级别: $level，使用ultimate级别")
                    enableUltimateIntegratedLock()
                }
            }

            // 综合所有策略的结果
            val finalResult = gestureBlockResult && (vendorResult || result)
            
            if (finalResult) {
                isIntegratedLockActive = true
                currentLockLevel = level
                Log.d(TAG, "✅ 集成专注锁定模式已启用 - 级别: $level")
                Log.d(TAG, "   - 手势阻止: ${if (gestureBlockResult) "成功" else "失败"}")
                Log.d(TAG, "   - 厂商策略: ${if (vendorResult) "成功" else "失败"}")
                Log.d(TAG, "   - 标准策略: ${if (result) "成功" else "失败"}")
            } else {
                Log.e(TAG, "❌ 集成专注锁定模式启用失败")
            }

            finalResult
        } catch (e: Exception) {
            Log.e(TAG, "❌ 启用集成专注锁定模式异常: ${e.message}")
            false
        }
    }
    
    /**
     * 禁用集成专注锁定模式
     */
    fun disableIntegratedFocusLock(): Boolean {
        return try {
            Log.d(TAG, "🔒 禁用集成专注锁定模式")
            
            if (!isIntegratedLockActive) {
                Log.w(TAG, "⚠️ 集成专注锁定未激活")
                return true
            }
            
            // 1. 禁用终极手势阻止器
            val gestureBlockResult = ultimateGestureBlocker.disableUltimateGestureBlocking()

            // 2. 禁用专注系统锁屏
            val systemLockResult = focusSystemLockManager.disableFocusSystemLock()

            // 3. 禁用现代化覆盖层
            val overlayResult = try {
                modernOverlayManager.disableOverlay()
                true
            } catch (e: Exception) {
                Log.e(TAG, "禁用覆盖层失败: ${e.message}")
                false
            }
            
            // 3. 禁用系统UI控制
            val systemUIResult = systemUIController.disableImmersiveMode()
            
            // 4. 禁用设备管理员Kiosk模式
            val kioskResult = deviceAdminKioskManager.disableKioskMode()
            
            // 5. 禁用无障碍服务锁定模式
            val accessibilityResult = try {
                YoYoAccessibilityService.disableLockMode()
                val service = YoYoAccessibilityService.getInstance()
                service?.allowSystemGestures()
                true
            } catch (e: Exception) {
                Log.e(TAG, "禁用无障碍服务锁定失败: ${e.message}")
                false
            }
            
            isIntegratedLockActive = false
            currentLockLevel = "none"
            unlockInterceptionCallback = null
            
            val success = systemLockResult && overlayResult && systemUIResult && kioskResult && accessibilityResult
            
            if (success) {
                Log.d(TAG, "✅ 集成专注锁定模式已禁用")
            } else {
                Log.w(TAG, "⚠️ 集成专注锁定模式禁用部分失败")
            }
            
            success
        } catch (e: Exception) {
            Log.e(TAG, "❌ 禁用集成专注锁定模式异常: ${e.message}")
            false
        }
    }
    
    /**
     * 启用基础集成锁定
     * 系统锁屏 + 基础覆盖层
     */
    private fun enableBasicIntegratedLock(): Boolean {
        return try {
            Log.d(TAG, "🔒 启用基础集成锁定")
            
            // 1. 准备覆盖层环境
            val overlayResult = prepareOverlayEnvironment()
            
            // 2. 启用专注系统锁屏（带解锁拦截）
            val systemLockResult = focusSystemLockManager.enableFocusSystemLock {
                // 解锁后立即激活覆盖层
                activateOverlayAfterUnlock()
            }
            
            overlayResult && systemLockResult
        } catch (e: Exception) {
            Log.e(TAG, "启用基础集成锁定失败: ${e.message}")
            false
        }
    }
    
    /**
     * 启用增强集成锁定
     * 系统锁屏 + 增强覆盖层 + 系统UI控制
     */
    private fun enableEnhancedIntegratedLock(): Boolean {
        return try {
            Log.d(TAG, "🔒 启用增强集成锁定")
            
            // 1. 启用基础集成锁定
            val basicResult = enableBasicIntegratedLock()
            
            // 2. 启用系统UI控制
            val systemUIResult = systemUIController.enableFullImmersiveMode()
            
            // 3. 启用设备管理员Kiosk模式
            val kioskResult = deviceAdminKioskManager.enableKioskMode()
            
            basicResult && systemUIResult && kioskResult
        } catch (e: Exception) {
            Log.e(TAG, "启用增强集成锁定失败: ${e.message}")
            false
        }
    }
    
    /**
     * 启用终极集成锁定
     * 系统锁屏 + 全部Kiosk功能 + 无障碍服务
     */
    private fun enableUltimateIntegratedLock(): Boolean {
        return try {
            Log.d(TAG, "🔒 启用终极集成锁定")
            
            // 1. 启用增强集成锁定
            val enhancedResult = enableEnhancedIntegratedLock()
            
            // 2. 启用无障碍服务锁定模式
            val accessibilityResult = try {
                YoYoAccessibilityService.enableLockMode()
                val service = YoYoAccessibilityService.getInstance()
                service?.blockSystemGestures()
                true
            } catch (e: Exception) {
                Log.e(TAG, "启用无障碍服务锁定失败: ${e.message}")
                false
            }
            
            enhancedResult && accessibilityResult
        } catch (e: Exception) {
            Log.e(TAG, "启用终极集成锁定失败: ${e.message}")
            false
        }
    }
    
    /**
     * 准备覆盖层环境
     */
    private fun prepareOverlayEnvironment(): Boolean {
        return try {
            // 检查悬浮窗权限
            if (!modernOverlayManager.hasOverlayPermission()) {
                Log.w(TAG, "⚠️ 缺少悬浮窗权限")
                return false
            }
            
            // 准备覆盖层（但不立即显示）
            Log.d(TAG, "✅ 覆盖层环境准备完成")
            true
        } catch (e: Exception) {
            Log.e(TAG, "准备覆盖层环境失败: ${e.message}")
            false
        }
    }
    
    /**
     * 解锁后激活覆盖层
     */
    private fun activateOverlayAfterUnlock() {
        try {
            Log.d(TAG, "🚀 解锁后激活覆盖层")

            // 1. 调用解锁拦截回调（优先级最高）
            unlockInterceptionCallback?.invoke()

            // 2. 启用现代化覆盖层（作为备用方案）
            val overlayResult = modernOverlayManager.enableModernOverlay()

            // 3. 启用系统UI控制
            val systemUIResult = systemUIController.enableFullImmersiveMode()

            if (overlayResult && systemUIResult) {
                Log.d(TAG, "✅ 解锁后覆盖层激活成功")
            } else {
                Log.w(TAG, "⚠️ 解锁后覆盖层激活部分失败")
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ 解锁后激活覆盖层失败: ${e.message}")
        }
    }
    
    /**
     * 检查厂商权限
     */
    private fun checkVendorPermissions(): Boolean {
        return try {
            Log.d(TAG, "🔍 检查厂商权限")

            val adaptationStatus = vendorPermissionManager.getVendorAdaptationStatus()
            Log.d(TAG, "厂商适配状态: ${adaptationStatus.adaptationLevel}")
            Log.d(TAG, "已授权权限: ${adaptationStatus.grantedPermissions.size}")
            Log.d(TAG, "缺失权限: ${adaptationStatus.missingPermissions.size}")

            // 如果缺失关键权限，尝试申请
            if (adaptationStatus.missingPermissions.isNotEmpty()) {
                Log.w(TAG, "⚠️ 检测到缺失权限，建议用户手动设置")
                for (permission in adaptationStatus.missingPermissions) {
                    Log.w(TAG, "   - 缺失权限: $permission")
                }
            }

            // 返回是否有基本的权限支持
            adaptationStatus.adaptationLevel != AdaptationLevel.LIMITED
        } catch (e: Exception) {
            Log.e(TAG, "❌ 检查厂商权限失败: ${e.message}")
            false
        }
    }

    /**
     * 申请厂商权限
     */
    fun requestVendorPermissions(): Boolean {
        return try {
            Log.d(TAG, "🔐 申请厂商权限")
            vendorPermissionManager.requestVendorSpecificPermissions()
        } catch (e: Exception) {
            Log.e(TAG, "❌ 申请厂商权限失败: ${e.message}")
            false
        }
    }

    /**
     * 获取厂商适配状态
     */
    fun getVendorAdaptationStatus(): VendorAdaptationStatus {
        return vendorPermissionManager.getVendorAdaptationStatus()
    }

    /**
     * 获取当前锁定状态
     */
    fun getCurrentLockStatus(): LockStatus {
        val vendorStatus = getVendorAdaptationStatus()

        return LockStatus(
            isActive = isIntegratedLockActive,
            level = currentLockLevel,
            systemLockActive = focusSystemLockManager.isInFocusLockMode(),
            overlayActive = modernOverlayManager.isActive(),
            kioskActive = deviceAdminKioskManager.isDeviceAdminActive(),
            accessibilityActive = YoYoAccessibilityService.isLockModeActive(),
            vendorAdaptation = vendorStatus
        )
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            Log.d(TAG, "🧹 清理IntegratedFocusLockManager资源")
            disableIntegratedFocusLock()
            focusSystemLockManager.cleanup()
            deviceAdminKioskManager.cleanup()
        } catch (e: Exception) {
            Log.e(TAG, "❌ 清理资源失败: ${e.message}")
        }
    }
}

/**
 * 锁定状态数据类
 */
data class LockStatus(
    val isActive: Boolean,
    val level: String,
    val systemLockActive: Boolean,
    val overlayActive: Boolean,
    val kioskActive: Boolean,
    val accessibilityActive: Boolean,
    val vendorAdaptation: VendorAdaptationStatus? = null
)
