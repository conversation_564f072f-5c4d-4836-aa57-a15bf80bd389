# 无障碍服务自动启用功能测试指南

## 🎯 测试目标

验证新实现的无障碍服务自动启用功能在不同Android版本和设备厂商上的兼容性和可靠性。

## 📱 测试环境要求

### 支持的Android版本
- Android 7.0 (API 24) 及以上
- 重点测试：Android 10, 11, 12, 13, 14

### 目标设备厂商
- **小米/Redmi** (MIUI)
- **OPPO/OnePlus** (ColorOS/OxygenOS)
- **华为/荣耀** (EMUI/HarmonyOS)
- **Vivo** (OriginOS)
- **三星** (One UI)
- **原生Android** (Pixel设备)

## 🧪 测试用例

### 1. 基础功能测试

#### 1.1 权限状态检测
```
测试步骤：
1. 启动应用
2. 进入专注任务创建页面
3. 选择"深度锁定"模式
4. 点击"开始专注"

预期结果：
- 应用能正确检测无障碍服务状态
- 显示增强版权限对话框
- 显示设备信息和权限状态
```

#### 1.2 自动启用功能
```
测试步骤：
1. 确保设备有WRITE_SECURE_SETTINGS权限
2. 触发无障碍服务启用流程
3. 观察自动启用过程

预期结果：
- 自动启用成功时显示成功提示
- 自动启用失败时显示回退选项
- 权限状态实时更新
```

### 2. 厂商兼容性测试

#### 2.1 小米设备测试
```
特殊测试点：
- MIUI优化设置影响
- 自启动管理限制
- 省电策略干扰
- 安全中心权限管理

测试方法：
1. 在小米设备上执行基础功能测试
2. 检查厂商特定引导信息是否正确显示
3. 验证小米特有的启用方法
```

#### 2.2 OPPO设备测试
```
特殊测试点：
- ColorOS权限管理
- 应用后台运行限制
- 智能省电影响

测试方法：
1. 测试OPPO特定的启用方法
2. 验证权限引导信息准确性
3. 检查后台运行设置提醒
```

#### 2.3 华为设备测试
```
特殊测试点：
- EMUI/HarmonyOS兼容性
- 应用启动管理
- 省电模式白名单

测试方法：
1. 测试华为特定启用流程
2. 验证启动管理设置引导
3. 检查省电模式处理
```

### 3. 回退机制测试

#### 3.1 ADB命令回退
```
测试场景：
- 设备无WRITE_SECURE_SETTINGS权限
- 设备未Root

测试步骤：
1. 移除应用的系统权限
2. 触发启用流程
3. 选择ADB命令选项

预期结果：
- 显示正确的ADB命令
- 提供详细的操作步骤
- 命令执行后能成功启用
```

#### 3.2 Root权限回退
```
测试场景：
- 设备已Root
- 缺少系统权限

测试步骤：
1. 在Root设备上测试
2. 选择Root授权选项
3. 验证Root权限授予

预期结果：
- 正确检测Root状态
- 成功通过Root授予权限
- 自动重试启用流程
```

#### 3.3 手动设置回退
```
测试场景：
- 所有自动方法都失败

测试步骤：
1. 模拟自动启用失败
2. 选择手动设置选项
3. 按照引导完成设置

预期结果：
- 显示详细的设置步骤
- 厂商特定引导准确
- 设置完成后正确检测状态
```

### 4. 错误处理测试

#### 4.1 网络异常处理
```
测试步骤：
1. 断开网络连接
2. 触发启用流程
3. 观察错误处理

预期结果：
- 不依赖网络连接
- 本地功能正常工作
- 错误信息友好
```

#### 4.2 权限拒绝处理
```
测试步骤：
1. 用户拒绝权限请求
2. 观察应用响应
3. 测试重新请求流程

预期结果：
- 优雅处理权限拒绝
- 提供重新请求选项
- 不会崩溃或卡死
```

## 📊 测试记录模板

### 设备信息记录
```
设备型号：
厂商：
Android版本：
API级别：
系统UI版本：
Root状态：
```

### 功能测试记录
```
测试项目：
测试结果：✅/❌
执行时间：
错误信息：
备注：
```

### 兼容性测试记录
```
厂商特定功能：✅/❌
引导信息准确性：✅/❌
自动启用成功率：
回退机制有效性：✅/❌
用户体验评分：1-5
```

## 🔧 调试工具

### 1. 日志收集
```bash
# 收集应用日志
adb logcat -s "YoYoAccessibility" "MainActivity" "PermissionManager"

# 收集系统日志
adb logcat -s "AccessibilityManagerService"
```

### 2. 权限检查命令
```bash
# 检查应用权限
adb shell dumpsys package com.example.lockphone | grep permission

# 检查无障碍服务状态
adb shell settings get secure enabled_accessibility_services
adb shell settings get secure accessibility_enabled
```

### 3. 手动权限授予
```bash
# 授予系统权限
adb shell pm grant com.example.lockphone android.permission.WRITE_SECURE_SETTINGS

# 手动启用无障碍服务
adb shell settings put secure enabled_accessibility_services com.example.lockphone/.YoYoAccessibilityService
adb shell settings put secure accessibility_enabled 1
```

## 📈 性能测试

### 1. 启用速度测试
- 记录从触发到完成的时间
- 不同方法的性能对比
- 设备性能影响分析

### 2. 资源使用测试
- CPU使用率监控
- 内存占用分析
- 电池消耗评估

### 3. 稳定性测试
- 连续启用/禁用测试
- 长时间运行稳定性
- 异常情况恢复能力

## ✅ 验收标准

### 功能完整性
- [ ] 所有启用方法都能正常工作
- [ ] 回退机制覆盖所有失败场景
- [ ] 错误处理完善且用户友好

### 兼容性要求
- [ ] 支持主流Android版本（7.0+）
- [ ] 适配主要设备厂商
- [ ] 厂商特定功能正常工作

### 用户体验
- [ ] 操作流程简单直观
- [ ] 引导信息清晰准确
- [ ] 响应速度满足要求

### 可靠性指标
- [ ] 自动启用成功率 > 80%
- [ ] 回退机制成功率 > 95%
- [ ] 无严重Bug或崩溃

## 🚀 测试执行计划

### 第一阶段：基础功能验证
- 在开发设备上完成所有基础测试
- 修复发现的问题
- 优化用户体验

### 第二阶段：兼容性测试
- 在不同厂商设备上测试
- 收集兼容性问题
- 实现厂商特定优化

### 第三阶段：压力测试
- 执行性能和稳定性测试
- 优化资源使用
- 完善错误处理

### 第四阶段：用户验收测试
- 邀请用户参与测试
- 收集用户反馈
- 最终优化和发布
