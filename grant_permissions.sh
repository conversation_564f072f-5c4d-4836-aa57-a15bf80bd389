#!/bin/bash

echo "正在为专注锁屏应用授予系统权限..."
echo

echo "1. 授予WRITE_SECURE_SETTINGS权限（用于自动控制无障碍服务）"
adb shell pm grant com.example.lockphone android.permission.WRITE_SECURE_SETTINGS

echo
echo "2. 授予WRITE_SETTINGS权限（用于系统设置访问）"
adb shell pm grant com.example.lockphone android.permission.WRITE_SETTINGS

echo
echo "3. 检查权限授予状态"
adb shell dumpsys package com.example.lockphone | grep "android.permission.WRITE_SECURE_SETTINGS"
adb shell dumpsys package com.example.lockphone | grep "android.permission.WRITE_SETTINGS"

echo
echo "权限授予完成！"
echo
echo "注意："
echo "- 这些权限允许应用自动控制无障碍服务"
echo "- 专注开始时自动启用，专注结束时自动关闭"
echo "- 确保更好的用户体验，避免手动管理"
echo
