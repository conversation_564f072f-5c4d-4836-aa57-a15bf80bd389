package com.example.lockphone

/**
 * 厂商ROM相关的数据类和枚举定义
 */

/**
 * ROM厂商枚举
 */
enum class ROMVendor {
    OPPO,           // OPPO ColorOS
    XIAOMI,         // 小米 MIUI
    HUAWEI,         // 华为 EMUI/HarmonyOS
    SAMSUNG,        // 三星 OneUI
    VIVO,           // Vivo Funtouch
    REALME,         // Realme UI
    STOCK_ANDROID,  // 原生Android
    UNKNOWN         // 未知厂商
}

/**
 * 覆盖层类型枚举
 */
enum class OverlayType {
    TYPE_APPLICATION_OVERLAY,   // 标准应用覆盖层
    TYPE_SYSTEM_OVERLAY,        // 系统覆盖层
    MULTIPLE_LAYERS,            // 多层覆盖
    VENDOR_SPECIFIC            // 厂商特定类型
}

/**
 * 手势拦截级别枚举
 */
enum class GestureBlockingLevel {
    STANDARD,   // 标准级别
    ENHANCED,   // 增强级别
    MAXIMUM     // 最大级别
}

/**
 * ROM信息数据类
 */
data class ROMInfo(
    val vendor: ROMVendor,
    val romName: String,
    val version: String,
    val androidVersion: String,
    val apiLevel: Int,
    val brand: String,
    val model: String
) {
    override fun toString(): String {
        return "$vendor $romName $version (Android $androidVersion, API $apiLevel) - $brand $model"
    }
}

/**
 * 厂商锁定策略数据类
 */
data class VendorLockStrategy(
    val vendor: ROMVendor,
    val useSystemLock: Boolean,
    val overlayType: OverlayType,
    val gestureBlocking: GestureBlockingLevel,
    val accessibilityRequired: Boolean,
    val deviceAdminRequired: Boolean,
    val specialPermissions: List<String>,
    val vendorOptimizations: VendorOptimizations,
    val fallbackStrategy: VendorLockStrategy?
)

/**
 * 厂商优化基类
 */
abstract class VendorOptimizations

/**
 * OPPO ColorOS优化
 */
data class OPPOOptimizations(
    val disableColorOSGestures: Boolean = true,
    val blockSmartSidebar: Boolean = true,
    val disableGameSpace: Boolean = true,
    val preventAutoFreeze: Boolean = true
) : VendorOptimizations()

/**
 * 小米MIUI优化
 */
data class MIUIOptimizations(
    val disableGestureNavigation: Boolean = true,
    val blockMIUIControlCenter: Boolean = true,
    val disableQuickSettings: Boolean = true,
    val preventMIUIOptimization: Boolean = true,
    val blockGameTurbo: Boolean = true
) : VendorOptimizations()

/**
 * 华为EMUI/HarmonyOS优化
 */
data class EMUIOptimizations(
    val useHuaweiFloatingWindow: Boolean = true,
    val blockEMUINavigationGestures: Boolean = true,
    val disableHuaweiAssistant: Boolean = true,
    val preventPowerGenie: Boolean = true,
    val blockSmartAssist: Boolean = true
) : VendorOptimizations()

/**
 * 三星OneUI优化
 */
data class OneUIOptimizations(
    val disableEdgePanel: Boolean = true,
    val blockBixby: Boolean = true,
    val disableSmartSelect: Boolean = true,
    val preventGameLauncher: Boolean = true
) : VendorOptimizations()

/**
 * Vivo Funtouch优化
 */
data class FuntouchOptimizations(
    val disableSmartMotion: Boolean = true,
    val blockJoviAssistant: Boolean = true,
    val preventVivoOptimization: Boolean = true
) : VendorOptimizations()

/**
 * Realme UI优化
 */
data class RealmeUIOptimizations(
    val preventRealmeOptimization: Boolean = true,
    val blockGameSpace: Boolean = true
) : VendorOptimizations()

/**
 * 标准Android优化
 */
class StandardOptimizations : VendorOptimizations()

/**
 * 权限申请结果枚举
 */
enum class PermissionRequestResult {
    GRANTED,        // 已授权
    DENIED,         // 被拒绝
    NEED_MANUAL,    // 需要手动设置
    NOT_SUPPORTED,  // 不支持
    ERROR           // 错误
}

/**
 * 厂商权限类型枚举
 */
enum class VendorPermissionType {
    AUTO_START,         // 自启动权限
    FLOATING_WINDOW,    // 悬浮窗权限
    BACKGROUND_RUN,     // 后台运行权限
    NOTIFICATION,       // 通知权限
    DEVICE_ADMIN,       // 设备管理员权限
    ACCESSIBILITY,      // 无障碍权限
    BATTERY_OPTIMIZATION, // 电池优化白名单
    APP_OPS,           // 应用操作权限
    VENDOR_SPECIFIC    // 厂商特定权限
}

/**
 * 权限申请信息数据类
 */
data class PermissionRequestInfo(
    val type: VendorPermissionType,
    val permission: String,
    val displayName: String,
    val description: String,
    val isRequired: Boolean,
    val requestMethod: PermissionRequestMethod,
    val settingsIntent: String? = null
)

/**
 * 权限申请方法枚举
 */
enum class PermissionRequestMethod {
    STANDARD_REQUEST,   // 标准权限申请
    SETTINGS_INTENT,    // 跳转设置页面
    VENDOR_API,         // 厂商API
    MANUAL_GUIDE       // 手动引导
}

/**
 * 厂商适配状态数据类
 */
data class VendorAdaptationStatus(
    val romInfo: ROMInfo,
    val lockStrategy: VendorLockStrategy,
    val requiredPermissions: List<PermissionRequestInfo>,
    val grantedPermissions: List<VendorPermissionType>,
    val missingPermissions: List<VendorPermissionType>,
    val adaptationLevel: AdaptationLevel,
    val recommendations: List<String>
)

/**
 * 适配级别枚举
 */
enum class AdaptationLevel {
    FULL,       // 完全适配
    PARTIAL,    // 部分适配
    BASIC,      // 基础适配
    LIMITED     // 受限适配
}
