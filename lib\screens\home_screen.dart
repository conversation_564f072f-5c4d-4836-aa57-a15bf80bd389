import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../utils/constants.dart';
import '../providers/statistics_provider.dart';
import '../services/permission_manager.dart';
import '../services/permission_cache_service.dart';
import '../services/focus_manager.dart' as focus_service;
import '../widgets/simplified_permission_dialog.dart';
import '../widgets/streamlined_permission_guide.dart';
import 'statistics_screen.dart';
import 'achievements_screen.dart';
import 'yoyo_create_task_screen.dart';
import 'permission_debug_screen.dart';
import 'streamlined_permission_demo.dart';
import 'super_enhanced_lock_test.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    // Load statistics when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<StatisticsProvider>().loadStatistics();
    });
  }

  /// 处理开始专注按钮点击
  /// 先检查基础权限，如果满足则直接进入任务创建页面
  /// 如果不满足则显示简化的权限授权对话框
  Future<void> _handleStartFocus() async {
    final permissionManager = PermissionManager.instance;

    // 检查基础权限（存储权限）
    final hasBasicPermissions = await permissionManager.hasBasicPermissions();

    if (hasBasicPermissions) {
      // 基础权限满足，进行智能权限预检查
      await _performSmartPermissionPrecheck();

      // 进入任务创建页面
      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const YoYoCreateTaskScreen(),
          ),
        );
      }
    } else {
      // 基础权限不满足，显示权限授权对话框
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => SimplifiedPermissionDialog(
            lockLevel: focus_service.LockLevel.basic, // 默认检查基础权限
            onPermissionsGranted: () {
              // 权限授权成功，进入任务创建页面
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const YoYoCreateTaskScreen(),
                ),
              );
            },
            onPermissionsDenied: () {
              // 权限授权失败，显示提示
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('需要基础权限才能使用专注功能'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
          ),
        );
      }
    }
  }

  /// 智能权限预检查
  /// 在用户准备开始专注时，预先检查和优化权限状态
  Future<void> _performSmartPermissionPrecheck() async {
    try {
      debugPrint('开始智能权限预检查');

      // 检查无障碍服务权限状态
      final accessibilityResult = await PermissionCacheService.instance
          .smartPermissionCheck(PermissionType.accessibility);

      debugPrint('无障碍权限智能检查结果: $accessibilityResult');

      // 如果可以自动启用，静默尝试
      if (accessibilityResult.canAutoEnable) {
        debugPrint('尝试静默启用无障碍服务');
        final autoEnabled =
            await PermissionManager.instance.enableAccessibilityServiceAuto();

        if (autoEnabled) {
          debugPrint('无障碍服务自动启用成功');
          // 记录自动启用尝试
          await PermissionCacheService.instance
              .recordAutoEnableAttempt(PermissionType.accessibility);
        } else {
          debugPrint('无障碍服务自动启用失败');
        }
      }

      debugPrint('智能权限预检查完成');
    } catch (e) {
      debugPrint('智能权限预检查失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingLarge),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Header
                  _buildHeader(),

                  const SizedBox(height: AppDimensions.paddingXLarge),

                  // Main action button
                  _buildMainActionButton(),

                  const SizedBox(height: AppDimensions.paddingXLarge),

                  // Today's stats
                  _buildTodayStats(),

                  const SizedBox(height: AppDimensions.paddingLarge),

                  // Streak card
                  _buildStreakCard(),

                  const SizedBox(height: AppDimensions.paddingXLarge),

                  // Bottom navigation
                  _buildBottomNavigation(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppStrings.appName,
              style: AppTextStyles.titleLarge.copyWith(
                color: AppColors.primary,
                fontSize: 28,
              ),
            ),
            const Text(
              AppStrings.appSubtitle,
              style: AppTextStyles.bodySmall,
            ),
          ],
        ),
        IconButton(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const AchievementsScreen(),
              ),
            );
          },
          icon: const Icon(
            Icons.settings,
            color: AppColors.onSurfaceVariant,
            size: 28,
          ),
        ),
        // 调试模式下显示权限调试入口
        if (kDebugMode)
          IconButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const PermissionDebugScreen(),
                ),
              );
            },
            icon: const Icon(
              Icons.bug_report,
              color: AppColors.onSurfaceVariant,
              size: 28,
            ),
            tooltip: '权限调试',
          ),
        // 调试模式下显示授权流程演示入口
        if (kDebugMode)
          IconButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const StreamlinedPermissionDemo(),
                ),
              );
            },
            icon: const Icon(
              Icons.science,
              color: AppColors.onSurfaceVariant,
              size: 28,
            ),
            tooltip: '授权流程演示',
          ),
      ],
    );
  }

  Widget _buildMainActionButton() {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        gradient: AppColors.primaryGradient,
        borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _handleStartFocus,
          borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
          child: const Center(
            child: Text(
              AppStrings.startFocus,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black,
                letterSpacing: 1,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTodayStats() {
    return Consumer<StatisticsProvider>(
      builder: (context, statsProvider, child) {
        return Row(
          children: [
            Expanded(
              child: _buildStatCard(
                value: statsProvider.todaySessionCount.toString(),
                label: AppStrings.todayFocus,
                icon: '🔒',
              ),
            ),
            const SizedBox(width: AppDimensions.paddingMedium),
            Expanded(
              child: _buildStatCard(
                value: statsProvider.todayMinutes.toString(),
                label: AppStrings.totalMinutes,
                icon: '⏱️',
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildStreakCard() {
    return Consumer<StatisticsProvider>(
      builder: (context, statsProvider, child) {
        return Container(
          padding: const EdgeInsets.all(AppDimensions.paddingLarge),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
            border: Border.all(
              color: AppColors.primary.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius:
                      BorderRadius.circular(AppDimensions.radiusMedium),
                ),
                child: const Center(
                  child: Text(
                    '🔥',
                    style: TextStyle(fontSize: 28),
                  ),
                ),
              ),
              const SizedBox(width: AppDimensions.paddingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${statsProvider.currentStreak}',
                      style: AppTextStyles.titleLarge.copyWith(
                        color: AppColors.primary,
                        fontSize: 32,
                      ),
                    ),
                    const Text(
                      AppStrings.consecutiveDays,
                      style: AppTextStyles.bodyMedium,
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatCard({
    required String value,
    required String label,
    required String icon,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        border: Border.all(
          color: AppColors.onSurface.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Text(
            icon,
            style: const TextStyle(fontSize: 24),
          ),
          const SizedBox(height: AppDimensions.paddingSmall),
          Text(
            value,
            style: AppTextStyles.titleLarge.copyWith(
              color: AppColors.primary,
              fontSize: 28,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: AppTextStyles.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavigation() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildNavButton(
                icon: '📊',
                label: AppStrings.myRecords,
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const StatisticsScreen(),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(width: AppDimensions.paddingMedium),
            Expanded(
              child: _buildNavButton(
                icon: '🏆',
                label: AppStrings.achievements,
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const AchievementsScreen(),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.paddingMedium),
        // 测试按钮行
        if (kDebugMode) ...[
          Row(
            children: [
              Expanded(
                child: _buildNavButton(
                  icon: '🔒',
                  label: '超级增强锁定测试',
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            const SuperEnhancedLockTestScreen(),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(width: AppDimensions.paddingMedium),
              Expanded(
                child: _buildNavButton(
                  icon: '🛠️',
                  label: '权限调试',
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const PermissionDebugScreen(),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildNavButton({
    required String icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        border: Border.all(
          color: AppColors.primary.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                icon,
                style: const TextStyle(fontSize: 24),
              ),
              const SizedBox(height: AppDimensions.paddingSmall),
              Text(
                label,
                style: AppTextStyles.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 显示简化的权限引导界面
  void _showStreamlinedPermissionGuide(focus_service.LockLevel level) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        child: StreamlinedPermissionGuide(
          targetLevel: level,
          onCompleted: () {
            Navigator.of(context).pop();
            _startFocusMode(level);
          },
          onSkipped: () {
            Navigator.of(context).pop();
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('已跳过权限配置，将使用基础防护模式'),
                backgroundColor: Colors.orange,
              ),
            );
            // 使用基础模式启动
            _startFocusMode(focus_service.LockLevel.basic);
          },
        ),
      ),
    );
  }

  /// 启动专注模式（简化版本）
  Future<void> _startFocusMode(focus_service.LockLevel level) async {
    try {
      // 基于五层防护体系，直接启动专注模式
      // 不再需要复杂的权限检查

      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const YoYoCreateTaskScreen(),
        ),
      );
    } catch (e) {
      debugPrint('启动专注模式失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('启动失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
