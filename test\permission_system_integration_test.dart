import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:lockphone/services/permission_cache_service.dart';
import 'package:lockphone/services/permission_manager.dart';
import 'package:lockphone/services/permission_state_monitor.dart';
import 'package:lockphone/services/app_lifecycle_manager.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('权限系统集成测试', () {
    late PermissionCacheService cacheService;
    late PermissionStateMonitor stateMonitor;
    late AppLifecycleManager lifecycleManager;

    setUp(() async {
      // 设置测试环境
      SharedPreferences.setMockInitialValues({});

      cacheService = PermissionCacheService.instance;
      stateMonitor = PermissionStateMonitor.instance;
      lifecycleManager = AppLifecycleManager.instance;

      await cacheService.init();

      // 模拟方法通道
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        const MethodChannel('yoyo_lock_screen'),
        (MethodCall methodCall) async {
          switch (methodCall.method) {
            case 'checkPermission':
              // 默认返回拒绝状态用于测试
              return 'denied';
            case 'enableAccessibilityServiceAuto':
              // 模拟自动启用失败
              return false;
            default:
              return null;
          }
        },
      );
    });

    tearDown(() async {
      // 清理测试数据
      await cacheService.clearAllPermissionCache();
      stateMonitor.dispose();
      lifecycleManager.dispose();
    });

    test('完整的用户体验流程测试', () async {
      // 场景1: 新用户首次使用
      print('\n=== 场景1: 新用户首次使用 ===');

      var result =
          await cacheService.smartPermissionCheck(PermissionType.accessibility);
      expect(result.currentStatus, PermissionStatus.denied);
      expect(result.shouldShowRationale, true);
      expect(result.hasUserConsent, false);
      expect(result.canAutoEnable, false);

      print('✓ 新用户需要显示权限说明');

      // 场景2: 用户同意权限
      print('\n=== 场景2: 用户同意权限 ===');

      await cacheService.recordUserConsent(PermissionType.accessibility);

      result =
          await cacheService.smartPermissionCheck(PermissionType.accessibility);
      expect(result.hasUserConsent, true);
      expect(result.canAutoEnable, true);
      expect(result.shouldShowRationale, false);

      print('✓ 用户同意后可以尝试自动启用');

      // 场景3: 自动启用尝试
      print('\n=== 场景3: 自动启用尝试 ===');

      await cacheService.recordAutoEnableAttempt(PermissionType.accessibility);

      result =
          await cacheService.smartPermissionCheck(PermissionType.accessibility);
      expect(result.hasAutoEnableAttempted, true);
      expect(result.canAutoEnable, false);
      expect(result.needsRequest, true);

      print('✓ 自动启用尝试后直接跳转设置');

      // 场景4: 权限授予成功
      print('\n=== 场景4: 权限授予成功 ===');

      await cacheService.cachePermissionStatus(
        PermissionType.accessibility,
        PermissionStatus.granted,
      );

      result =
          await cacheService.smartPermissionCheck(PermissionType.accessibility);
      expect(result.currentStatus, PermissionStatus.granted);
      expect(result.needsRequest, false);
      expect(result.canAutoEnable, false);

      print('✓ 权限授予后无需任何操作');

      // 场景5: 权限被撤销
      print('\n=== 场景5: 权限被撤销 ===');

      await cacheService.cachePermissionStatus(
        PermissionType.accessibility,
        PermissionStatus.denied,
      );

      // 模拟权限撤销处理
      await cacheService.resetPermissionState(PermissionType.accessibility);

      result =
          await cacheService.smartPermissionCheck(PermissionType.accessibility);
      expect(result.currentStatus, PermissionStatus.denied);
      expect(result.hasUserConsent, false);
      expect(result.hasAutoEnableAttempted, false);
      expect(result.shouldShowRationale, true);

      print('✓ 权限撤销后重置到初始状态');
    });

    test('权限状态监听测试', () async {
      print('\n=== 权限状态监听测试 ===');

      bool callbackTriggered = false;
      PermissionStatus? oldStatus;
      PermissionStatus? newStatus;

      // 添加状态变化回调
      stateMonitor.addStateChangeCallback((type, old, current) {
        if (type == PermissionType.accessibility) {
          callbackTriggered = true;
          oldStatus = old;
          newStatus = current;
        }
      });

      // 设置初始状态
      await cacheService.cachePermissionStatus(
        PermissionType.accessibility,
        PermissionStatus.granted,
      );

      // 模拟权限状态变化
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        const MethodChannel('yoyo_lock_screen'),
        (MethodCall methodCall) async {
          if (methodCall.method == 'checkPermission') {
            return 'denied'; // 模拟权限被撤销
          }
          return null;
        },
      );

      // 手动触发检查
      await stateMonitor.checkSpecificPermission(PermissionType.accessibility);

      expect(callbackTriggered, true);
      expect(oldStatus, PermissionStatus.granted);
      expect(newStatus, PermissionStatus.denied);

      print('✓ 权限状态变化监听正常工作');
    });

    test('应用生命周期管理测试', () async {
      print('\n=== 应用生命周期管理测试 ===');

      // 初始化生命周期管理器
      lifecycleManager.initialize();
      expect(lifecycleManager.isInitialized, true);

      // 模拟应用暂停
      lifecycleManager.didChangeAppLifecycleState(AppLifecycleState.paused);
      expect(stateMonitor.isMonitoring, false);

      // 模拟应用恢复
      lifecycleManager.didChangeAppLifecycleState(AppLifecycleState.resumed);
      expect(stateMonitor.isMonitoring, true);

      print('✓ 应用生命周期管理正常工作');
    });

    test('缓存性能测试', () async {
      print('\n=== 缓存性能测试 ===');

      final stopwatch = Stopwatch()..start();

      // 测试缓存读取性能
      for (int i = 0; i < 100; i++) {
        await cacheService
            .getCachedPermissionStatus(PermissionType.accessibility);
      }

      stopwatch.stop();
      final cacheTime = stopwatch.elapsedMilliseconds;

      stopwatch.reset();
      stopwatch.start();

      // 测试直接检查性能（模拟）
      for (int i = 0; i < 100; i++) {
        await PermissionManager.instance
            .checkPermission(PermissionType.accessibility);
      }

      stopwatch.stop();
      final directTime = stopwatch.elapsedMilliseconds;

      print('缓存读取时间: ${cacheTime}ms');
      print('直接检查时间: ${directTime}ms');
      print(
          '性能提升: ${((directTime - cacheTime) / directTime * 100).toStringAsFixed(1)}%');

      // 缓存应该比直接检查更快（在真实环境中）
      expect(cacheTime, lessThanOrEqualTo(directTime));
    });

    test('错误处理和恢复测试', () async {
      print('\n=== 错误处理和恢复测试 ===');

      // 模拟方法通道错误
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        const MethodChannel('yoyo_lock_screen'),
        (MethodCall methodCall) async {
          throw PlatformException(code: 'ERROR', message: '模拟错误');
        },
      );

      // 测试错误处理
      final status =
          await cacheService.getPermissionStatus(PermissionType.accessibility);
      expect(status, PermissionStatus.unknown);

      // 恢复正常
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        const MethodChannel('yoyo_lock_screen'),
        (MethodCall methodCall) async {
          if (methodCall.method == 'checkPermission') {
            return 'granted';
          }
          return null;
        },
      );

      // 强制刷新应该恢复正常
      final refreshedStatus = await cacheService
          .forceRefreshPermissionStatus(PermissionType.accessibility);
      expect(refreshedStatus, PermissionStatus.granted);

      print('✓ 错误处理和恢复机制正常工作');
    });

    test('多权限类型独立性测试', () async {
      print('\n=== 多权限类型独立性测试 ===');

      // 设置不同权限的不同状态
      await cacheService.cachePermissionStatus(
          PermissionType.accessibility, PermissionStatus.granted);
      await cacheService.cachePermissionStatus(
          PermissionType.deviceAdmin, PermissionStatus.denied);
      await cacheService.recordUserConsent(PermissionType.accessibility);

      // 检查权限状态独立性
      final accessibilityResult =
          await cacheService.smartPermissionCheck(PermissionType.accessibility);
      final deviceAdminResult =
          await cacheService.smartPermissionCheck(PermissionType.deviceAdmin);

      expect(accessibilityResult.currentStatus, PermissionStatus.granted);
      expect(accessibilityResult.hasUserConsent, true);

      expect(deviceAdminResult.currentStatus, PermissionStatus.denied);
      expect(deviceAdminResult.hasUserConsent, false);

      // 重置一个权限不应影响另一个
      await cacheService.resetPermissionState(PermissionType.accessibility);

      final accessibilityAfterReset =
          await cacheService.smartPermissionCheck(PermissionType.accessibility);
      final deviceAdminAfterReset =
          await cacheService.smartPermissionCheck(PermissionType.deviceAdmin);

      expect(accessibilityAfterReset.hasUserConsent, false);
      expect(
          deviceAdminAfterReset.currentStatus, PermissionStatus.denied); // 不受影响

      print('✓ 多权限类型独立性正常');
    });
  });
}
