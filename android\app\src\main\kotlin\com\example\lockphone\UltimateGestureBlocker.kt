package com.example.lockphone

import android.app.Activity
import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.util.Log
import android.view.*
import android.widget.FrameLayout

/**
 * 终极手势阻止器
 * 实现真正不可逃脱的手势拦截，参考"禅定空间"应用
 * 
 * 主要功能：
 * 1. 100%覆盖屏幕的多层拦截
 * 2. 阻止所有系统手势（Home、Back、Recent、状态栏下拉）
 * 3. 防止用户通过任何手势操作离开专注模式
 * 4. 支持厂商特定的手势拦截优化
 */
class UltimateGestureBlocker(private val activity: Activity) {
    
    companion object {
        private const val TAG = "UltimateGestureBlocker"
        private const val OVERLAY_COUNT = 3 // 多层覆盖确保拦截
    }
    
    private val windowManager = activity.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    private val overlayViews = mutableListOf<View>()
    private var isActive = false
    
    // 手势检测相关
    private var lastTouchTime = 0L
    private var consecutiveSystemGestures = 0
    private var isSystemGestureBlocked = false
    
    /**
     * 启用终极手势阻止
     */
    fun enableUltimateGestureBlocking(): Boolean {
        return try {
            Log.d(TAG, "🚫 启用终极手势阻止器")
            
            if (isActive) {
                Log.w(TAG, "⚠️ 终极手势阻止器已激活")
                return true
            }
            
            // 1. 创建多层覆盖视图
            createMultipleOverlayLayers()
            
            // 2. 启用无障碍服务手势阻止
            enableAccessibilityGestureBlocking()
            
            // 3. 设置系统UI隐藏
            hideSystemUICompletely()
            
            // 4. 启动手势监控
            startGestureMonitoring()
            
            isActive = true
            isSystemGestureBlocked = true
            
            Log.d(TAG, "✅ 终极手势阻止器已启用")
            true
        } catch (e: Exception) {
            Log.e(TAG, "❌ 启用终极手势阻止器失败: ${e.message}")
            false
        }
    }
    
    /**
     * 禁用终极手势阻止
     */
    fun disableUltimateGestureBlocking(): Boolean {
        return try {
            Log.d(TAG, "🔓 禁用终极手势阻止器")
            
            if (!isActive) {
                Log.w(TAG, "⚠️ 终极手势阻止器未激活")
                return true
            }
            
            // 1. 移除所有覆盖层
            removeAllOverlayLayers()
            
            // 2. 恢复系统UI
            restoreSystemUI()
            
            // 3. 停止手势监控
            stopGestureMonitoring()
            
            isActive = false
            isSystemGestureBlocked = false
            
            Log.d(TAG, "✅ 终极手势阻止器已禁用")
            true
        } catch (e: Exception) {
            Log.e(TAG, "❌ 禁用终极手势阻止器失败: ${e.message}")
            false
        }
    }
    
    /**
     * 创建多层覆盖视图
     */
    private fun createMultipleOverlayLayers() {
        for (i in 0 until OVERLAY_COUNT) {
            val overlayView = createUltimateOverlayView(i)
            val params = createUltimateOverlayParams(i)
            
            windowManager.addView(overlayView, params)
            overlayViews.add(overlayView)
            
            Log.d(TAG, "✅ 创建覆盖层 ${i + 1}/$OVERLAY_COUNT")
        }
    }
    
    /**
     * 创建终极覆盖视图
     */
    private fun createUltimateOverlayView(layerIndex: Int): View {
        val view = object : FrameLayout(activity) {
            override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
                ev?.let { event ->
                    handleUltimateGestureInterception(event, layerIndex)
                }
                return true // 完全消费所有触摸事件
            }
            
            override fun onInterceptTouchEvent(ev: MotionEvent?): Boolean {
                return true // 拦截所有触摸事件
            }
            
            override fun onTouchEvent(event: MotionEvent?): Boolean {
                event?.let { handleUltimateGestureInterception(it, layerIndex) }
                return true
            }
            
            override fun dispatchKeyEvent(event: KeyEvent?): Boolean {
                // 拦截所有按键事件
                event?.let { handleKeyInterception(it) }
                return true
            }
        }
        
        // 设置视图属性
        view.setBackgroundColor(if (layerIndex == 0) 0x01000000 else 0x00000000) // 最底层微透明
        view.isClickable = true
        view.isFocusable = true
        view.isFocusableInTouchMode = true
        
        // 设置系统UI隐藏
        view.systemUiVisibility = (
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
            View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
            View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
            View.SYSTEM_UI_FLAG_FULLSCREEN or
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
        )
        
        return view
    }
    
    /**
     * 创建终极覆盖参数
     */
    private fun createUltimateOverlayParams(layerIndex: Int): WindowManager.LayoutParams {
        val type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else {
            @Suppress("DEPRECATION")
            WindowManager.LayoutParams.TYPE_SYSTEM_ALERT
        }
        
        return WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            type,
            // 根据层级设置不同的标志
            if (layerIndex == 0) {
                // 底层：可获得焦点，拦截所有事件
                WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH or
                WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS or
                WindowManager.LayoutParams.FLAG_FULLSCREEN or
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
            } else {
                // 上层：透明拦截层
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH or
                WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS or
                WindowManager.LayoutParams.FLAG_FULLSCREEN or
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
            },
            PixelFormat.TRANSLUCENT
        ).apply {
            // 确保覆盖整个屏幕，包括系统手势区域
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
            }
            
            // 设置窗口优先级
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                flags = flags or WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
            }
        }
    }
    
    /**
     * 处理终极手势拦截
     */
    private fun handleUltimateGestureInterception(event: MotionEvent, layerIndex: Int) {
        val currentTime = System.currentTimeMillis()
        val x = event.x
        val y = event.y
        
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                Log.w(TAG, "🚨 层级$layerIndex 拦截手势: ($x, $y)")
                
                // 检测系统手势区域
                if (isSystemGestureArea(x, y)) {
                    consecutiveSystemGestures++
                    Log.w(TAG, "🚨 系统手势尝试 #$consecutiveSystemGestures 被阻止")
                    
                    // 触发强化拦截
                    triggerEnhancedInterception()
                }
                
                lastTouchTime = currentTime
            }
            MotionEvent.ACTION_MOVE -> {
                // 检测滑动手势
                if (isSwipeGesture(event)) {
                    Log.w(TAG, "🚨 滑动手势被拦截")
                    triggerEnhancedInterception()
                }
            }
        }
    }
    
    /**
     * 处理按键拦截
     */
    private fun handleKeyInterception(event: KeyEvent) {
        when (event.keyCode) {
            KeyEvent.KEYCODE_HOME,
            KeyEvent.KEYCODE_BACK,
            KeyEvent.KEYCODE_APP_SWITCH,
            KeyEvent.KEYCODE_MENU -> {
                Log.w(TAG, "🚨 系统按键被拦截: ${event.keyCode}")
            }
        }
    }
    
    /**
     * 检测是否为系统手势区域
     */
    private fun isSystemGestureArea(x: Float, y: Float): Boolean {
        val screenWidth = activity.resources.displayMetrics.widthPixels
        val screenHeight = activity.resources.displayMetrics.heightPixels
        val density = activity.resources.displayMetrics.density
        
        // 底部手势区域（更大的范围）
        val bottomGestureHeight = 150 * density
        if (y > screenHeight - bottomGestureHeight) return true
        
        // 侧边手势区域
        val sideGestureWidth = 80 * density
        if (x < sideGestureWidth || x > screenWidth - sideGestureWidth) return true
        
        // 顶部状态栏区域
        val topGestureHeight = 120 * density
        if (y < topGestureHeight) return true
        
        return false
    }
    
    /**
     * 检测滑动手势
     */
    private fun isSwipeGesture(event: MotionEvent): Boolean {
        if (event.historySize == 0) return false
        
        val deltaX = Math.abs(event.x - event.getHistoricalX(0))
        val deltaY = Math.abs(event.y - event.getHistoricalY(0))
        val threshold = 30 * activity.resources.displayMetrics.density
        
        return deltaX > threshold || deltaY > threshold
    }
    
    /**
     * 触发强化拦截
     */
    private fun triggerEnhancedInterception() {
        // 触发无障碍服务的强化拦截
        YoYoAccessibilityService.getInstance()?.blockSystemGestures()
        
        // 重新隐藏系统UI
        hideSystemUICompletely()
    }
    
    /**
     * 启用无障碍服务手势阻止
     */
    private fun enableAccessibilityGestureBlocking() {
        YoYoAccessibilityService.getInstance()?.blockSystemGestures()
    }
    
    /**
     * 完全隐藏系统UI
     */
    private fun hideSystemUICompletely() {
        overlayViews.forEach { view ->
            view.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
                View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
                View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
                View.SYSTEM_UI_FLAG_FULLSCREEN or
                View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            )
        }
    }
    
    /**
     * 启动手势监控
     */
    private fun startGestureMonitoring() {
        Log.d(TAG, "🔍 启动手势监控")
    }
    
    /**
     * 停止手势监控
     */
    private fun stopGestureMonitoring() {
        Log.d(TAG, "🔍 停止手势监控")
    }
    
    /**
     * 移除所有覆盖层
     */
    private fun removeAllOverlayLayers() {
        overlayViews.forEach { view ->
            try {
                windowManager.removeView(view)
            } catch (e: Exception) {
                Log.w(TAG, "移除覆盖层失败: ${e.message}")
            }
        }
        overlayViews.clear()
    }
    
    /**
     * 恢复系统UI
     */
    private fun restoreSystemUI() {
        activity.window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_VISIBLE
    }
    
    /**
     * 检查是否激活
     */
    fun isActive(): Boolean = isActive
}
