@echo off
setlocal enabledelayedexpansion

echo ========================================
echo 专注锁屏应用 - 自动诊断修复工具
echo ========================================
echo.

:: 检查ADB
echo [1/6] 检查ADB环境...
adb version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ ADB未找到，请安装Android SDK Platform Tools
    pause
    exit /b 1
)
echo ✅ ADB环境正常

:: 检查设备连接
echo.
echo [2/6] 检查设备连接...
for /f "tokens=1" %%i in ('adb devices ^| find /c "device"') do set device_count=%%i
if %device_count% lss 2 (
    echo ❌ 未检测到设备，请确保：
    echo    - 设备已连接并启用USB调试
    echo    - 已授权此计算机的USB调试
    adb devices
    pause
    exit /b 1
)
echo ✅ 设备连接正常

:: 检查应用安装
echo.
echo [3/6] 检查应用安装状态...
adb shell pm list packages | find "com.example.lockphone" >nul
if %ERRORLEVEL% neq 0 (
    echo ❌ 应用未安装，请先安装专注锁屏应用
    pause
    exit /b 1
)
echo ✅ 应用已安装

:: 检查当前权限状态
echo.
echo [4/6] 检查当前权限状态...
set has_write_secure=0
set has_write_settings=0

adb shell dumpsys package com.example.lockphone | find "android.permission.WRITE_SECURE_SETTINGS: granted=true" >nul
if %ERRORLEVEL% equ 0 set has_write_secure=1

adb shell dumpsys package com.example.lockphone | find "android.permission.WRITE_SETTINGS: granted=true" >nul
if %ERRORLEVEL% equ 0 set has_write_settings=1

echo WRITE_SECURE_SETTINGS: !has_write_secure!
echo WRITE_SETTINGS: !has_write_settings!

:: 尝试授予权限
echo.
echo [5/6] 尝试授予权限...

if !has_write_secure! equ 0 (
    echo 授予WRITE_SECURE_SETTINGS权限...
    adb shell pm grant com.example.lockphone android.permission.WRITE_SECURE_SETTINGS
    if %ERRORLEVEL% neq 0 (
        echo 常规方法失败，尝试Root方法...
        adb shell "su -c 'pm grant com.example.lockphone android.permission.WRITE_SECURE_SETTINGS'"
        if %ERRORLEVEL% neq 0 (
            echo ❌ WRITE_SECURE_SETTINGS权限授予失败
        ) else (
            echo ✅ WRITE_SECURE_SETTINGS权限授予成功（Root）
            set has_write_secure=1
        )
    ) else (
        echo ✅ WRITE_SECURE_SETTINGS权限授予成功
        set has_write_secure=1
    )
) else (
    echo ✅ WRITE_SECURE_SETTINGS权限已存在
)

if !has_write_settings! equ 0 (
    echo 授予WRITE_SETTINGS权限...
    adb shell pm grant com.example.lockphone android.permission.WRITE_SETTINGS
    if %ERRORLEVEL% neq 0 (
        echo ❌ WRITE_SETTINGS权限授予失败
    ) else (
        echo ✅ WRITE_SETTINGS权限授予成功
        set has_write_settings=1
    )
) else (
    echo ✅ WRITE_SETTINGS权限已存在
)

:: 测试功能
echo.
echo [6/6] 测试自动控制功能...

if !has_write_secure! equ 1 (
    echo 测试无障碍服务控制...
    
    :: 保存当前状态
    for /f "delims=" %%i in ('adb shell settings get secure enabled_accessibility_services') do set original_services=%%i
    for /f "delims=" %%i in ('adb shell settings get secure accessibility_enabled') do set original_enabled=%%i
    
    :: 测试启用
    adb shell settings put secure enabled_accessibility_services com.example.lockphone/.YoYoAccessibilityService
    adb shell settings put secure accessibility_enabled 1
    
    timeout /t 2 /nobreak >nul
    
    :: 检查结果
    adb shell settings get secure enabled_accessibility_services | find "lockphone" >nul
    if %ERRORLEVEL% equ 0 (
        echo ✅ 无障碍服务控制测试成功
    ) else (
        echo ❌ 无障碍服务控制测试失败
    )
    
    :: 恢复原始状态
    adb shell settings put secure enabled_accessibility_services "!original_services!"
    adb shell settings put secure accessibility_enabled !original_enabled!
    
) else (
    echo ❌ 跳过功能测试（缺少必要权限）
)

:: 生成报告
echo.
echo ========================================
echo 诊断报告
echo ========================================
echo.
echo 权限状态：
if !has_write_secure! equ 1 (
    echo ✅ WRITE_SECURE_SETTINGS: 已授予
) else (
    echo ❌ WRITE_SECURE_SETTINGS: 未授予
)

if !has_write_settings! equ 1 (
    echo ✅ WRITE_SETTINGS: 已授予
) else (
    echo ❌ WRITE_SETTINGS: 未授予
)

echo.
if !has_write_secure! equ 1 (
    echo 🎉 自动控制功能可用！
    echo    应用可以自动启用/禁用无障碍服务
) else (
    echo ⚠️  自动控制功能不可用
    echo.
    echo 可能的解决方案：
    echo 1. 确保设备已Root
    echo 2. 在开发者选项中启用"USB安装"
    echo 3. 关闭设备的安全策略限制
    echo 4. 尝试不同的ADB版本
    echo 5. 重启设备后重新运行
    echo.
    echo 手动解决方案：
    echo 1. 进入设置 → 无障碍 → 已下载的应用
    echo 2. 找到"专注锁屏服务"并手动开启
    echo 3. 专注结束后手动关闭
)

echo.
pause
