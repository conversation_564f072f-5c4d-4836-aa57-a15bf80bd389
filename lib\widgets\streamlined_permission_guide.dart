import 'package:flutter/material.dart';
import '../services/permission_manager.dart';
import '../services/focus_manager.dart' as focus_service;

/// 简化的权限引导界面
/// 基于五层防护体系，大幅简化权限要求
class StreamlinedPermissionGuide extends StatefulWidget {
  final focus_service.LockLevel targetLevel;
  final VoidCallback? onCompleted;
  final VoidCallback? onSkipped;

  const StreamlinedPermissionGuide({
    super.key,
    required this.targetLevel,
    this.onCompleted,
    this.onSkipped,
  });

  @override
  State<StreamlinedPermissionGuide> createState() =>
      _StreamlinedPermissionGuideState();
}

class _StreamlinedPermissionGuideState
    extends State<StreamlinedPermissionGuide> {
  bool _isLoading = false;
  bool _storageGranted = false;
  bool _accessibilityGranted = false;
  bool _deviceAdminGranted = false;

  @override
  void initState() {
    super.initState();
    _checkCurrentPermissions();
  }

  /// 检查当前权限状态
  Future<void> _checkCurrentPermissions() async {
    setState(() => _isLoading = true);

    try {
      final storage = await PermissionManager.instance
          .checkPermission(PermissionType.storage);
      final accessibility = await PermissionManager.instance
          .checkPermission(PermissionType.accessibility);
      final deviceAdmin = await PermissionManager.instance
          .checkPermission(PermissionType.deviceAdmin);

      setState(() {
        _storageGranted = storage == PermissionStatus.granted;
        _accessibilityGranted = accessibility == PermissionStatus.granted;
        _deviceAdminGranted = deviceAdmin == PermissionStatus.granted;
      });
    } catch (e) {
      debugPrint('检查权限状态失败: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 是否可以开始专注
  bool get _canStartFocus {
    // 基于五层防护体系，只需要存储权限即可开始
    return _storageGranted;
  }

  /// 获取防护强度描述
  String get _protectionLevel {
    if (_storageGranted && _accessibilityGranted && _deviceAdminGranted) {
      return '🛡️ 终极防护 (五层防护体系 + 系统级控制)';
    } else if (_storageGranted && _accessibilityGranted) {
      return '🔒 强力防护 (五层防护体系 + 无障碍增强)';
    } else if (_storageGranted) {
      return '🔐 标准防护 (五层防护体系)';
    } else {
      return '⚠️ 需要基础权限';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(),
          const SizedBox(height: 24),
          _buildProtectionStatus(),
          const SizedBox(height: 24),
          _buildPermissionList(),
          const SizedBox(height: 32),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        const Icon(
          Icons.security,
          size: 48,
          color: Colors.blue,
        ),
        const SizedBox(height: 16),
        Text(
          '专注模式权限配置',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 8),
        Text(
          '基于五层防护体系，提供强力的专注保护',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildProtectionStatus() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _canStartFocus
            ? Colors.green.withOpacity(0.1)
            : Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _canStartFocus ? Colors.green : Colors.orange,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            _canStartFocus ? Icons.check_circle : Icons.info,
            color: _canStartFocus ? Colors.green : Colors.orange,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _protectionLevel,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _canStartFocus ? '已准备就绪，可以开始专注模式' : '需要基础存储权限才能开始',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionList() {
    return Column(
      children: [
        _buildPermissionItem(
          icon: Icons.storage,
          title: '存储权限',
          description: '保存专注记录和设置',
          isRequired: true,
          isGranted: _storageGranted,
          onTap: () => _requestPermission(PermissionType.storage),
        ),
        const SizedBox(height: 12),
        _buildPermissionItem(
          icon: Icons.accessibility,
          title: '无障碍服务',
          description: '系统级手势拦截，增强防护效果',
          isRequired: false,
          isGranted: _accessibilityGranted,
          onTap: () => _requestPermission(PermissionType.accessibility),
        ),
        const SizedBox(height: 12),
        _buildPermissionItem(
          icon: Icons.admin_panel_settings,
          title: '设备管理员',
          description: '终极Kiosk模式，最强锁定防护',
          isRequired: false,
          isGranted: _deviceAdminGranted,
          onTap: () => _requestPermission(PermissionType.deviceAdmin),
        ),
      ],
    );
  }

  Widget _buildPermissionItem({
    required IconData icon,
    required String title,
    required String description,
    required bool isRequired,
    required bool isGranted,
    required VoidCallback onTap,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isGranted
            ? Colors.green.withOpacity(0.05)
            : Colors.grey.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isGranted
              ? Colors.green.withOpacity(0.3)
              : Colors.grey.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: isGranted ? Colors.green : Colors.grey,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: 8),
                    if (isRequired)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Text(
                          '必需',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                          ),
                        ),
                      )
                    else
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.blue,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Text(
                          '可选',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          if (isGranted)
            const Icon(
              Icons.check_circle,
              color: Colors.green,
            )
          else
            TextButton(
              onPressed: onTap,
              child: const Text('授权'),
            ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        if (!_canStartFocus) ...[
          Expanded(
            child: OutlinedButton(
              onPressed: widget.onSkipped,
              child: const Text('跳过'),
            ),
          ),
          const SizedBox(width: 16),
        ],
        Expanded(
          child: ElevatedButton(
            onPressed: _canStartFocus ? widget.onCompleted : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: _canStartFocus ? Colors.blue : Colors.grey,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    _canStartFocus ? '开始专注' : '需要基础权限',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
        ),
      ],
    );
  }

  /// 请求指定权限
  Future<void> _requestPermission(PermissionType type) async {
    setState(() => _isLoading = true);

    try {
      final success = await PermissionManager.instance.requestPermission(type);
      if (success) {
        await _checkCurrentPermissions();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${_getPermissionName(type)}已授权'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${_getPermissionName(type)}授权失败'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('请求权限失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('权限请求出错: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  String _getPermissionName(PermissionType type) {
    switch (type) {
      case PermissionType.storage:
        return '存储权限';
      case PermissionType.accessibility:
        return '无障碍服务';
      case PermissionType.deviceAdmin:
        return '设备管理员权限';
      case PermissionType.notification:
        return '通知权限';
    }
  }
}
