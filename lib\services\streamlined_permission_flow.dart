import 'package:flutter/material.dart';
import 'permission_manager.dart';
import 'permission_cache_service.dart';
import 'focus_manager.dart' show LockLevel;

/// 简化的权限授权流程
/// 重新梳理授权逻辑，提供清晰的权限管理体验
class StreamlinedPermissionFlow {
  static StreamlinedPermissionFlow? _instance;
  static StreamlinedPermissionFlow get instance =>
      _instance ??= StreamlinedPermissionFlow._();

  StreamlinedPermissionFlow._();

  /// 锁定级别对应的权限需求
  /// 优化后的权限体系：基于五层防护体系，大幅简化权限要求
  static const Map<LockLevel, List<PermissionType>> _lockLevelPermissions = {
    LockLevel.basic: [PermissionType.storage],
    LockLevel.enhanced: [PermissionType.storage],
    LockLevel.deep: [
      PermissionType.storage,
      // 可选增强权限（不强制要求）：
      // PermissionType.accessibility, // 无障碍服务 - 增强系统级监控
      // PermissionType.deviceAdmin,   // 设备管理员 - 终极Kiosk模式
    ],
  };

  /// 检查锁定级别所需权限
  Future<PermissionCheckResult> checkLockLevelPermissions(
      LockLevel level) async {
    final result = PermissionCheckResult();
    result.lockLevel = level;
    result.requiredPermissions = _lockLevelPermissions[level] ?? [];

    // 检查每个权限的状态
    for (final permission in result.requiredPermissions) {
      final status =
          await PermissionManager.instance.forceCheckPermission(permission);
      result.permissionStatus[permission] = status;

      if (status != PermissionStatus.granted) {
        result.missingPermissions.add(permission);
      }
    }

    result.allGranted = result.missingPermissions.isEmpty;
    result.canProceed = _canProceedWithLevel(level, result.permissionStatus);

    debugPrint(
        '锁定级别 ${level.name} 权限检查结果: ${result.allGranted ? "全部授予" : "缺少权限"}');
    debugPrint(
        '缺少权限: ${result.missingPermissions.map((p) => p.name).join(", ")}');

    return result;
  }

  /// 判断是否可以继续使用指定锁定级别
  bool _canProceedWithLevel(
      LockLevel level, Map<PermissionType, PermissionStatus> permissions) {
    switch (level) {
      case LockLevel.basic:
        // 基础锁定只需要存储权限
        return permissions[PermissionType.storage] == PermissionStatus.granted;

      case LockLevel.enhanced:
        // 增强锁定基于系统级手势拦截，只需要存储权限
        return permissions[PermissionType.storage] == PermissionStatus.granted;

      case LockLevel.deep:
        // 深度锁定基于五层防护体系，只需要存储权限
        // 无障碍服务和设备管理员权限为可选增强功能
        return permissions[PermissionType.storage] == PermissionStatus.granted;
    }
  }

  /// 获取实际可用的锁定级别
  LockLevel getAvailableLockLevel(LockLevel requestedLevel,
      Map<PermissionType, PermissionStatus> permissions) {
    // 检查存储权限（所有级别的基础要求）
    if (permissions[PermissionType.storage] != PermissionStatus.granted) {
      debugPrint('存储权限未授予，无法使用任何锁定功能');
      return LockLevel.basic; // 即使基础锁定也需要存储权限
    }

    // 基于五层防护体系，所有锁定级别都可用
    switch (requestedLevel) {
      case LockLevel.deep:
        debugPrint('深度锁定可用（基于五层防护体系）');
        return LockLevel.deep;
      case LockLevel.enhanced:
        debugPrint('增强锁定可用（基于系统级手势拦截）');
        return LockLevel.enhanced;
      case LockLevel.basic:
        debugPrint('基础锁定可用');
        return LockLevel.basic;
    }
  }

  /// 简化的权限请求流程
  Future<PermissionFlowResult> requestPermissionsForLevel(
    BuildContext context,
    LockLevel level,
  ) async {
    final result = PermissionFlowResult();
    result.requestedLevel = level;

    try {
      // 1. 检查当前权限状态
      final checkResult = await checkLockLevelPermissions(level);
      result.permissionCheckResult = checkResult;

      // 2. 如果权限已足够，直接返回
      if (checkResult.canProceed) {
        result.success = true;
        result.finalLevel = level;
        result.message = '权限检查通过';
        return result;
      }

      // 3. 显示权限请求对话框
      final userApproved = await _showPermissionRequestDialog(
          context, level, checkResult.missingPermissions);

      if (!userApproved) {
        result.success = false;
        result.finalLevel = LockLevel.basic; // 降级到基础级别
        result.message = '用户取消权限请求';
        return result;
      }

      // 4. 逐个请求权限
      final grantResults =
          await _requestMissingPermissions(checkResult.missingPermissions);
      result.grantResults = grantResults;

      // 5. 重新检查权限状态
      final finalCheck = await checkLockLevelPermissions(level);
      result.finalLevel =
          getAvailableLockLevel(level, finalCheck.permissionStatus);

      result.success = true;
      result.message = result.finalLevel == level ? '权限授予成功' : '部分权限未授予，已降级';

      return result;
    } catch (e) {
      debugPrint('权限请求流程失败: $e');
      result.success = false;
      result.error = e.toString();
      result.finalLevel = LockLevel.basic;
      result.message = '权限请求失败';
      return result;
    }
  }

  /// 显示权限请求对话框
  Future<bool> _showPermissionRequestDialog(
    BuildContext context,
    LockLevel level,
    List<PermissionType> missingPermissions,
  ) async {
    if (missingPermissions.isEmpty) return true;

    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            title: Text('${_getLockLevelName(level)}需要权限'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(_getLockLevelDescription(level)),
                const SizedBox(height: 16),
                const Text('需要以下权限：',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                ...missingPermissions.map((permission) => Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Row(
                        children: [
                          Icon(_getPermissionIcon(permission), size: 16),
                          const SizedBox(width: 8),
                          Expanded(
                              child:
                                  Text(_getPermissionDescription(permission))),
                        ],
                      ),
                    )),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.info_outline,
                          color: Colors.blue.shade700, size: 16),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '如果不授予某些权限，系统会自动降级到可用的锁定级别',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.blue.shade700,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('跳过'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('授予权限'),
              ),
            ],
          ),
        ) ??
        false;
  }

  /// 请求缺失的权限
  Future<Map<PermissionType, bool>> _requestMissingPermissions(
      List<PermissionType> permissions) async {
    final results = <PermissionType, bool>{};

    for (final permission in permissions) {
      try {
        final granted =
            await PermissionManager.instance.requestPermission(permission);
        results[permission] = granted;

        // 记录用户同意状态
        if (granted) {
          await PermissionCacheService.instance.recordUserConsent(permission);
        }

        debugPrint('权限 ${permission.name} 请求结果: ${granted ? "授予" : "拒绝"}');
      } catch (e) {
        debugPrint('请求权限 ${permission.name} 失败: $e');
        results[permission] = false;
      }
    }

    return results;
  }

  /// 获取锁定级别名称
  String _getLockLevelName(LockLevel level) {
    switch (level) {
      case LockLevel.basic:
        return '基础锁定';
      case LockLevel.enhanced:
        return '增强锁定';
      case LockLevel.deep:
        return '深度锁定';
    }
  }

  /// 获取锁定级别描述
  String _getLockLevelDescription(LockLevel level) {
    switch (level) {
      case LockLevel.basic:
        return '应用级锁定，阻止返回键和应用切换';
      case LockLevel.enhanced:
        return '系统级锁定，阻止手势操作和通知栏';
      case LockLevel.deep:
        return '深度锁定，完全阻止系统操作和手势';
    }
  }

  /// 获取权限图标
  IconData _getPermissionIcon(PermissionType permission) {
    switch (permission) {
      case PermissionType.storage:
        return Icons.storage;
      case PermissionType.accessibility:
        return Icons.accessibility;
      case PermissionType.deviceAdmin:
        return Icons.admin_panel_settings;
      case PermissionType.notification:
        return Icons.notifications;
    }
  }

  /// 获取权限描述
  String _getPermissionDescription(PermissionType permission) {
    switch (permission) {
      case PermissionType.storage:
        return '存储权限 - 保存专注记录';
      case PermissionType.accessibility:
        return '无障碍权限 - 系统级手势拦截';
      case PermissionType.deviceAdmin:
        return '设备管理员 - 终极Kiosk模式';
      case PermissionType.notification:
        return '通知权限 - 显示专注提醒';
    }
  }
}

/// 权限检查结果
class PermissionCheckResult {
  LockLevel lockLevel = LockLevel.basic;
  List<PermissionType> requiredPermissions = [];
  Map<PermissionType, PermissionStatus> permissionStatus = {};
  List<PermissionType> missingPermissions = [];
  bool allGranted = false;
  bool canProceed = false;
}

/// 权限流程结果
class PermissionFlowResult {
  bool success = false;
  LockLevel requestedLevel = LockLevel.basic;
  LockLevel finalLevel = LockLevel.basic;
  String message = '';
  String? error;
  PermissionCheckResult? permissionCheckResult;
  Map<PermissionType, bool> grantResults = {};
}
