package com.example.lockphone

import android.app.Activity
import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.*
import android.widget.FrameLayout

/**
 * 系统级手势拦截器
 * 使用最高优先级的系统窗口来拦截所有手势，包括底部上划
 */
class SystemGestureInterceptor(private val activity: Activity) {
    
    companion object {
        private const val TAG = "SystemGestureInterceptor"
    }
    
    private val windowManager = activity.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    private var interceptorView: View? = null
    private var isActive = false
    private var gestureBlockHandler: Handler? = null
    
    /**
     * 启用系统级手势拦截
     */
    fun enable(): Boolean {
        if (isActive) {
            Log.d(TAG, "系统手势拦截器已经启用")
            return true
        }

        try {
            Log.d(TAG, "🚫 启用系统级手势拦截器")

            // 检查悬浮窗权限
            val hasOverlayPermission = Settings.canDrawOverlays(activity)
            Log.d(TAG, "悬浮窗权限状态: ${if (hasOverlayPermission) "✅ 已授权" else "❌ 未授权"}")

            if (hasOverlayPermission) {
                // 创建系统级拦截覆盖层
                createSystemInterceptorOverlay()
                Log.d(TAG, "✅ 系统级覆盖层已创建")
            } else {
                Log.w(TAG, "⚠️ 无悬浮窗权限，跳过覆盖层创建")
            }

            // 启动持续监控（无论是否有权限都启动）
            startContinuousBlocking()

            isActive = true
            Log.d(TAG, "✅ 系统级手势拦截器已启用")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "启用系统手势拦截器失败: ${e.message}")
            return false
        }
    }
    
    /**
     * 禁用系统级手势拦截
     */
    fun disable() {
        if (!isActive) return
        
        try {
            Log.d(TAG, "禁用系统级手势拦截器")
            
            // 停止监控
            gestureBlockHandler?.removeCallbacksAndMessages(null)
            
            // 移除拦截覆盖层
            removeInterceptorOverlay()
            
            isActive = false
            Log.d(TAG, "系统级手势拦截器已禁用")
        } catch (e: Exception) {
            Log.e(TAG, "禁用系统手势拦截器失败: ${e.message}")
        }
    }
    
    /**
     * 创建系统级拦截覆盖层
     */
    private fun createSystemInterceptorOverlay() {
        removeInterceptorOverlay()
        
        interceptorView = createInterceptorView()
        val params = createSystemInterceptorParams()
        
        try {
            windowManager.addView(interceptorView, params)
            Log.d(TAG, "系统级拦截覆盖层已创建")
        } catch (e: Exception) {
            Log.e(TAG, "创建系统级拦截覆盖层失败: ${e.message}")
            interceptorView = null
        }
    }
    
    /**
     * 创建拦截器视图
     */
    private fun createInterceptorView(): View {
        val view = object : FrameLayout(activity) {
            override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
                // 在这里拦截所有触摸事件
                ev?.let { event ->
                    handleSystemGesture(event)
                }
                return true // 完全消费事件，不传递给系统
            }
            
            override fun onInterceptTouchEvent(ev: MotionEvent?): Boolean {
                // 拦截所有触摸事件
                return true
            }
            
            override fun onTouchEvent(event: MotionEvent?): Boolean {
                // 处理触摸事件
                event?.let { handleSystemGesture(it) }
                return true
            }
        }
        
        view.setBackgroundColor(0x00000000) // 完全透明
        view.isClickable = true
        view.isFocusable = true
        view.isFocusableInTouchMode = true
        
        // 设置触摸监听器作为额外保险
        view.setOnTouchListener { _, event ->
            handleSystemGesture(event)
            true
        }
        
        return view
    }
    
    /**
     * 处理系统手势
     */
    private fun handleSystemGesture(event: MotionEvent) {
        val screenHeight = activity.resources.displayMetrics.heightPixels
        val screenWidth = activity.resources.displayMetrics.widthPixels
        
        // 定义危险区域
        val bottomDangerZone = screenHeight * 0.25f // 底部25%
        val sideDangerZone = screenWidth * 0.1f     // 侧边10%
        val topDangerZone = screenHeight * 0.1f     // 顶部10%（状态栏）
        
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                val isBottomGesture = event.y > screenHeight - bottomDangerZone
                val isSideGesture = event.x < sideDangerZone || event.x > screenWidth - sideDangerZone
                val isTopGesture = event.y < topDangerZone
                
                if (isBottomGesture) {
                    Log.w(TAG, "🚫 系统级拦截：底部手势被阻止 (${event.x}, ${event.y})")
                    triggerSystemBlock()
                } else if (isSideGesture) {
                    Log.w(TAG, "🚫 系统级拦截：侧边手势被阻止 (${event.x}, ${event.y})")
                    triggerSystemBlock()
                } else if (isTopGesture) {
                    Log.w(TAG, "🚫 系统级拦截：顶部手势被阻止 (${event.x}, ${event.y})")
                    triggerSystemBlock()
                }
            }
            
            MotionEvent.ACTION_MOVE -> {
                // 检测滑动手势
                if (event.historySize > 0) {
                    val deltaY = event.getHistoricalY(0) - event.y
                    val deltaX = Math.abs(event.getHistoricalX(0) - event.x)
                    
                    // 向上滑动
                    if (deltaY > 30) {
                        Log.w(TAG, "🚫 系统级拦截：向上滑动被阻止")
                        triggerSystemBlock()
                    }
                    
                    // 侧边滑动
                    if (deltaX > 30 && (event.x < sideDangerZone || event.x > screenWidth - sideDangerZone)) {
                        Log.w(TAG, "🚫 系统级拦截：侧边滑动被阻止")
                        triggerSystemBlock()
                    }
                }
            }
            
            MotionEvent.ACTION_UP -> {
                // 手势结束，确保系统状态
                triggerSystemBlock()
            }
        }
    }
    
    /**
     * 创建系统级拦截器参数
     */
    private fun createSystemInterceptorParams(): WindowManager.LayoutParams {
        // 使用最高优先级的窗口类型
        val type = when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.O -> {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            }
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> {
                @Suppress("DEPRECATION")
                WindowManager.LayoutParams.TYPE_SYSTEM_ERROR
            }
            else -> {
                @Suppress("DEPRECATION")
                WindowManager.LayoutParams.TYPE_SYSTEM_OVERLAY
            }
        }
        
        return WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            type,
            // 关键：不使用FLAG_NOT_FOCUSABLE，确保能拦截事件
            WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                    WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS or
                    WindowManager.LayoutParams.FLAG_LAYOUT_INSET_DECOR or
                    WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED or
                    WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON or
                    WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED or
                    WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD,
            PixelFormat.TRANSLUCENT
        ).apply {
            gravity = Gravity.TOP or Gravity.LEFT
            x = 0
            y = 0
            width = WindowManager.LayoutParams.MATCH_PARENT
            height = WindowManager.LayoutParams.MATCH_PARENT
            
            // Android 9+ 刘海屏适配
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
            }
        }
    }
    
    /**
     * 启动持续阻止
     */
    private fun startContinuousBlocking() {
        gestureBlockHandler = Handler(Looper.getMainLooper())
        
        val runnable = object : Runnable {
            override fun run() {
                if (isActive) {
                    // 每20ms检查一次，确保拦截器始终有效
                    ensureInterceptorActive()
                    gestureBlockHandler?.postDelayed(this, 20)
                }
            }
        }
        
        gestureBlockHandler?.post(runnable)
        Log.d(TAG, "持续阻止监控已启动")
    }
    
    /**
     * 确保拦截器始终有效
     */
    private fun ensureInterceptorActive() {
        try {
            // 检查拦截器视图是否还在窗口中
            if (interceptorView?.parent == null) {
                Log.w(TAG, "检测到拦截器失效，重新创建")
                createSystemInterceptorOverlay()
            }
        } catch (e: Exception) {
            Log.e(TAG, "确保拦截器有效失败: ${e.message}")
        }
    }
    
    /**
     * 触发系统阻止
     */
    private fun triggerSystemBlock() {
        try {
            // 强制重新创建拦截器
            createSystemInterceptorOverlay()
            
            // 通知LockScreenManager强化锁定
            val lockManager = LockScreenManager.getInstance()
            lockManager?.reinforceLock()
            
            // 如果有无障碍服务，立即返回
            val service = YoYoAccessibilityService.getInstance()
            service?.executeImmediateReturn()
            
        } catch (e: Exception) {
            Log.e(TAG, "触发系统阻止失败: ${e.message}")
        }
    }
    
    /**
     * 移除拦截器覆盖层
     */
    private fun removeInterceptorOverlay() {
        interceptorView?.let {
            try {
                windowManager.removeView(it)
            } catch (e: Exception) {
                Log.e(TAG, "移除拦截器覆盖层失败: ${e.message}")
            }
            interceptorView = null
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        disable()
    }
}
