# 无障碍服务权限修复测试指南

## 修复内容总结

### 🔧 主要修复
1. **改进了无障碍服务检测逻辑** - 使用多种匹配方式检测服务状态
2. **添加了详细的调试日志** - 帮助诊断权限问题
3. **增加了调试信息显示** - 用户可以看到详细的服务状态
4. **修复了服务名称匹配问题** - 支持多种服务名称格式

### 🔍 修复的具体问题
- 无障碍服务检测逻辑不准确
- 缺少详细的错误信息
- 服务名称匹配方式单一
- 权限状态反馈不清晰

## 测试步骤

### 1. 重新构建应用
```bash
flutter clean
flutter pub get
flutter run
```

### 2. 测试深度锁定权限
1. 打开应用
2. 点击"开始专注"
3. 选择"深度锁定"级别
4. 点击"开始专注"
5. 观察是否显示调试信息对话框

### 3. 查看调试信息
调试信息对话框会显示：
- 应用包名
- 目标服务名称
- 已启用服务数量
- YoYo服务状态
- 所有已启用的无障碍服务列表

### 4. 手动启用无障碍服务
如果服务未启用：
1. 点击"打开设置"按钮
2. 在无障碍设置中找到"专注锁屏服务"
3. 开启服务
4. 返回应用重新测试

### 5. 验证修复效果
- ✅ 权限检查应该正确识别服务状态
- ✅ 调试信息应该显示详细的服务信息
- ✅ 深度锁定应该能正常启用

## 预期结果

### 修复前的问题
- 即使启用了无障碍服务，仍提示"权限不足"
- 无法获得详细的错误信息
- 用户不知道具体哪个权限有问题

### 修复后的效果
- 正确检测无障碍服务状态
- 显示详细的调试信息
- 提供直接跳转到设置的按钮
- 更准确的权限状态反馈

## 故障排除

如果仍然有问题，请检查：

1. **服务名称是否正确**
   - 检查AndroidManifest.xml中的服务声明
   - 确认服务类名拼写正确

2. **权限声明是否完整**
   - 确认BIND_ACCESSIBILITY_SERVICE权限已声明
   - 检查服务配置文件是否正确

3. **设备兼容性**
   - 某些设备可能有特殊的无障碍服务限制
   - 检查设备的无障碍设置页面

## 日志查看

使用以下命令查看详细日志：
```bash
flutter logs
```

关键日志标签：
- `YoYoLockScreen`: 主要权限检查日志
- `LockScreenManager`: 锁屏管理日志
- `YoYoAccessibilityService`: 无障碍服务日志
