class TimeFormatter {
  /// Formats seconds into MM:SS format
  static String formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }
  
  /// Formats duration in minutes to human readable format
  static String formatDuration(int minutes) {
    if (minutes < 60) {
      return '$minutes分钟';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      if (remainingMinutes == 0) {
        return '$hours小时';
      } else {
        return '$hours小时$remainingMinutes分钟';
      }
    }
  }
  
  /// Formats total minutes into hours and minutes
  static String formatTotalTime(int totalMinutes) {
    if (totalMinutes < 60) {
      return '$totalMinutes分钟';
    } else {
      final hours = totalMinutes ~/ 60;
      final minutes = totalMinutes % 60;
      if (minutes == 0) {
        return '$hours小时';
      } else {
        return '$hours.${(minutes / 60 * 10).round()}小时';
      }
    }
  }
  
  /// Formats DateTime to readable date string
  static String formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final targetDate = DateTime(date.year, date.month, date.day);
    
    if (targetDate == today) {
      return '今天';
    } else if (targetDate == yesterday) {
      return '昨天';
    } else {
      return '${date.month}月${date.day}日';
    }
  }
  
  /// Formats DateTime to time string (HH:MM)
  static String formatTimeOfDay(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
  
  /// Calculates completion percentage
  static double calculateProgress(int elapsedSeconds, int totalSeconds) {
    if (totalSeconds == 0) return 0.0;
    return (elapsedSeconds / totalSeconds).clamp(0.0, 1.0);
  }
  
  /// Gets remaining time in seconds
  static int getRemainingSeconds(int totalSeconds, int elapsedSeconds) {
    return (totalSeconds - elapsedSeconds).clamp(0, totalSeconds);
  }
}
