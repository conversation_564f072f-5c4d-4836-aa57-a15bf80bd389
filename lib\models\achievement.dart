class Achievement {
  final String id;
  final String title;
  final String description;
  final String icon;
  final AchievementType type;
  final int targetValue;
  final bool isUnlocked;
  final DateTime? unlockedAt;

  Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.type,
    required this.targetValue,
    this.isUnlocked = false,
    this.unlockedAt,
  });

  Achievement copyWith({
    String? id,
    String? title,
    String? description,
    String? icon,
    AchievementType? type,
    int? targetValue,
    bool? isUnlocked,
    DateTime? unlockedAt,
  }) {
    return Achievement(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      type: type ?? this.type,
      targetValue: targetValue ?? this.targetValue,
      isUnlocked: isUnlocked ?? this.isUnlocked,
      unlockedAt: unlockedAt ?? this.unlockedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'icon': icon,
      'type': type.name,
      'target_value': targetValue,
      'is_unlocked': isUnlocked ? 1 : 0,
      'unlocked_at': unlockedAt?.toIso8601String(),
    };
  }

  factory Achievement.fromMap(Map<String, dynamic> map) {
    return Achievement(
      id: map['id'],
      title: map['title'],
      description: map['description'],
      icon: map['icon'],
      type: AchievementType.values.firstWhere(
        (e) => e.name == map['type'],
      ),
      targetValue: map['target_value'],
      isUnlocked: map['is_unlocked'] == 1,
      unlockedAt: map['unlocked_at'] != null 
          ? DateTime.parse(map['unlocked_at'])
          : null,
    );
  }
}

enum AchievementType {
  firstSession,      // 首次专注
  sessionCount,      // 专注次数
  totalTime,         // 总专注时间
  streak,            // 连续天数
  longSession,       // 长时间专注
  completionRate,    // 完成率
  taskType,          // 特定任务类型
}

class AchievementDefinitions {
  static List<Achievement> get allAchievements => [
    // 首次专注
    Achievement(
      id: 'first_session',
      title: '首次锁机',
      description: '迈出专注的第一步',
      icon: '🎯',
      type: AchievementType.firstSession,
      targetValue: 1,
    ),
    
    // 专注次数成就
    Achievement(
      id: 'sessions_5',
      title: '初级专注者',
      description: '完成5次专注',
      icon: '🌱',
      type: AchievementType.sessionCount,
      targetValue: 5,
    ),
    Achievement(
      id: 'sessions_10',
      title: '专注新手',
      description: '完成10次专注',
      icon: '🌿',
      type: AchievementType.sessionCount,
      targetValue: 10,
    ),
    Achievement(
      id: 'sessions_25',
      title: '专注达人',
      description: '完成25次专注',
      icon: '🌳',
      type: AchievementType.sessionCount,
      targetValue: 25,
    ),
    Achievement(
      id: 'sessions_50',
      title: '专注大师',
      description: '完成50次专注',
      icon: '🏆',
      type: AchievementType.sessionCount,
      targetValue: 50,
    ),
    Achievement(
      id: 'sessions_100',
      title: '专注传奇',
      description: '完成100次专注',
      icon: '👑',
      type: AchievementType.sessionCount,
      targetValue: 100,
    ),
    
    // 总时间成就
    Achievement(
      id: 'time_1h',
      title: '时间新手',
      description: '累计专注1小时',
      icon: '⏰',
      type: AchievementType.totalTime,
      targetValue: 60, // minutes
    ),
    Achievement(
      id: 'time_5h',
      title: '时间管理者',
      description: '累计专注5小时',
      icon: '⏱️',
      type: AchievementType.totalTime,
      targetValue: 300,
    ),
    Achievement(
      id: 'time_10h',
      title: '时间大师',
      description: '累计专注10小时',
      icon: '🕐',
      type: AchievementType.totalTime,
      targetValue: 600,
    ),
    Achievement(
      id: 'time_25h',
      title: '时间专家',
      description: '累计专注25小时',
      icon: '⌚',
      type: AchievementType.totalTime,
      targetValue: 1500,
    ),
    Achievement(
      id: 'time_50h',
      title: '时间传奇',
      description: '累计专注50小时',
      icon: '⏳',
      type: AchievementType.totalTime,
      targetValue: 3000,
    ),
    
    // 连续天数成就
    Achievement(
      id: 'streak_3',
      title: '习惯养成',
      description: '连续专注3天',
      icon: '🔥',
      type: AchievementType.streak,
      targetValue: 3,
    ),
    Achievement(
      id: 'streak_7',
      title: '一周坚持',
      description: '连续专注7天',
      icon: '🌟',
      type: AchievementType.streak,
      targetValue: 7,
    ),
    Achievement(
      id: 'streak_14',
      title: '两周坚持',
      description: '连续专注14天',
      icon: '💫',
      type: AchievementType.streak,
      targetValue: 14,
    ),
    Achievement(
      id: 'streak_30',
      title: '月度坚持',
      description: '连续专注30天',
      icon: '🎖️',
      type: AchievementType.streak,
      targetValue: 30,
    ),
    
    // 长时间专注成就
    Achievement(
      id: 'long_45',
      title: '深度专注',
      description: '单次专注45分钟',
      icon: '🎯',
      type: AchievementType.longSession,
      targetValue: 45,
    ),
    Achievement(
      id: 'long_90',
      title: '超级专注',
      description: '单次专注90分钟',
      icon: '🚀',
      type: AchievementType.longSession,
      targetValue: 90,
    ),
    Achievement(
      id: 'long_120',
      title: '极限专注',
      description: '单次专注2小时',
      icon: '💎',
      type: AchievementType.longSession,
      targetValue: 120,
    ),
    Achievement(
      id: 'long_180',
      title: '专注之神',
      description: '单次专注3小时',
      icon: '🌌',
      type: AchievementType.longSession,
      targetValue: 180,
    ),
    
    // 完成率成就
    Achievement(
      id: 'completion_80',
      title: '自律达人',
      description: '专注完成率达到80%',
      icon: '📈',
      type: AchievementType.completionRate,
      targetValue: 80,
    ),
    Achievement(
      id: 'completion_90',
      title: '自律大师',
      description: '专注完成率达到90%',
      icon: '📊',
      type: AchievementType.completionRate,
      targetValue: 90,
    ),
    Achievement(
      id: 'completion_95',
      title: '自律传奇',
      description: '专注完成率达到95%',
      icon: '🏅',
      type: AchievementType.completionRate,
      targetValue: 95,
    ),
  ];
}
