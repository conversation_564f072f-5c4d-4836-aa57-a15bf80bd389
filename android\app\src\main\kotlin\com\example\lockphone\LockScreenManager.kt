package com.example.lockphone

import android.accessibilityservice.AccessibilityServiceInfo
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.provider.Settings
import android.util.Log
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.view.accessibility.AccessibilityManager
import android.widget.FrameLayout

/**
 * YoYo日常风格的锁屏管理器
 * 提供分级的锁屏功能
 */
class LockScreenManager(private val activity: Activity) {
    
    companion object {
        private const val TAG = "LockScreenManager"
        private var instance: LockScreenManager? = null

        fun getInstance(): LockScreenManager? = instance

        fun initialize(activity: Activity): LockScreenManager {
            instance = LockScreenManager(activity)
            instance?.bottomGestureBlocker = BottomGestureBlocker(activity)
            instance?.systemGestureInterceptor = SystemGestureInterceptor(activity)
            instance?.deviceAdminKioskManager = DeviceAdminKioskManager(activity)
            return instance!!
        }
    }

    private val windowManager: WindowManager = activity.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    private var overlayView: View? = null
    private var isLockEnabled = false
    private var currentLevel = "basic"
    private var bottomGestureBlocker: BottomGestureBlocker? = null
    private var systemGestureInterceptor: SystemGestureInterceptor? = null
    private var deviceAdminKioskManager: DeviceAdminKioskManager? = null

    /**
     * 启用锁屏功能
     */
    fun enableLockScreen(level: String): Boolean {
        Log.d(TAG, "🔒 LockScreenManager: 收到锁屏启用请求")
        Log.d(TAG, "🔒 LockScreenManager: 目标级别: $level")

        return try {
            currentLevel = level

            // 优先尝试现代化覆盖层
            val modernOverlayResult = enableModernOverlayLock()

            val result = when (level) {
                "basic" -> {
                    Log.d(TAG, "🔒 LockScreenManager: 执行基础锁定")
                    val basicResult = enableBasicLock()
                    modernOverlayResult || basicResult // 任一成功即可
                }
                "enhanced" -> {
                    Log.d(TAG, "🔒 LockScreenManager: 执行增强锁定")
                    val enhancedResult = enableEnhancedLock()
                    modernOverlayResult || enhancedResult
                }
                "deep" -> {
                    Log.d(TAG, "🔒 LockScreenManager: 执行深度锁定")
                    val deepResult = enableDeepLock()
                    modernOverlayResult || deepResult
                }
                else -> {
                    Log.w(TAG, "❌ LockScreenManager: 未知级别，降级到基础锁定")
                    val basicResult = enableBasicLock()
                    modernOverlayResult || basicResult
                }
            }

            Log.d(TAG, "🔒 LockScreenManager: 锁屏启用结果: $result (现代覆盖层: $modernOverlayResult)")
            result
        } catch (e: Exception) {
            Log.e(TAG, "❌ LockScreenManager: 启用锁屏失败: ${e.message}")
            false
        }
    }

    /**
     * 禁用锁屏功能
     */
    fun disableLockScreen(): Boolean {
        return try {
            Log.d(TAG, "🔒 LockScreenManager: 开始禁用锁屏功能")

            // 禁用现代化覆盖层
            try {
                val modernOverlayManager = ModernOverlayManager.getInstance(activity)
                modernOverlayManager.disableOverlay()
                Log.d(TAG, "✅ LockScreenManager: 现代化覆盖层已禁用")
            } catch (e: Exception) {
                Log.e(TAG, "❌ LockScreenManager: 禁用现代化覆盖层失败: ${e.message}")
            }

            // 禁用系统UI控制
            try {
                val systemUIController = SystemUIController(activity)
                systemUIController.disableImmersiveMode()
                Log.d(TAG, "✅ LockScreenManager: 系统UI控制已禁用")
            } catch (e: Exception) {
                Log.e(TAG, "❌ LockScreenManager: 禁用系统UI控制失败: ${e.message}")
            }

            // 禁用无障碍服务的锁定模式
            if (currentLevel == "deep") {
                try {
                    YoYoAccessibilityService.disableLockMode()
                    val service = YoYoAccessibilityService.getInstance()
                    service?.allowSystemGestures()
                    Log.d(TAG, "✅ LockScreenManager: 无障碍服务锁定模式已禁用")
                } catch (e: Exception) {
                    Log.e(TAG, "❌ LockScreenManager: 禁用无障碍服务锁定失败: ${e.message}")
                }
            }

            // 禁用底部手势阻止器
            bottomGestureBlocker?.disable()

            // 禁用系统级手势拦截器
            systemGestureInterceptor?.disable()

            // 禁用设备管理员Kiosk模式
            deviceAdminKioskManager?.disableKioskMode()

            removeOverlay()
            showSystemUI()
            isLockEnabled = false
            currentLevel = "basic"
            Log.d(TAG, "锁屏已禁用")
            true
        } catch (e: Exception) {
            Log.e(TAG, "禁用锁屏失败: ${e.message}")
            false
        }
    }

    /**
     * 现代化覆盖层锁定 - 2025年最佳实践
     */
    private fun enableModernOverlayLock(): Boolean {
        return try {
            Log.d(TAG, "🔒 LockScreenManager: 启用现代化覆盖层锁定")

            // 获取现代覆盖管理器
            val modernOverlayManager = ModernOverlayManager.getInstance(activity)

            // 检查悬浮窗权限
            if (!modernOverlayManager.hasOverlayPermission()) {
                Log.w(TAG, "⚠️ LockScreenManager: 缺少悬浮窗权限，无法启用现代覆盖层")
                return false
            }

            // 启用现代覆盖层
            val overlayResult = modernOverlayManager.enableModernOverlay()

            // 启用系统UI控制
            val systemUIController = SystemUIController(activity)
            val systemUIResult = systemUIController.enableFullImmersiveMode()

            val success = overlayResult && systemUIResult

            if (success) {
                Log.d(TAG, "✅ LockScreenManager: 现代化覆盖层锁定已启用")
            } else {
                Log.w(TAG, "⚠️ LockScreenManager: 现代化覆盖层锁定部分失败 (覆盖层:$overlayResult, 系统UI:$systemUIResult)")
            }

            return success

        } catch (e: Exception) {
            Log.e(TAG, "❌ LockScreenManager: 启用现代化覆盖层锁定失败: ${e.message}")
            false
        }
    }

    /**
     * 基础锁定 - Flutter界面级别 + 基础手势防护
     */
    private fun enableBasicLock(): Boolean {
        try {
            Log.d(TAG, "🔒 LockScreenManager: 启用基础锁定")

            // 1. 隐藏系统UI
            hideSystemUI()
            Log.d(TAG, "🔒 LockScreenManager: 系统UI已隐藏")

            // 2. 启用基础的底部手势阻止（如果可用）
            val bottomBlockerEnabled = bottomGestureBlocker?.enable() ?: false
            Log.d(TAG, "🔒 LockScreenManager: 基础底部手势阻止: ${if (bottomBlockerEnabled) "✅ 已启用" else "⚠️ 不可用"}")

            // 3. 启用Activity级别的触摸拦截
            enableActivityTouchInterception()

            // 4. 强化窗口标志
            enableEnhancedWindowFlags()

            currentLevel = "basic"
            isLockEnabled = true
            Log.d(TAG, "✅ LockScreenManager: 基础锁定已启用")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "❌ LockScreenManager: 启用基础锁定失败: ${e.message}")
            return false
        }
    }

    /**
     * 增强锁定 - 基于五层防护体系的强化锁定
     */
    private fun enableEnhancedLock(): Boolean {
        try {
            Log.d(TAG, "🔒 启用增强锁定 - 五层防护体系")

            // 1. 隐藏系统UI
            hideSystemUIAggressively()

            // 2. 如果有悬浮窗权限，创建覆盖层
            if (Settings.canDrawOverlays(activity)) {
                createEnhancedOverlay()
                setAntiGestureWindowFlags()
                Log.d(TAG, "✅ 悬浮窗覆盖层已启用")
            } else {
                Log.w(TAG, "⚠️ 缺少悬浮窗权限，使用无覆盖层模式")
            }

            // 3. 启用底部手势阻止器（核心防护）
            val bottomBlockerEnabled = bottomGestureBlocker?.enable() ?: false
            Log.d(TAG, "底部手势阻止器: ${if (bottomBlockerEnabled) "✅ 已启用" else "❌ 启用失败"}")

            // 4. 启用系统级手势拦截器（最强防护）
            val systemInterceptorEnabled = systemGestureInterceptor?.enable() ?: false
            Log.d(TAG, "系统级手势拦截器: ${if (systemInterceptorEnabled) "✅ 已启用" else "❌ 启用失败"}")

            // 5. 启动持续监控
            startAggressiveSystemUIMonitoring()

            // 6. 启用屏幕常亮
            enableScreenAlwaysOn()

            currentLevel = "enhanced"
            isLockEnabled = true

            Log.d(TAG, "🛡️ 增强锁定已启用 - 五层防护体系激活")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "启用增强锁定失败: ${e.message}")
            return false
        }
    }

    /**
     * 超级增强锁定 - 最强的非无障碍锁定
     */
    fun enableSuperEnhancedLock(): Boolean {
        if (!Settings.canDrawOverlays(activity)) {
            Log.w(TAG, "缺少悬浮窗权限，无法启用超级增强锁定")
            return false
        }

        try {
            // 1. 激进地隐藏系统UI
            hideSystemUIAggressively()

            // 2. 创建增强的覆盖层
            createEnhancedOverlay()

            // 3. 设置窗口标志以阻止手势
            setAntiGestureWindowFlags()

            // 4. 启动持续监控
            startAggressiveSystemUIMonitoring()

            // 5. 启用屏幕常亮
            enableScreenAlwaysOn()

            // 6. 启用专门的底部手势阻止器
            bottomGestureBlocker?.enable()

            // 7. 启用系统级手势拦截器（最强防护）
            systemGestureInterceptor?.enable()

            currentLevel = "super_enhanced"
            isLockEnabled = true
            Log.d(TAG, "超级增强锁定已启用")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "启用超级增强锁定失败: ${e.message}")
            return false
        }
    }

    /**
     * 深度锁定 - 真正的Kiosk模式
     */
    private fun enableDeepLock(): Boolean {
        // 检查无障碍服务是否可用
        val accessibilityEnabled = isAccessibilityServiceEnabled()
        if (!accessibilityEnabled) {
            Log.w(TAG, "无障碍服务未启用，使用超级增强锁定模式")
            return enableSuperEnhancedLock()
        }

        try {
            // 1. 启用真正的Kiosk模式
            enableTrueKioskMode()

            // 2. 使用最强的系统UI隐藏
            hideSystemUIAggressively()

            // 3. 创建全屏不可穿透覆盖层
            createKioskOverlay()

            // 4. 设置最强的窗口标志
            setKioskWindowFlags()

            // 5. 启用无障碍服务的锁定模式
            YoYoAccessibilityService.enableLockMode()
            val service = YoYoAccessibilityService.getInstance()
            service?.blockSystemGestures()
            Log.d(TAG, "无障碍服务锁定模式已启用")

            // 6. 启动激进的系统UI监控
            startAggressiveSystemUIMonitoring()

            // 7. 禁用系统手势
            disableSystemGestures()

            // 8. 启用专门的底部手势阻止器
            bottomGestureBlocker?.enable()

            // 9. 启用系统级手势拦截器（最强防护）
            systemGestureInterceptor?.enable()

            // 10. 启用设备管理员Kiosk模式（终极防护）
            deviceAdminKioskManager?.enableKioskMode()

            currentLevel = "deep"
            isLockEnabled = true
            Log.d(TAG, "🔒 真正的深度锁定已启用 - Kiosk模式激活")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "启用深度锁定失败: ${e.message}")
            return false
        }
    }

    /**
     * 创建悬浮窗覆盖层
     */
    private fun createOverlay() {
        removeOverlay() // 先移除已存在的覆盖层

        overlayView = createOverlayView()
        val params = createOverlayParams()

        try {
            windowManager.addView(overlayView, params)
            Log.d(TAG, "悬浮窗覆盖层已创建")
        } catch (e: Exception) {
            Log.e(TAG, "创建悬浮窗失败: ${e.message}")
            overlayView = null
        }
    }

    /**
     * 创建覆盖层视图
     */
    private fun createOverlayView(): View {
        val view = FrameLayout(activity)
        view.setBackgroundColor(0x01000000) // 几乎透明的背景

        // 拦截所有触摸事件，特别是底部向上滑动
        view.setOnTouchListener { _, event ->
            val screenHeight = activity.resources.displayMetrics.heightPixels
            val bottomZone = screenHeight * 0.1f // 底部10%区域

            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    Log.d(TAG, "拦截触摸事件: ${event.x}, ${event.y}, 屏幕高度: $screenHeight")

                    // 特别检查底部区域的触摸
                    if (event.y > screenHeight - bottomZone) {
                        Log.w(TAG, "检测到底部区域触摸，强制拦截")
                    }
                }
                MotionEvent.ACTION_MOVE -> {
                    // 检测向上滑动手势
                    if (event.y > screenHeight - bottomZone) {
                        Log.w(TAG, "检测到底部向上滑动手势，强制拦截")
                    }
                }
                MotionEvent.ACTION_UP -> {
                    Log.d(TAG, "触摸结束: ${event.x}, ${event.y}")
                }
            }
            true // 消费所有触摸事件，阻止传递给系统
        }

        // 设置视图属性以增强拦截能力
        view.isClickable = true
        view.isFocusable = true
        view.isFocusableInTouchMode = true

        return view
    }

    /**
     * 创建覆盖层参数
     */
    private fun createOverlayParams(): WindowManager.LayoutParams {
        val type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else {
            @Suppress("DEPRECATION")
            WindowManager.LayoutParams.TYPE_PHONE
        }

        return WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            type,
            // 更强的标志组合来阻止手势
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
            WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
            WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS or
            WindowManager.LayoutParams.FLAG_FULLSCREEN or
            WindowManager.LayoutParams.FLAG_LAYOUT_INSET_DECOR or
            WindowManager.LayoutParams.FLAG_LAYOUT_IN_OVERSCAN,
            PixelFormat.TRANSLUCENT
        ).apply {
            gravity = Gravity.TOP or Gravity.START
            // 设置系统UI可见性标志
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
            }
        }
    }

    /**
     * 移除覆盖层
     */
    private fun removeOverlay() {
        overlayView?.let { view ->
            try {
                windowManager.removeView(view)
                Log.d(TAG, "悬浮窗覆盖层已移除")
            } catch (e: Exception) {
                Log.e(TAG, "移除悬浮窗失败: ${e.message}")
            }
            overlayView = null
        }
    }

    /**
     * 隐藏系统UI
     */
    private fun hideSystemUI() {
        try {
            val decorView = activity.window.decorView

            // 使用最强的系统UI隐藏标志组合
            decorView.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY or
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
                View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
                View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
                View.SYSTEM_UI_FLAG_FULLSCREEN or
                View.SYSTEM_UI_FLAG_LOW_PROFILE
            )

            // 设置窗口标志
            activity.window.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
            activity.window.addFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS)
            activity.window.addFlags(WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN)

            // 监听系统UI可见性变化，确保持续隐藏
            decorView.setOnSystemUiVisibilityChangeListener { visibility ->
                if (isLockEnabled && (visibility and View.SYSTEM_UI_FLAG_FULLSCREEN) == 0) {
                    Log.w(TAG, "检测到系统UI显示，重新隐藏")
                    // 延迟重新隐藏，避免与系统冲突
                    decorView.postDelayed({
                        if (isLockEnabled) {
                            hideSystemUI()
                        }
                    }, 100)
                }
            }

            Log.d(TAG, "系统UI已隐藏")
        } catch (e: Exception) {
            Log.e(TAG, "隐藏系统UI失败: ${e.message}")
        }
    }

    /**
     * 激进地隐藏系统UI（超级增强模式）
     */
    private fun hideSystemUIAggressively() {
        try {
            val decorView = activity.window.decorView

            // 使用最强的系统UI隐藏标志组合
            decorView.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY or
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
                View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
                View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
                View.SYSTEM_UI_FLAG_FULLSCREEN or
                View.SYSTEM_UI_FLAG_LOW_PROFILE or
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            )

            // 设置更强的窗口标志
            activity.window.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
            activity.window.addFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS)
            activity.window.addFlags(WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN)
            activity.window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            activity.window.addFlags(WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD)
            activity.window.addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED)
            activity.window.addFlags(WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED)
            activity.window.addFlags(WindowManager.LayoutParams.FLAG_SECURE)

            // 移除可能干扰的标志
            activity.window.clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE)
            activity.window.clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE)

            // 设置更激进的监听器
            decorView.setOnSystemUiVisibilityChangeListener { visibility ->
                if (isLockEnabled) {
                    Log.w(TAG, "系统UI可见性变化，立即重新隐藏")
                    // 立即重新隐藏，不延迟
                    hideSystemUIAggressively()
                }
            }

            Log.d(TAG, "激进系统UI隐藏已启用")
        } catch (e: Exception) {
            Log.e(TAG, "激进隐藏系统UI失败: ${e.message}")
        }
    }

    /**
     * 显示系统UI
     */
    private fun showSystemUI() {
        try {
            val decorView = activity.window.decorView
            decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_VISIBLE
            Log.d(TAG, "系统UI已显示")
        } catch (e: Exception) {
            Log.e(TAG, "显示系统UI失败: ${e.message}")
        }
    }

    /**
     * 检查无障碍服务是否启用
     */
    private fun isAccessibilityServiceEnabled(): Boolean {
        try {
            val accessibilityManager = activity.getSystemService(Context.ACCESSIBILITY_SERVICE) as AccessibilityManager
            val enabledServices = accessibilityManager.getEnabledAccessibilityServiceList(AccessibilityServiceInfo.FEEDBACK_ALL_MASK)

            val targetServiceName = "com.example.lockphone/.YoYoAccessibilityService"
            val targetServiceClass = "com.example.lockphone.YoYoAccessibilityService"

            Log.d(TAG, "检查无障碍服务，目标服务: $targetServiceName")
            Log.d(TAG, "已启用的服务数量: ${enabledServices.size}")

            for (service in enabledServices) {
                val serviceName = service.resolveInfo.serviceInfo.name
                val packageName = service.resolveInfo.serviceInfo.packageName
                val fullName = "$packageName/$serviceName"
                val fullName2 = "$packageName/.$serviceName"

                Log.d(TAG, "已启用服务: $serviceName, 包名: $packageName")

                if (serviceName == targetServiceClass ||
                    fullName == targetServiceName ||
                    fullName2 == targetServiceName ||
                    serviceName.endsWith("YoYoAccessibilityService")) {
                    Log.d(TAG, "找到匹配的无障碍服务: $serviceName")
                    return true
                }
            }

            Log.w(TAG, "未找到YoYo无障碍服务")
            return false
        } catch (e: Exception) {
            Log.e(TAG, "检查无障碍服务失败: ${e.message}")
            return false
        }
    }

    /**
     * 创建增强的覆盖层（超级增强模式）
     */
    private fun createEnhancedOverlay() {
        removeOverlay() // 先移除已存在的覆盖层

        overlayView = createEnhancedOverlayView()
        val params = createEnhancedOverlayParams()

        try {
            windowManager.addView(overlayView, params)
            Log.d(TAG, "增强悬浮窗覆盖层已创建")
        } catch (e: Exception) {
            Log.e(TAG, "创建增强悬浮窗失败: ${e.message}")
            overlayView = null
        }
    }

    /**
     * 创建增强的覆盖层视图
     */
    private fun createEnhancedOverlayView(): View {
        val view = FrameLayout(activity)
        view.setBackgroundColor(0x02000000) // 几乎透明但能拦截事件

        // 超强的触摸事件拦截 - 专门阻止底部上划手势
        var startY = 0f
        var startX = 0f
        var startTime = 0L
        var isBottomZoneTouch = false
        var consecutiveBottomTouches = 0

        view.setOnTouchListener { _, event ->
            val screenHeight = activity.resources.displayMetrics.heightPixels
            val screenWidth = activity.resources.displayMetrics.widthPixels
            val bottomZone = screenHeight * 0.20f // 扩大底部检测区域到20%
            val sideZone = screenWidth * 0.08f // 扩大侧边检测区域到8%

            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    startY = event.y
                    startTime = System.currentTimeMillis()
                    Log.w(TAG, "拦截触摸开始: (${event.x}, ${event.y})")

                    // 检查底部手势区域 - 立即拦截
                    if (event.y > screenHeight - bottomZone) {
                        consecutiveBottomTouches++
                        Log.w(TAG, "⚠️ 检测到底部危险区域触摸，立即强制拦截 - 第${consecutiveBottomTouches}次")
                        reinforceLock()
                        startSuperProtection()
                        return@setOnTouchListener true
                    } else {
                        consecutiveBottomTouches = 0
                    }

                    // 检查侧边手势区域
                    if (event.x < sideZone || event.x > screenWidth - sideZone) {
                        Log.w(TAG, "⚠️ 检测到侧边手势区域触摸，立即拦截")
                        reinforceLock()
                        return@setOnTouchListener true
                    }
                }
                MotionEvent.ACTION_MOVE -> {
                    val deltaY = startY - event.y // 向上滑动为正值
                    val deltaTime = System.currentTimeMillis() - startTime

                    // 检测向上滑动手势
                    if (deltaY > 50 && deltaTime < 1000) { // 50像素以上的向上滑动
                        Log.w(TAG, "🚫 检测到向上滑动手势 deltaY=$deltaY, 强制拦截")
                        reinforceLock()
                        return@setOnTouchListener true
                    }

                    // 检测底部区域的任何移动
                    if (event.y > screenHeight - bottomZone || startY > screenHeight - bottomZone) {
                        Log.w(TAG, "🚫 检测到底部区域滑动，强制拦截")
                        reinforceLock()
                        return@setOnTouchListener true
                    }
                }
                MotionEvent.ACTION_UP -> {
                    val deltaY = startY - event.y
                    val deltaTime = System.currentTimeMillis() - startTime

                    Log.d(TAG, "触摸结束: (${event.x}, ${event.y}), deltaY=$deltaY, time=${deltaTime}ms")

                    // 最后检查：如果是快速向上滑动，也要拦截
                    if (deltaY > 30 && deltaTime < 800) {
                        Log.w(TAG, "🚫 检测到快速向上滑动，最终拦截")
                        reinforceLock()
                    }
                }
            }
            true // 强制消费所有触摸事件，不让系统处理
        }

        // 设置视图属性以增强拦截能力
        view.isClickable = true
        view.isFocusable = true
        view.isFocusableInTouchMode = true

        return view
    }

    /**
     * 创建增强的覆盖层参数
     */
    private fun createEnhancedOverlayParams(): WindowManager.LayoutParams {
        val type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else {
            @Suppress("DEPRECATION")
            WindowManager.LayoutParams.TYPE_PHONE
        }

        return WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            type,
            // 优化的标志组合 - 移除FLAG_NOT_FOCUSABLE以确保能接收触摸事件
            WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS or
            WindowManager.LayoutParams.FLAG_FULLSCREEN or
            WindowManager.LayoutParams.FLAG_LAYOUT_INSET_DECOR or
            WindowManager.LayoutParams.FLAG_LAYOUT_IN_OVERSCAN or
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED or
            WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON or
            WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH or
            WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL,
            PixelFormat.TRANSLUCENT
        ).apply {
            gravity = Gravity.TOP or Gravity.START
            // 设置系统UI可见性标志
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
            }
        }
    }

    /**
     * 设置反手势窗口标志
     */
    private fun setAntiGestureWindowFlags() {
        try {
            val window = activity.window

            // 基础标志
            window.addFlags(WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED)
            window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            window.addFlags(WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD)
            window.addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED)
            window.addFlags(WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON)

            // 增强的反手势标志
            window.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
            window.addFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS)
            window.addFlags(WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN)
            window.addFlags(WindowManager.LayoutParams.FLAG_SECURE)

            // 设置窗口属性以最大化覆盖
            val layoutParams = window.attributes
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            layoutParams.height = WindowManager.LayoutParams.MATCH_PARENT
            layoutParams.x = 0
            layoutParams.y = 0

            // Android 9+ 的刘海屏适配
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                layoutParams.layoutInDisplayCutoutMode =
                    WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
            }

            window.attributes = layoutParams

            Log.d(TAG, "增强反手势窗口标志已设置")
        } catch (e: Exception) {
            Log.e(TAG, "设置反手势窗口标志失败: ${e.message}")
        }
    }

    /**
     * 启动激进的系统UI监控
     */
    private fun startAggressiveSystemUIMonitoring() {
        // 每100ms检查一次系统UI状态
        val handler = android.os.Handler(android.os.Looper.getMainLooper())
        val runnable = object : Runnable {
            override fun run() {
                if (isLockEnabled && currentLevel == "super_enhanced") {
                    hideSystemUIAggressively()
                    handler.postDelayed(this, 100)
                }
            }
        }
        handler.post(runnable)
        Log.d(TAG, "激进系统UI监控已启动")
    }

    /**
     * 启用屏幕常亮
     */
    private fun enableScreenAlwaysOn() {
        try {
            activity.window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            Log.d(TAG, "屏幕常亮已启用")
        } catch (e: Exception) {
            Log.e(TAG, "启用屏幕常亮失败: ${e.message}")
        }
    }

    /**
     * 强化锁定机制
     */
    fun reinforceLock() {
        if (!isLockEnabled) return

        try {
            Log.w(TAG, "🔒 检测到逃逸尝试，立即强化锁定机制")

            // 对深度锁定和超级增强锁定都使用最强的防护
            if (currentLevel == "deep" || currentLevel == "super_enhanced") {
                // 使用最激进的系统UI隐藏
                hideSystemUIAggressively()

                // 重新创建增强覆盖层以确保完全覆盖
                createEnhancedOverlay()

                // 强制设置反手势窗口标志
                setAntiGestureWindowFlags()

                // 如果是深度锁定且有无障碍服务，重新激活手势阻止
                if (currentLevel == "deep" && isAccessibilityServiceEnabled()) {
                    try {
                        val service = YoYoAccessibilityService.getInstance()
                        service?.blockSystemGestures()
                        Log.d(TAG, "重新激活无障碍服务手势阻止")
                    } catch (e: Exception) {
                        Log.e(TAG, "重新激活无障碍服务失败: ${e.message}")
                    }
                }
            } else {
                // 普通锁定级别
                hideSystemUI()
                setAntiGestureWindowFlags()
            }

            Log.w(TAG, "🛡️ 锁定机制强化完成，当前级别: $currentLevel")
        } catch (e: Exception) {
            Log.e(TAG, "强化锁定失败: ${e.message}")
        }
    }

    /**
     * 启动超强防护模式
     */
    private fun startSuperProtection() {
        try {
            Log.w(TAG, "🛡️ 启动超强防护模式")

            // 1. 立即重新创建覆盖层
            createEnhancedOverlay()

            // 2. 强制隐藏系统UI
            hideSystemUIAggressively()

            // 3. 设置最强的窗口标志
            setKioskWindowFlags()

            // 4. 启动连续监控
            startContinuousMonitoring()

            // 5. 如果有无障碍服务，立即激活
            if (isAccessibilityServiceEnabled()) {
                val service = YoYoAccessibilityService.getInstance()
                service?.executeImmediateReturn()
            }

            // 6. 强化底部手势阻止器
            bottomGestureBlocker?.enable()

        } catch (e: Exception) {
            Log.e(TAG, "启动超强防护失败: ${e.message}")
        }
    }

    /**
     * 启动连续监控
     */
    private fun startContinuousMonitoring() {
        try {
            val handler = android.os.Handler(android.os.Looper.getMainLooper())
            val runnable = object : Runnable {
                override fun run() {
                    if (isLockEnabled) {
                        // 每50ms检查一次系统状态
                        hideSystemUIAggressively()
                        setAntiGestureWindowFlags()

                        // 继续监控
                        handler.postDelayed(this, 50)
                    }
                }
            }
            handler.post(runnable)

            Log.d(TAG, "连续监控已启动")
        } catch (e: Exception) {
            Log.e(TAG, "启动连续监控失败: ${e.message}")
        }
    }



    /**
     * 获取当前锁定状态
     */
    fun isLocked(): Boolean = isLockEnabled

    /**
     * 获取当前锁定级别
     */
    fun getCurrentLevel(): String = currentLevel

    /**
     * 启用真正的Kiosk模式
     */
    private fun enableTrueKioskMode() {
        try {
            // 1. 设置应用为Launcher（临时）
            setAsTemporaryLauncher()

            // 2. 启用沉浸式粘性模式
            enableImmersiveSticky()

            // 3. 禁用状态栏下拉
            disableStatusBarExpansion()

            Log.d(TAG, "真正的Kiosk模式已启用")
        } catch (e: Exception) {
            Log.e(TAG, "启用Kiosk模式失败: ${e.message}")
        }
    }

    /**
     * 设置为临时Launcher
     */
    private fun setAsTemporaryLauncher() {
        try {
            val intent = Intent(Intent.ACTION_MAIN)
            intent.addCategory(Intent.CATEGORY_HOME)
            intent.addCategory(Intent.CATEGORY_DEFAULT)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK

            // 启动选择器让用户选择我们的应用作为临时主屏幕
            // 注意：这需要用户手动选择，但可以阻止Home键
            Log.d(TAG, "尝试设置为临时Launcher")
        } catch (e: Exception) {
            Log.e(TAG, "设置临时Launcher失败: ${e.message}")
        }
    }

    /**
     * 启用沉浸式粘性模式
     */
    private fun enableImmersiveSticky() {
        val decorView = activity.window.decorView
        decorView.systemUiVisibility = (
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
            or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
            or View.SYSTEM_UI_FLAG_FULLSCREEN
        )

        // 设置监听器，当系统UI显示时立即重新隐藏
        decorView.setOnSystemUiVisibilityChangeListener { visibility ->
            if (isLockEnabled && (visibility and View.SYSTEM_UI_FLAG_FULLSCREEN) == 0) {
                Log.w(TAG, "系统UI被显示，立即重新隐藏")
                decorView.systemUiVisibility = (
                    View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                    or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_FULLSCREEN
                )
            }
        }
    }

    /**
     * 禁用状态栏下拉
     */
    private fun disableStatusBarExpansion() {
        try {
            val window = activity.window
            window.addFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE)

            // 立即恢复触摸，但状态栏已被禁用
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                window.clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE)
            }, 100)

            Log.d(TAG, "状态栏下拉已禁用")
        } catch (e: Exception) {
            Log.e(TAG, "禁用状态栏下拉失败: ${e.message}")
        }
    }

    /**
     * 创建Kiosk模式覆盖层
     */
    private fun createKioskOverlay() {
        removeOverlay() // 先移除已存在的覆盖层

        overlayView = createKioskOverlayView()
        val params = createKioskOverlayParams()

        try {
            windowManager.addView(overlayView, params)
            Log.d(TAG, "Kiosk模式覆盖层已创建")
        } catch (e: Exception) {
            Log.e(TAG, "创建Kiosk覆盖层失败: ${e.message}")
            overlayView = null
        }
    }

    /**
     * 创建Kiosk覆盖层视图
     */
    private fun createKioskOverlayView(): View {
        val view = FrameLayout(activity)
        view.setBackgroundColor(0x01000000) // 几乎透明

        // 完全阻止所有触摸事件传递到系统
        view.setOnTouchListener { _, event ->
            val screenHeight = activity.resources.displayMetrics.heightPixels
            val bottomZone = screenHeight * 0.25f // 扩大到25%

            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    if (event.y > screenHeight - bottomZone) {
                        Log.w(TAG, "🚫 Kiosk模式：阻止底部手势 (${event.x}, ${event.y})")
                        // 立即强化锁定
                        reinforceLock()
                        // 震动反馈
                        try {
                            val vibrator = activity.getSystemService(Context.VIBRATOR_SERVICE) as android.os.Vibrator
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                                vibrator.vibrate(android.os.VibrationEffect.createOneShot(100, android.os.VibrationEffect.DEFAULT_AMPLITUDE))
                            } else {
                                @Suppress("DEPRECATION")
                                vibrator.vibrate(100)
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "震动反馈失败: ${e.message}")
                        }
                    }
                }
                MotionEvent.ACTION_MOVE -> {
                    // 阻止任何滑动手势
                    if (event.y > screenHeight - bottomZone) {
                        Log.w(TAG, "🚫 Kiosk模式：阻止底部滑动")
                        reinforceLock()
                    }
                }
            }
            true // 完全消费事件，不传递给系统
        }

        // 设置为完全不可穿透
        view.isClickable = true
        view.isFocusable = false // 不获取焦点，避免影响应用
        view.isFocusableInTouchMode = false

        return view
    }

    /**
     * 创建Kiosk覆盖层参数
     */
    private fun createKioskOverlayParams(): WindowManager.LayoutParams {
        val type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else {
            @Suppress("DEPRECATION")
            WindowManager.LayoutParams.TYPE_SYSTEM_ERROR // 使用系统错误类型，优先级最高
        }

        return WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            type,
            // Kiosk模式专用标志组合
            WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS or
            WindowManager.LayoutParams.FLAG_FULLSCREEN or
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED or
            WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON or
            WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD or
            WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED or
            WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON,
            PixelFormat.TRANSLUCENT
        ).apply {
            gravity = Gravity.TOP or Gravity.START
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
            }
        }
    }

    /**
     * 设置Kiosk窗口标志
     */
    private fun setKioskWindowFlags() {
        try {
            val window = activity.window

            // 设置最强的窗口标志组合
            window.addFlags(WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED)
            window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            window.addFlags(WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD)
            window.addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED)
            window.addFlags(WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON)
            window.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)

            // 设置窗口属性
            window.attributes = window.attributes.apply {
                flags = flags or WindowManager.LayoutParams.FLAG_FULLSCREEN
            }

            Log.d(TAG, "Kiosk窗口标志已设置")
        } catch (e: Exception) {
            Log.e(TAG, "设置Kiosk窗口标志失败: ${e.message}")
        }
    }

    /**
     * 禁用系统手势
     */
    private fun disableSystemGestures() {
        try {
            // 通过无障碍服务禁用系统手势
            val service = YoYoAccessibilityService.getInstance()
            service?.let {
                it.blockSystemGestures()
                Log.d(TAG, "系统手势已通过无障碍服务禁用")
            }
        } catch (e: Exception) {
            Log.e(TAG, "禁用系统手势失败: ${e.message}")
        }
    }

    /**
     * 启动系统UI监控
     */
    private fun startSystemUIMonitoring() {
        val handler = android.os.Handler(android.os.Looper.getMainLooper())
        val runnable = object : Runnable {
            override fun run() {
                if (isLockEnabled) {
                    // 检查系统UI状态并重新隐藏
                    val decorView = activity.window.decorView
                    val currentVisibility = decorView.systemUiVisibility

                    if ((currentVisibility and View.SYSTEM_UI_FLAG_FULLSCREEN) == 0) {
                        Log.w(TAG, "检测到系统UI显示，重新隐藏")
                        hideSystemUI()
                    }

                    // 继续监控
                    handler.postDelayed(this, 500) // 每500ms检查一次
                }
            }
        }
        handler.post(runnable)
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            disableLockScreen()
            bottomGestureBlocker?.cleanup()
            bottomGestureBlocker = null
            systemGestureInterceptor?.cleanup()
            systemGestureInterceptor = null
            deviceAdminKioskManager?.cleanup()
            deviceAdminKioskManager = null
            instance = null
            Log.d(TAG, "LockScreenManager资源已清理")
        } catch (e: Exception) {
            Log.e(TAG, "清理资源失败: ${e.message}")
        }
    }

    /**
     * 启用Activity级别的触摸拦截
     */
    private fun enableActivityTouchInterception() {
        try {
            Log.d(TAG, "🔒 LockScreenManager: 启用Activity触摸拦截")

            val contentView = activity.findViewById<android.view.View>(android.R.id.content)
            contentView?.setOnTouchListener { _, event ->
                val y = event.y
                val screenHeight = activity.resources.displayMetrics.heightPixels
                val bottomThreshold = screenHeight * 0.9f // 底部10%区域

                if (y > bottomThreshold && event.action == android.view.MotionEvent.ACTION_MOVE) {
                    Log.w(TAG, "🚫 LockScreenManager: Activity层拦截底部手势 (y=$y, threshold=$bottomThreshold)")
                    return@setOnTouchListener true // 消费事件
                }
                false // 允许其他区域的触摸
            }

            Log.d(TAG, "✅ LockScreenManager: Activity触摸拦截已启用")
        } catch (e: Exception) {
            Log.e(TAG, "❌ LockScreenManager: 启用Activity触摸拦截失败: ${e.message}")
        }
    }

    /**
     * 启用强化窗口标志
     */
    private fun enableEnhancedWindowFlags() {
        try {
            Log.d(TAG, "🔒 LockScreenManager: 启用强化窗口标志")

            activity.window.apply {
                // 添加更多窗口标志来阻止手势
                addFlags(
                    WindowManager.LayoutParams.FLAG_FULLSCREEN or
                    WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON or
                    WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD or
                    WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED or
                    WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON
                )

                // 设置更激进的系统UI隐藏
                decorView.systemUiVisibility = (
                    android.view.View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
                    android.view.View.SYSTEM_UI_FLAG_FULLSCREEN or
                    android.view.View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY or
                    android.view.View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or
                    android.view.View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
                    android.view.View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                )
            }

            Log.d(TAG, "✅ LockScreenManager: 强化窗口标志已启用")
        } catch (e: Exception) {
            Log.e(TAG, "❌ LockScreenManager: 启用强化窗口标志失败: ${e.message}")
        }
    }
}
