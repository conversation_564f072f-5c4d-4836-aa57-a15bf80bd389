import 'package:flutter/material.dart';
import 'permission_manager.dart';
import 'permission_cache_service.dart';
import 'permission_diagnostic_service.dart';
import '../widgets/enhanced_accessibility_permission_dialog.dart';

/// 优化的权限授权流程
/// 提供更准确和用户友好的权限管理体验
class OptimizedPermissionFlow {
  static OptimizedPermissionFlow? _instance;
  static OptimizedPermissionFlow get instance =>
      _instance ??= OptimizedPermissionFlow._();

  OptimizedPermissionFlow._();

  /// 智能权限检查和授权流程
  /// 这是主要的权限处理入口点
  Future<PermissionFlowResult> handlePermissionFlow(
    BuildContext context,
    PermissionType type, {
    bool showDiagnosticOnFailure = true,
  }) async {
    final result = PermissionFlowResult();

    try {
      debugPrint('开始优化权限流程: ${type.name}');

      // 第一步：诊断当前权限状态
      final diagnostic =
          await PermissionDiagnosticService.instance.diagnosePermission(type);
      result.diagnosticResult = diagnostic;

      debugPrint(
          '权限诊断完成: 系统状态=${diagnostic.systemStatus.name}, 一致性=${diagnostic.isConsistent}');

      // 第二步：如果权限已授予，直接返回成功
      if (diagnostic.systemStatus == PermissionStatus.granted) {
        // 确保缓存状态一致
        if (!diagnostic.isConsistent) {
          debugPrint('权限已授予但缓存不一致，修复中...');
          await PermissionDiagnosticService.instance
              .fixPermissionInconsistency(type);
        }

        result.success = true;
        result.finalStatus = PermissionStatus.granted;
        result.message = '权限已授予';
        return result;
      }

      // 第三步：权限未授予，根据用户历史决定策略
      final smartResult =
          await PermissionCacheService.instance.smartPermissionCheck(type);
      result.smartCheckResult = smartResult;

      debugPrint(
          '智能检查结果: canAutoEnable=${smartResult.canAutoEnable}, shouldShowRationale=${smartResult.shouldShowRationale}');

      // 第四步：尝试自动启用（如果条件满足）
      if (smartResult.canAutoEnable && type == PermissionType.accessibility) {
        debugPrint('尝试自动启用无障碍服务...');

        final autoEnabled =
            await PermissionManager.instance.enableAccessibilityServiceAuto();
        result.autoEnableAttempted = true;
        result.autoEnableSuccess = autoEnabled;

        if (autoEnabled) {
          // 记录自动启用尝试
          await PermissionCacheService.instance.recordAutoEnableAttempt(type);

          // 等待一下让系统状态更新
          await Future.delayed(const Duration(milliseconds: 1000));

          // 强制检查最新状态
          final finalStatus =
              await PermissionManager.instance.forceCheckPermission(type);

          if (finalStatus == PermissionStatus.granted) {
            result.success = true;
            result.finalStatus = PermissionStatus.granted;
            result.message = '自动启用成功';
            return result;
          } else {
            debugPrint('自动启用失败，权限状态仍为: ${finalStatus.name}');
          }
        }
      }

      // 第五步：显示权限对话框
      if (context.mounted) {
        final dialogResult =
            await _showPermissionDialog(context, type, smartResult);
        result.userInteracted = true;
        result.userGranted = dialogResult;

        if (dialogResult) {
          // 用户完成了权限设置，再次检查状态
          await Future.delayed(const Duration(milliseconds: 500));
          final finalStatus =
              await PermissionManager.instance.forceCheckPermission(type);

          result.success = finalStatus == PermissionStatus.granted;
          result.finalStatus = finalStatus;
          result.message = result.success ? '权限授予成功' : '权限仍未授予';
        } else {
          result.success = false;
          result.finalStatus = PermissionStatus.denied;
          result.message = '用户取消授权';
        }
      }

      // 第六步：如果仍然失败且启用了诊断，显示诊断信息
      if (!result.success && showDiagnosticOnFailure && context.mounted) {
        await _showDiagnosticDialog(context, diagnostic);
      }

      return result;
    } catch (e) {
      debugPrint('权限流程处理失败: $e');
      result.success = false;
      result.error = e.toString();
      result.message = '权限处理失败: $e';
      return result;
    }
  }

  /// 显示权限对话框
  Future<bool> _showPermissionDialog(
    BuildContext context,
    PermissionType type,
    PermissionCheckResult smartResult,
  ) async {
    if (type == PermissionType.accessibility) {
      return await _showAccessibilityPermissionDialog(context);
    } else {
      return await _showGenericPermissionDialog(context, type, smartResult);
    }
  }

  /// 显示无障碍服务权限对话框
  Future<bool> _showAccessibilityPermissionDialog(BuildContext context) async {
    bool result = false;

    await showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => EnhancedAccessibilityPermissionDialog(
        onPermissionGranted: () {
          result = true;
          Navigator.of(context).pop();
        },
        onPermissionDenied: () {
          result = false;
          Navigator.of(context).pop();
        },
      ),
    );

    return result;
  }

  /// 显示通用权限对话框
  Future<bool> _showGenericPermissionDialog(
    BuildContext context,
    PermissionType type,
    PermissionCheckResult smartResult,
  ) async {
    final permissionInfo = PermissionManager.instance.getPermissionInfo(type);

    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            title: Text('需要${permissionInfo.title}权限'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(permissionInfo.description),
                if (smartResult.hasUserConsent &&
                    smartResult.hasAutoEnableAttempted)
                  const Padding(
                    padding: EdgeInsets.only(top: 16),
                    child: Text(
                      '检测到您之前已经同意过此权限，但自动启用失败。请手动在设置中启用。',
                      style: TextStyle(color: Colors.orange),
                    ),
                  ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('稍后设置'),
              ),
              ElevatedButton(
                onPressed: () async {
                  await PermissionCacheService.instance.recordUserConsent(type);
                  Navigator.of(context).pop(true);
                },
                child: const Text('立即设置'),
              ),
            ],
          ),
        ) ??
        false;
  }

  /// 显示诊断对话框
  Future<void> _showDiagnosticDialog(
    BuildContext context,
    PermissionDiagnosticResult diagnostic,
  ) async {
    final report = PermissionDiagnosticService.instance
        .generateDiagnosticReport(diagnostic);

    await showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('权限诊断信息'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: SingleChildScrollView(
            child: Text(
              report,
              style: const TextStyle(
                fontFamily: 'monospace',
                fontSize: 12,
              ),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
          if (diagnostic.recommendations.isNotEmpty)
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await PermissionDiagnosticService.instance
                    .fixPermissionInconsistency(diagnostic.permissionType);
              },
              child: const Text('尝试修复'),
            ),
        ],
      ),
    );
  }

  /// 快速权限检查（仅检查，不显示对话框）
  Future<bool> quickPermissionCheck(PermissionType type) async {
    try {
      final status =
          await PermissionManager.instance.forceCheckPermission(type);
      return status == PermissionStatus.granted;
    } catch (e) {
      debugPrint('快速权限检查失败: $e');
      return false;
    }
  }

  /// 批量权限检查
  Future<Map<PermissionType, bool>> batchPermissionCheck(
      List<PermissionType> types) async {
    final results = <PermissionType, bool>{};

    for (final type in types) {
      results[type] = await quickPermissionCheck(type);
    }

    return results;
  }
}

/// 权限流程处理结果
class PermissionFlowResult {
  bool success = false;
  PermissionStatus finalStatus = PermissionStatus.unknown;
  String message = '';
  String? error;

  // 诊断信息
  PermissionDiagnosticResult? diagnosticResult;
  PermissionCheckResult? smartCheckResult;

  // 流程信息
  bool autoEnableAttempted = false;
  bool autoEnableSuccess = false;
  bool userInteracted = false;
  bool userGranted = false;

  @override
  String toString() {
    return 'PermissionFlowResult('
        'success: $success, '
        'finalStatus: ${finalStatus.name}, '
        'message: $message, '
        'autoEnableAttempted: $autoEnableAttempted, '
        'autoEnableSuccess: $autoEnableSuccess, '
        'userInteracted: $userInteracted, '
        'userGranted: $userGranted'
        ')';
  }
}
