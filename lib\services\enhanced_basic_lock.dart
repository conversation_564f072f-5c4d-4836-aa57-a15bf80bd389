import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';

/// 增强的基础锁定服务
/// 在没有无障碍权限的情况下，提供尽可能强的锁定效果
class EnhancedBasicLock {
  static EnhancedBasicLock? _instance;
  static EnhancedBasicLock get instance => _instance ??= EnhancedBasicLock._();

  EnhancedBasicLock._();

  bool _isLockActive = false;
  Timer? _gestureDetectionTimer;
  Timer? _appStateMonitorTimer;
  int _backPressCount = 0;
  DateTime? _lastBackPress;

  // 配置参数
  static const int _backPressThreshold = 10; // 需要连续按返回键10次才能退出
  static const Duration _backPressWindow = Duration(seconds: 3); // 3秒内的按键才算连续
  static const Duration _gestureDetectionInterval =
      Duration(milliseconds: 100); // 手势检测间隔
  static const Duration _appStateCheckInterval =
      Duration(seconds: 1); // 应用状态检查间隔

  /// 启用增强基础锁定
  Future<bool> enableLock() async {
    if (_isLockActive) {
      debugPrint('增强基础锁定已经启用');
      return true;
    }

    try {
      debugPrint('启用增强基础锁定');

      // 1. 隐藏系统UI
      await _hideSystemUI();

      // 2. 启动手势检测
      _startGestureDetection();

      // 3. 启动应用状态监控
      _startAppStateMonitoring();

      // 4. 设置系统UI标志
      await _setSystemUIFlags();

      _isLockActive = true;
      debugPrint('增强基础锁定已启用');
      return true;
    } catch (e) {
      debugPrint('启用增强基础锁定失败: $e');
      return false;
    }
  }

  /// 禁用增强基础锁定
  Future<void> disableLock() async {
    if (!_isLockActive) return;

    debugPrint('禁用增强基础锁定');

    // 停止所有监控
    _gestureDetectionTimer?.cancel();
    _appStateMonitorTimer?.cancel();

    // 恢复系统UI
    await _showSystemUI();

    _isLockActive = false;
    _backPressCount = 0;
    _lastBackPress = null;

    debugPrint('增强基础锁定已禁用');
  }

  /// 处理返回键按下
  bool handleBackPress() {
    if (!_isLockActive) {
      debugPrint('锁定未启用，不拦截返回键');
      return false;
    }

    final now = DateTime.now();

    // 检查是否在时间窗口内
    if (_lastBackPress == null ||
        now.difference(_lastBackPress!) > _backPressWindow) {
      _backPressCount = 1;
    } else {
      _backPressCount++;
    }

    _lastBackPress = now;

    debugPrint('返回键按下次数: $_backPressCount/$_backPressThreshold');

    // 如果达到阈值，允许退出
    if (_backPressCount >= _backPressThreshold) {
      debugPrint('达到返回键阈值，允许退出');
      return false; // 允许系统处理返回键
    }

    // 显示提示信息
    _showBackPressHint();

    return true; // 拦截返回键
  }

  /// 隐藏系统UI
  Future<void> _hideSystemUI() async {
    await SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.immersiveSticky,
      overlays: [],
    );
  }

  /// 显示系统UI
  Future<void> _showSystemUI() async {
    await SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.edgeToEdge,
      overlays: SystemUiOverlay.values,
    );
  }

  /// 设置系统UI标志
  Future<void> _setSystemUIFlags() async {
    // 设置屏幕常亮
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);

    // 隐藏状态栏和导航栏
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        systemNavigationBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarIconBrightness: Brightness.light,
      ),
    );
  }

  /// 启动手势检测
  void _startGestureDetection() {
    _gestureDetectionTimer = Timer.periodic(_gestureDetectionInterval, (timer) {
      if (!_isLockActive) {
        timer.cancel();
        return;
      }

      // 定期重新隐藏系统UI，防止用户通过手势调出
      _hideSystemUI();
    });
  }

  /// 启动应用状态监控
  void _startAppStateMonitoring() {
    _appStateMonitorTimer = Timer.periodic(_appStateCheckInterval, (timer) {
      if (!_isLockActive) {
        timer.cancel();
        return;
      }

      // 这里可以添加应用状态检查逻辑
      // 例如检查应用是否仍在前台
      _checkAppState();
    });
  }

  /// 检查应用状态
  void _checkAppState() {
    // 重新设置系统UI标志，确保锁定状态
    _setSystemUIFlags();
  }

  /// 显示返回键提示
  void _showBackPressHint() {
    // 这里可以通过回调通知UI显示提示
    // 例如："连续按返回键 $_backPressCount/$_backPressThreshold 次可退出专注"
  }

  /// 获取当前锁定状态
  bool get isLockActive => _isLockActive;

  /// 获取返回键按下次数
  int get backPressCount => _backPressCount;

  /// 获取返回键阈值
  int get backPressThreshold => _backPressThreshold;

  /// 重置返回键计数
  void resetBackPressCount() {
    _backPressCount = 0;
    _lastBackPress = null;
  }
}

/// 增强基础锁定界面Widget
class EnhancedBasicLockScreen extends StatefulWidget {
  final String taskName;
  final int remainingSeconds;
  final VoidCallback? onEmergencyExit;
  final VoidCallback? onTimeUp;

  const EnhancedBasicLockScreen({
    super.key,
    required this.taskName,
    required this.remainingSeconds,
    this.onEmergencyExit,
    this.onTimeUp,
  });

  @override
  State<EnhancedBasicLockScreen> createState() =>
      _EnhancedBasicLockScreenState();
}

class _EnhancedBasicLockScreenState extends State<EnhancedBasicLockScreen>
    with WidgetsBindingObserver {
  late Timer _uiUpdateTimer;
  String _backPressHint = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // 启动UI更新定时器
    _uiUpdateTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          // 更新返回键提示
          final lockService = EnhancedBasicLock.instance;
          if (lockService.backPressCount > 0) {
            _backPressHint =
                '连续按返回键 ${lockService.backPressCount}/${lockService.backPressThreshold} 次可退出专注';
          } else {
            _backPressHint = '';
          }
        });
      }
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _uiUpdateTimer.cancel();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // 当应用状态改变时，重新应用锁定设置
    if (state == AppLifecycleState.resumed) {
      EnhancedBasicLock.instance._hideSystemUI();
      EnhancedBasicLock.instance._setSystemUIFlags();
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          // 处理返回键
          EnhancedBasicLock.instance.handleBackPress();
        }
      },
      child: Scaffold(
        backgroundColor: Colors.black,
        body: Container(
          width: double.infinity,
          height: double.infinity,
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color(0xFF1a1a2e),
                Color(0xFF16213e),
                Color(0xFF0f3460),
              ],
            ),
          ),
          child: SafeArea(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 任务名称
                Text(
                  widget.taskName,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 40),

                // 倒计时显示
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.3),
                      width: 2,
                    ),
                  ),
                  child: Text(
                    _formatTime(widget.remainingSeconds),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 48,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'monospace',
                    ),
                  ),
                ),

                const SizedBox(height: 40),

                // 专注提示
                const Text(
                  '专注进行中...',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 18,
                  ),
                ),

                const SizedBox(height: 20),

                // 返回键提示
                if (_backPressHint.isNotEmpty)
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 20),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.orange.withOpacity(0.5),
                      ),
                    ),
                    child: Text(
                      _backPressHint,
                      style: const TextStyle(
                        color: Colors.orange,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),

                const Spacer(),

                // 底部提示
                Container(
                  margin: const EdgeInsets.all(20),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      const Icon(
                        Icons.info_outline,
                        color: Colors.white60,
                        size: 20,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '基础锁定模式\n连续按返回键 ${EnhancedBasicLock.instance.backPressThreshold} 次可退出',
                        style: const TextStyle(
                          color: Colors.white60,
                          fontSize: 12,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 格式化时间显示
  String _formatTime(int seconds) {
    final hours = seconds ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    final secs = seconds % 60;

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
    }
  }
}
