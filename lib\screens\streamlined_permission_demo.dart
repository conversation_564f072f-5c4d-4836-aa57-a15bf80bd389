import 'package:flutter/material.dart';
import '../services/streamlined_permission_flow.dart';
import '../services/unified_lock_manager.dart';
import '../services/enhanced_basic_lock.dart';
import '../services/focus_manager.dart' as focus;

/// 简化授权流程演示页面
class StreamlinedPermissionDemo extends StatefulWidget {
  const StreamlinedPermissionDemo({super.key});

  @override
  State<StreamlinedPermissionDemo> createState() =>
      _StreamlinedPermissionDemoState();
}

class _StreamlinedPermissionDemoState extends State<StreamlinedPermissionDemo> {
  String _statusText = '准备就绪';
  bool _isLoading = false;
  focus.LockLevel _selectedLevel = focus.LockLevel.deep;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('简化授权流程演示'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // 锁定级别选择
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '选择锁定级别',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      ...focus.LockLevel.values
                          .map((level) => RadioListTile<focus.LockLevel>(
                                title: Text(_getLockLevelName(level)),
                                subtitle: Text(_getLockLevelDescription(level)),
                                value: level,
                                groupValue: _selectedLevel,
                                onChanged: (value) {
                                  setState(() {
                                    _selectedLevel = value!;
                                  });
                                },
                              )),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // 状态显示
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '状态信息',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: double.infinity,
                        height: 150,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey[300]!),
                        ),
                        child: SingleChildScrollView(
                          child: Text(
                            _statusText,
                            style: const TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // 操作按钮
              if (_isLoading)
                const Center(child: CircularProgressIndicator())
              else
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: [
                    ElevatedButton.icon(
                      onPressed: _checkPermissions,
                      icon: const Icon(Icons.search),
                      label: const Text('检查权限'),
                    ),
                    ElevatedButton.icon(
                      onPressed: _requestPermissions,
                      icon: const Icon(Icons.security),
                      label: const Text('申请权限'),
                    ),
                    ElevatedButton.icon(
                      onPressed: _testLock,
                      icon: const Icon(Icons.lock),
                      label: const Text('测试锁定'),
                    ),
                    ElevatedButton.icon(
                      onPressed: _testBasicLock,
                      icon: const Icon(Icons.lock_outline),
                      label: const Text('测试基础锁定'),
                    ),
                    ElevatedButton.icon(
                      onPressed: _clearStatus,
                      icon: const Icon(Icons.clear),
                      label: const Text('清除状态'),
                    ),
                  ],
                ),

              const SizedBox(height: 16),

              // 说明信息
              Card(
                color: Colors.blue.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.info_outline, color: Colors.blue.shade700),
                          const SizedBox(width: 8),
                          Text(
                            '授权流程说明',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      const Text(
                        '1. 基础锁定：仅需存储权限，连续按返回键10次可退出\n'
                        '2. 增强锁定：需要悬浮窗权限，提供更强的锁定效果\n'
                        '3. 深度锁定：需要无障碍权限，提供最强的锁定效果\n'
                        '4. 智能降级：权限不足时自动降级到可用的最高级别',
                        style: TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 检查权限状态
  Future<void> _checkPermissions() async {
    setState(() {
      _isLoading = true;
      _statusText = '正在检查权限状态...';
    });

    try {
      final result = await StreamlinedPermissionFlow.instance
          .checkLockLevelPermissions(_selectedLevel);

      String message = '权限检查结果:\n';
      message += '锁定级别: ${_getLockLevelName(_selectedLevel)}\n';
      message +=
          '所需权限: ${result.requiredPermissions.map((p) => p.name).join(', ')}\n';
      message += '全部授予: ${result.allGranted ? '是' : '否'}\n';
      message += '可以继续: ${result.canProceed ? '是' : '否'}\n';

      if (result.missingPermissions.isNotEmpty) {
        message +=
            '缺少权限: ${result.missingPermissions.map((p) => p.name).join(', ')}\n';
      }

      // 检查实际可用级别
      final availableLevel = StreamlinedPermissionFlow.instance
          .getAvailableLockLevel(_selectedLevel, result.permissionStatus);

      if (availableLevel != _selectedLevel) {
        message += '建议级别: ${_getLockLevelName(availableLevel)}\n';
      }

      setState(() {
        _statusText = message;
      });
    } catch (e) {
      setState(() {
        _statusText = '检查权限失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 申请权限
  Future<void> _requestPermissions() async {
    setState(() {
      _isLoading = true;
      _statusText = '正在申请权限...';
    });

    try {
      final result = await StreamlinedPermissionFlow.instance
          .requestPermissionsForLevel(context, _selectedLevel);

      String message = '权限申请结果:\n';
      message += '申请成功: ${result.success ? '是' : '否'}\n';
      message += '请求级别: ${_getLockLevelName(result.requestedLevel)}\n';
      message += '最终级别: ${_getLockLevelName(result.finalLevel)}\n';
      message += '消息: ${result.message}\n';

      if (result.grantResults.isNotEmpty) {
        message += '权限结果:\n';
        result.grantResults.forEach((type, granted) {
          message += '  ${type.name}: ${granted ? '授予' : '拒绝'}\n';
        });
      }

      setState(() {
        _statusText = message;
      });
    } catch (e) {
      setState(() {
        _statusText = '申请权限失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 测试锁定
  Future<void> _testLock() async {
    setState(() {
      _isLoading = true;
      _statusText = '正在测试锁定...';
    });

    try {
      final result =
          await UnifiedLockManager.instance.enableLock(context, _selectedLevel);

      String message = '锁定测试结果:\n';
      message += '启用成功: ${result.success ? '是' : '否'}\n';
      message +=
          '请求级别: ${_getLockLevelName(result.requestedLevel ?? _selectedLevel)}\n';
      message += '实际级别: ${_getLockLevelName(result.actualLevel)}\n';
      message += '消息: ${result.message}\n';

      if (result.success) {
        message += '\n锁定已启用，3秒后自动禁用...';

        // 3秒后自动禁用
        Future.delayed(const Duration(seconds: 3), () async {
          await UnifiedLockManager.instance.disableLock();
          setState(() {
            _statusText += '\n锁定已禁用';
          });
        });
      }

      setState(() {
        _statusText = message;
      });
    } catch (e) {
      setState(() {
        _statusText = '测试锁定失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 测试基础锁定
  Future<void> _testBasicLock() async {
    setState(() {
      _isLoading = true;
      _statusText = '正在测试基础锁定...';
    });

    try {
      final basicLock = EnhancedBasicLock.instance;
      final success = await basicLock.enableLock();

      String message = '基础锁定测试结果:\n';
      message += '启用成功: ${success ? '是' : '否'}\n';
      message += '锁定状态: ${basicLock.isLockActive ? '已启用' : '未启用'}\n';
      message += '返回键阈值: ${basicLock.backPressThreshold}\n';
      message += '当前按键次数: ${basicLock.backPressCount}\n';

      if (success) {
        message += '\n基础锁定已启用，3秒后自动禁用...';
        message += '\n提示：在真实使用中，需要连续按返回键${basicLock.backPressThreshold}次才能退出';

        // 3秒后自动禁用
        Future.delayed(const Duration(seconds: 3), () async {
          await basicLock.disableLock();
          setState(() {
            _statusText += '\n基础锁定已禁用';
          });
        });
      }

      setState(() {
        _statusText = message;
      });
    } catch (e) {
      setState(() {
        _statusText = '测试基础锁定失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 清除状态
  void _clearStatus() {
    setState(() {
      _statusText = '状态已清除';
    });
  }

  /// 获取锁定级别名称
  String _getLockLevelName(focus.LockLevel level) {
    switch (level) {
      case focus.LockLevel.basic:
        return '基础锁定';
      case focus.LockLevel.enhanced:
        return '增强锁定';
      case focus.LockLevel.deep:
        return '深度锁定';
    }
  }

  /// 获取锁定级别描述
  String _getLockLevelDescription(focus.LockLevel level) {
    switch (level) {
      case focus.LockLevel.basic:
        return '仅需存储权限，连续按返回键10次可退出';
      case focus.LockLevel.enhanced:
        return '需要悬浮窗权限，阻止手势和通知';
      case focus.LockLevel.deep:
        return '需要无障碍权限，完全阻止系统操作';
    }
  }
}
