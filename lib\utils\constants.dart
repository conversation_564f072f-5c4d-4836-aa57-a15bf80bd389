import 'package:flutter/material.dart';

class AppColors {
  static const Color background = Color(0xFF000000);
  static const Color surface = Color(0xFF1a1a1a);
  static const Color surfaceVariant = Color(0xFF2a2a2a);
  static const Color primary = Color(0xFF00ffff);
  static const Color secondary = Color(0xFFff00ff);
  static const Color onBackground = Color(0xFFffffff);
  static const Color onSurface = Color(0xFFe0e0e0);
  static const Color onSurfaceVariant = Color(0xFF888888);
  static const Color error = Color(0xFFff6b6b);
  static const Color success = Color(0xFF00ff88);

  // Gradient colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, secondary],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient backgroundGradient = LinearGradient(
    colors: [Color(0xFF0f0f0f), Color(0xFF1a1a1a)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
}

class AppTextStyles {
  static const String fontFamily = 'SF Pro Display';
  static const String timerFontFamily = 'Orbitron';

  static const TextStyle timerLarge = TextStyle(
    fontSize: 72,
    fontWeight: FontWeight.w900,
    color: AppColors.primary,
    fontFamily: timerFontFamily,
    height: 1.0,
  );

  static const TextStyle titleLarge = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w600,
    color: AppColors.onBackground,
    fontFamily: fontFamily,
  );

  static const TextStyle titleMedium = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    color: AppColors.primary,
    fontFamily: fontFamily,
  );

  static const TextStyle bodyLarge = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w400,
    color: AppColors.onSurface,
    fontFamily: fontFamily,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: AppColors.onSurface,
    fontFamily: fontFamily,
  );

  static const TextStyle bodySmall = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    color: AppColors.onSurfaceVariant,
    fontFamily: fontFamily,
  );

  static const TextStyle labelLarge = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    color: AppColors.onBackground,
    fontFamily: fontFamily,
    letterSpacing: 0.5,
  );

  static const TextStyle labelMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: AppColors.onSurface,
    fontFamily: fontFamily,
    letterSpacing: 0.3,
  );

  static const TextStyle labelSmall = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w500,
    color: AppColors.onSurfaceVariant,
    fontFamily: fontFamily,
    letterSpacing: 0.3,
  );

  static const TextStyle titleSmall = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    color: AppColors.onBackground,
    fontFamily: fontFamily,
  );
}

class AppDimensions {
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;

  static const double radiusSmall = 8.0;
  static const double radiusMedium = 16.0;
  static const double radiusLarge = 24.0;

  static const double buttonHeight = 56.0;
  static const double cardElevation = 8.0;
}

class AppDurations {
  static const Duration animationFast = Duration(milliseconds: 200);
  static const Duration animationMedium = Duration(milliseconds: 300);
  static const Duration animationSlow = Duration(milliseconds: 500);

  static const Duration emergencyExitCooldown = Duration(seconds: 15);
  static const int emergencyExitTapCount = 10;
}

class AppStrings {
  static const String appName = '锁手机';
  static const String appSubtitle = 'Focus Prison';

  // Main screen
  static const String startFocus = '🔒 开始锁手机';
  static const String todayFocus = '今日锁机';
  static const String totalMinutes = '分钟';
  static const String consecutiveDays = '连续锁机天数';
  static const String myRecords = '我的记录';
  static const String achievements = '成就系统';

  // Task creation
  static const String createTask = '🔒 锁手机设置';
  static const String focusGoal = '🎯 锁机目标';
  static const String focusDuration = '⏰ 锁定时长';
  static const String startLocking = '🔒 开始锁定';

  // Focus screen
  static const String phoneLocked = '手机已锁定';
  static const String motivationText = '"专注当下，拒绝干扰"';
  static const String lockingInProgress = '锁定中';

  // Emergency exit
  static const String emergencyExit = '强制解锁申请';
  static const String confirmExit = '确定要强制解锁手机吗？';
  static const String cooldownText = '冷却倒计时';
  static const String continueSession = '继续锁定';
  static const String forceUnlock = '强制解锁';

  // Completion
  static const String unlockSuccess = '手机解锁成功！';
  static const String sessionComplete = '你已成功保持专注';
  static const String thisSession = '本次锁定时长';
  static const String todayCompleted = '今日完成';
  static const String againButton = '再来一次';
  static const String viewDetails = '查看详情';
}
