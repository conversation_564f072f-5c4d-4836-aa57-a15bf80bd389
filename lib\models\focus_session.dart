class FocusSession {
  final String id;
  final String taskType;
  final int durationMinutes;
  bool completed;
  final DateTime startTime;
  DateTime? endTime;
  final bool isEscaped;

  FocusSession({
    required this.id,
    required this.taskType,
    required this.durationMinutes,
    required this.completed,
    required this.startTime,
    this.endTime,
    this.isEscaped = false,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'task_type': taskType,
      'duration_minutes': durationMinutes,
      'completed': completed ? 1 : 0,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime?.toIso8601String(),
      'is_escaped': isEscaped ? 1 : 0,
    };
  }

  factory FocusSession.fromMap(Map<String, dynamic> map) {
    return FocusSession(
      id: map['id']?.toString() ?? '',
      taskType: map['task_type'] ?? '',
      durationMinutes: map['duration_minutes'] ?? 0,
      completed: map['completed'] == 1 || map['completed'] == true,
      startTime: DateTime.parse(map['start_time']),
      endTime: map['end_time'] != null ? DateTime.parse(map['end_time']) : null,
      isEscaped: map['is_escaped'] == 1 || map['is_escaped'] == true,
    );
  }

  FocusSession copyWith({
    String? id,
    String? taskType,
    int? durationMinutes,
    bool? completed,
    DateTime? startTime,
    DateTime? endTime,
    bool? isEscaped,
  }) {
    return FocusSession(
      id: id ?? this.id,
      taskType: taskType ?? this.taskType,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      completed: completed ?? this.completed,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      isEscaped: isEscaped ?? this.isEscaped,
    );
  }
}

enum TaskType {
  deepWork('深度工作', '💼'),
  study('学习充电', '📚'),
  reading('阅读时光', '📖'),
  writing('写作创作', '✍️'),
  coding('编程开发', '💻'),
  meditation('冥想修行', '🧘'),
  exercise('运动健身', '🏃'),
  creative('创意思考', '💡');

  const TaskType(this.displayName, this.icon);

  final String displayName;
  final String icon;
}
