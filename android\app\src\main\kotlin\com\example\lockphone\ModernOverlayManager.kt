package com.example.lockphone

import android.app.Activity
import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.provider.Settings
import android.util.Log
import android.view.*
import android.widget.FrameLayout

/**
 * 现代化覆盖窗口管理器 - 2025年最佳实践
 * 使用TYPE_APPLICATION_OVERLAY实现不可逃脱的Kiosk模式
 */
class ModernOverlayManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "ModernOverlayManager"
        
        @Volatile
        private var instance: ModernOverlayManager? = null
        
        fun getInstance(context: Context): ModernOverlayManager {
            return instance ?: synchronized(this) {
                instance ?: ModernOverlayManager(context.applicationContext).also { instance = it }
            }
        }
    }
    
    private val windowManager: WindowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    private var overlayView: View? = null
    private var isOverlayActive = false
    
    // 屏幕尺寸信息
    private val displayMetrics = context.resources.displayMetrics
    private val screenWidth = displayMetrics.widthPixels
    private val screenHeight = displayMetrics.heightPixels
    
    /**
     * 检查悬浮窗权限
     */
    fun hasOverlayPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(context)
        } else {
            true
        }
    }
    
    /**
     * 启用现代化覆盖层
     */
    fun enableModernOverlay(): Boolean {
        try {
            Log.d(TAG, "🔒 ModernOverlayManager: 开始启用现代化覆盖层")
            
            if (!hasOverlayPermission()) {
                Log.w(TAG, "❌ ModernOverlayManager: 缺少悬浮窗权限")
                return false
            }
            
            if (isOverlayActive) {
                Log.d(TAG, "⚠️ ModernOverlayManager: 覆盖层已激活")
                return true
            }
            
            // 创建覆盖视图
            createOverlayView()
            
            // 添加到窗口管理器
            windowManager.addView(overlayView, createOverlayParams())
            
            isOverlayActive = true
            Log.d(TAG, "✅ ModernOverlayManager: 现代化覆盖层已启用")
            return true
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ ModernOverlayManager: 启用覆盖层失败: ${e.message}")
            return false
        }
    }
    
    /**
     * 创建覆盖视图
     */
    private fun createOverlayView() {
        overlayView = object : FrameLayout(context) {
            override fun onTouchEvent(event: MotionEvent): Boolean {
                return handleTouchEvent(event)
            }
            
            override fun dispatchTouchEvent(event: MotionEvent): Boolean {
                return handleTouchEvent(event)
            }
        }.apply {
            // 设置为透明背景
            setBackgroundColor(0x01000000) // 几乎透明，但仍能接收触摸事件
            
            // 确保视图可以接收触摸事件
            isClickable = true
            isFocusable = false
        }
    }
    
    /**
     * 创建覆盖窗口参数
     */
    private fun createOverlayParams(): WindowManager.LayoutParams {
        val type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else {
            @Suppress("DEPRECATION")
            WindowManager.LayoutParams.TYPE_SYSTEM_ALERT
        }
        
        return WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            type,
            // 修复：移除FLAG_NOT_FOCUSABLE以允许拦截系统手势
            WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
            WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH or
            WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS or
            WindowManager.LayoutParams.FLAG_FULLSCREEN or
            WindowManager.LayoutParams.FLAG_LAYOUT_INSET_DECOR,
            PixelFormat.TRANSLUCENT
        ).apply {
            // 确保覆盖整个屏幕，包括系统手势区域
            x = 0
            y = 0
            width = screenWidth
            height = screenHeight

            // 添加系统手势拦截配置
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                // Android 9+ 支持手势导航拦截
                layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
            }

            // 设置为系统级窗口优先级
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // 确保覆盖层在最顶层
                flags = flags or WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
            }

            Log.d(TAG, "🔒 ModernOverlayManager: 增强覆盖窗口参数 - 宽度:$width, 高度:$height")
        }
    }
    
    /**
     * 处理触摸事件
     */
    private fun handleTouchEvent(event: MotionEvent): Boolean {
        val x = event.x
        val y = event.y
        val action = event.action
        
        // 检查是否为系统手势区域
        if (isSystemGestureArea(x, y)) {
            when (action) {
                MotionEvent.ACTION_DOWN -> {
                    Log.w(TAG, "🚫 ModernOverlayManager: 系统手势区域触摸开始 - ($x, $y)")
                }
                MotionEvent.ACTION_MOVE -> {
                    Log.w(TAG, "🚫 ModernOverlayManager: 系统手势移动被拦截 - ($x, $y)")
                }
                MotionEvent.ACTION_UP -> {
                    Log.w(TAG, "🚫 ModernOverlayManager: 系统手势被完全拦截 - ($x, $y)")
                }
            }
            return true // 消费事件，阻止传递给下层
        }
        
        // 允许应用内正常触摸
        return false
    }
    
    /**
     * 判断是否为系统手势区域
     */
    private fun isSystemGestureArea(x: Float, y: Float): Boolean {
        // 底部导航手势区域（底部8%）
        val bottomGestureThreshold = screenHeight * 0.92f
        
        // 侧边手势区域（左右各3%）
        val leftGestureThreshold = screenWidth * 0.03f
        val rightGestureThreshold = screenWidth * 0.97f
        
        // 顶部状态栏下拉区域（顶部5%）
        val topGestureThreshold = screenHeight * 0.05f
        
        val isBottomGesture = y > bottomGestureThreshold
        val isLeftGesture = x < leftGestureThreshold
        val isRightGesture = x > rightGestureThreshold
        val isTopGesture = y < topGestureThreshold
        
        if (isBottomGesture || isLeftGesture || isRightGesture || isTopGesture) {
            Log.d(TAG, "🎯 ModernOverlayManager: 检测到系统手势区域 - 位置:($x,$y), 底部:$isBottomGesture, 左侧:$isLeftGesture, 右侧:$isRightGesture, 顶部:$isTopGesture")
            return true
        }
        
        return false
    }
    
    /**
     * 禁用覆盖层
     */
    fun disableOverlay(): Boolean {
        try {
            Log.d(TAG, "🔒 ModernOverlayManager: 开始禁用覆盖层")
            
            if (!isOverlayActive || overlayView == null) {
                Log.d(TAG, "⚠️ ModernOverlayManager: 覆盖层未激活")
                return true
            }
            
            windowManager.removeView(overlayView)
            overlayView = null
            isOverlayActive = false
            
            Log.d(TAG, "✅ ModernOverlayManager: 覆盖层已禁用")
            return true
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ ModernOverlayManager: 禁用覆盖层失败: ${e.message}")
            return false
        }
    }
    
    /**
     * 检查覆盖层状态
     */
    fun isActive(): Boolean = isOverlayActive
    
    /**
     * 更新覆盖层配置
     */
    fun updateOverlayConfiguration() {
        if (isOverlayActive && overlayView != null) {
            try {
                Log.d(TAG, "🔒 ModernOverlayManager: 更新覆盖层配置")
                windowManager.updateViewLayout(overlayView, createOverlayParams())
                Log.d(TAG, "✅ ModernOverlayManager: 覆盖层配置已更新")
            } catch (e: Exception) {
                Log.e(TAG, "❌ ModernOverlayManager: 更新覆盖层配置失败: ${e.message}")
            }
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            disableOverlay()
            instance = null
            Log.d(TAG, "🔒 ModernOverlayManager: 资源已清理")
        } catch (e: Exception) {
            Log.e(TAG, "❌ ModernOverlayManager: 清理资源失败: ${e.message}")
        }
    }
}
