import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'permission_manager.dart';

/// 权限状态缓存服务
/// 用于本地存储权限授权状态，避免重复授权提示
class PermissionCacheService {
  static PermissionCacheService? _instance;
  static PermissionCacheService get instance =>
      _instance ??= PermissionCacheService._();

  PermissionCacheService._();

  static const String _keyPrefix = 'permission_cache_';
  static const String _keyLastCheck = 'permission_last_check_';
  static const String _keyUserConsent = 'permission_user_consent_';
  static const String _keyAutoEnableAttempted =
      'permission_auto_enable_attempted_';

  // 缓存有效期（毫秒）- 5分钟
  static const int _cacheValidityDuration = 5 * 60 * 1000;

  SharedPreferences? _prefs;

  // 方法通道，用于直接调用原生权限检查
  static const MethodChannel _channel = MethodChannel('yoyo_lock_screen');

  /// 初始化服务
  Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// 直接检查权限状态（不使用缓存）
  Future<PermissionStatus> _checkPermissionDirect(PermissionType type) async {
    try {
      final result = await _channel.invokeMethod('checkPermission', {
        'type': type.name,
      });

      switch (result) {
        case 'granted':
          return PermissionStatus.granted;
        case 'denied':
          return PermissionStatus.denied;
        default:
          return PermissionStatus.unknown;
      }
    } catch (e) {
      debugPrint('直接检查权限失败: $e');
      return PermissionStatus.unknown;
    }
  }

  /// 获取权限状态缓存
  Future<PermissionStatus?> getCachedPermissionStatus(
      PermissionType type) async {
    await init();

    final cacheKey = '$_keyPrefix${type.name}';
    final lastCheckKey = '$_keyLastCheck${type.name}';

    final cachedStatus = _prefs!.getString(cacheKey);
    final lastCheckTime = _prefs!.getInt(lastCheckKey) ?? 0;

    // 检查缓存是否有效
    final now = DateTime.now().millisecondsSinceEpoch;
    final isValid = (now - lastCheckTime) < _cacheValidityDuration;

    if (cachedStatus != null && isValid) {
      debugPrint('使用缓存的权限状态: ${type.name} = $cachedStatus');
      return _parsePermissionStatus(cachedStatus);
    }

    return null;
  }

  /// 缓存权限状态
  Future<void> cachePermissionStatus(
      PermissionType type, PermissionStatus status) async {
    await init();

    final cacheKey = '$_keyPrefix${type.name}';
    final lastCheckKey = '$_keyLastCheck${type.name}';
    final now = DateTime.now().millisecondsSinceEpoch;

    await _prefs!.setString(cacheKey, status.name);
    await _prefs!.setInt(lastCheckKey, now);

    debugPrint('缓存权限状态: ${type.name} = ${status.name}');
  }

  /// 检查用户是否已经同意过某个权限
  Future<bool> hasUserConsent(PermissionType type) async {
    await init();

    final consentKey = '$_keyUserConsent${type.name}';
    return _prefs!.getBool(consentKey) ?? false;
  }

  /// 记录用户同意权限
  Future<void> recordUserConsent(PermissionType type) async {
    await init();

    final consentKey = '$_keyUserConsent${type.name}';
    await _prefs!.setBool(consentKey, true);

    debugPrint('记录用户同意权限: ${type.name}');
  }

  /// 检查是否已经尝试过自动启用
  Future<bool> hasAutoEnableAttempted(PermissionType type) async {
    await init();

    final attemptKey = '$_keyAutoEnableAttempted${type.name}';
    return _prefs!.getBool(attemptKey) ?? false;
  }

  /// 记录自动启用尝试
  Future<void> recordAutoEnableAttempt(PermissionType type) async {
    await init();

    final attemptKey = '$_keyAutoEnableAttempted${type.name}';
    await _prefs!.setBool(attemptKey, true);

    debugPrint('记录自动启用尝试: ${type.name}');
  }

  /// 清除特定权限的缓存
  Future<void> clearPermissionCache(PermissionType type) async {
    await init();

    final cacheKey = '$_keyPrefix${type.name}';
    final lastCheckKey = '$_keyLastCheck${type.name}';

    await _prefs!.remove(cacheKey);
    await _prefs!.remove(lastCheckKey);

    debugPrint('清除权限缓存: ${type.name}');
  }

  /// 清除所有权限缓存
  Future<void> clearAllPermissionCache() async {
    await init();

    final keys = _prefs!.getKeys();
    final permissionKeys = keys
        .where((key) =>
            key.startsWith(_keyPrefix) || key.startsWith(_keyLastCheck))
        .toList();

    for (final key in permissionKeys) {
      await _prefs!.remove(key);
    }

    debugPrint('清除所有权限缓存');
  }

  /// 强制刷新权限状态
  /// 清除缓存并重新检查
  Future<PermissionStatus> forceRefreshPermissionStatus(
      PermissionType type) async {
    await clearPermissionCache(type);

    // 直接调用系统检查，不使用缓存
    final status = await _checkPermissionDirect(type);
    await cachePermissionStatus(type, status);

    return status;
  }

  /// 检查权限状态（优先使用缓存）
  Future<PermissionStatus> getPermissionStatus(PermissionType type) async {
    // 首先尝试从缓存获取
    final cachedStatus = await getCachedPermissionStatus(type);
    if (cachedStatus != null) {
      return cachedStatus;
    }

    // 缓存无效或不存在，重新检查
    final status = await _checkPermissionDirect(type);
    await cachePermissionStatus(type, status);

    return status;
  }

  /// 智能权限检查
  /// 根据缓存状态和用户历史决定是否需要重新授权
  Future<PermissionCheckResult> smartPermissionCheck(
      PermissionType type) async {
    final result = PermissionCheckResult();

    // 检查当前权限状态
    final currentStatus = await getPermissionStatus(type);
    result.currentStatus = currentStatus;

    // 如果权限已授予，直接返回
    if (currentStatus == PermissionStatus.granted) {
      result.needsRequest = false;
      result.canAutoEnable = false;
      result.shouldShowRationale = false;
      return result;
    }

    // 检查用户是否已经同意过
    final hasConsent = await hasUserConsent(type);
    result.hasUserConsent = hasConsent;

    // 检查是否已经尝试过自动启用
    final hasAttempted = await hasAutoEnableAttempted(type);
    result.hasAutoEnableAttempted = hasAttempted;

    // 决定处理策略
    if (type == PermissionType.accessibility) {
      // 无障碍服务特殊处理
      if (hasConsent && !hasAttempted) {
        // 用户已同意但未尝试自动启用
        result.needsRequest = false;
        result.canAutoEnable = true;
        result.shouldShowRationale = false;
      } else if (hasConsent && hasAttempted) {
        // 用户已同意且已尝试自动启用，但仍未授权
        result.needsRequest = true;
        result.canAutoEnable = false;
        result.shouldShowRationale = false; // 直接跳转设置
      } else {
        // 用户未同意，需要显示说明
        result.needsRequest = true;
        result.canAutoEnable = false;
        result.shouldShowRationale = true;
      }
    } else {
      // 其他权限的标准处理
      result.needsRequest = true;
      result.canAutoEnable = false;
      result.shouldShowRationale = !hasConsent;
    }

    return result;
  }

  /// 解析权限状态字符串
  PermissionStatus _parsePermissionStatus(String status) {
    switch (status) {
      case 'granted':
        return PermissionStatus.granted;
      case 'denied':
        return PermissionStatus.denied;
      default:
        return PermissionStatus.unknown;
    }
  }

  /// 重置权限状态（用于测试或重新配置）
  Future<void> resetPermissionState(PermissionType type) async {
    await init();

    final keys = [
      '$_keyPrefix${type.name}',
      '$_keyLastCheck${type.name}',
      '$_keyUserConsent${type.name}',
      '$_keyAutoEnableAttempted${type.name}',
    ];

    for (final key in keys) {
      await _prefs!.remove(key);
    }

    debugPrint('重置权限状态: ${type.name}');
  }
}

/// 权限检查结果
class PermissionCheckResult {
  PermissionStatus currentStatus = PermissionStatus.unknown;
  bool needsRequest = false;
  bool canAutoEnable = false;
  bool shouldShowRationale = false;
  bool hasUserConsent = false;
  bool hasAutoEnableAttempted = false;

  @override
  String toString() {
    return 'PermissionCheckResult('
        'currentStatus: $currentStatus, '
        'needsRequest: $needsRequest, '
        'canAutoEnable: $canAutoEnable, '
        'shouldShowRationale: $shouldShowRationale, '
        'hasUserConsent: $hasUserConsent, '
        'hasAutoEnableAttempted: $hasAutoEnableAttempted'
        ')';
  }
}
