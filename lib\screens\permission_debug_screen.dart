import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/permission_manager.dart';
import '../services/permission_cache_service.dart';
import '../services/permission_diagnostic_service.dart';

/// 权限调试页面
/// 用于诊断和修复权限相关问题
class PermissionDebugScreen extends StatefulWidget {
  const PermissionDebugScreen({super.key});

  @override
  State<PermissionDebugScreen> createState() => _PermissionDebugScreenState();
}

class _PermissionDebugScreenState extends State<PermissionDebugScreen> {
  bool _isLoading = false;
  String _diagnosticReport = '';
  PermissionDiagnosticResult? _lastResult;

  @override
  void initState() {
    super.initState();
    _runDiagnostic();
  }

  /// 安全显示SnackBar的辅助方法
  void _showSnackBar(String message, {Color? backgroundColor}) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor ?? Colors.blue,
        ),
      );
    }
  }

  /// 运行权限诊断
  Future<void> _runDiagnostic() async {
    setState(() {
      _isLoading = true;
      _diagnosticReport = '正在诊断权限状态...';
    });

    try {
      final result = await PermissionDiagnosticService.instance
          .diagnosePermission(PermissionType.accessibility);

      final report =
          PermissionDiagnosticService.instance.generateDiagnosticReport(result);

      setState(() {
        _lastResult = result;
        _diagnosticReport = report;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _diagnosticReport = '诊断失败: $e';
        _isLoading = false;
      });
    }
  }

  /// 强制刷新权限状态
  Future<void> _forceRefresh() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final status = await PermissionManager.instance
          .forceCheckPermission(PermissionType.accessibility);

      _showSnackBar(
        '强制刷新完成，当前状态: ${status.name}',
        backgroundColor:
            status == PermissionStatus.granted ? Colors.green : Colors.orange,
      );

      // 重新运行诊断
      await _runDiagnostic();
    } catch (e) {
      _showSnackBar('强制刷新失败: $e', backgroundColor: Colors.red);
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 修复权限状态不一致
  Future<void> _fixInconsistency() async {
    if (_lastResult == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final success = await PermissionDiagnosticService.instance
          .fixPermissionInconsistency(PermissionType.accessibility);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success ? '修复成功' : '修复失败'),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }

      if (success) {
        await _runDiagnostic();
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('修复失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 清除所有缓存
  Future<void> _clearCache() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await PermissionCacheService.instance.clearAllPermissionCache();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('缓存已清除'),
          backgroundColor: Colors.green,
        ),
      );

      await _runDiagnostic();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('清除缓存失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 重置权限状态
  Future<void> _resetPermissionState() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await PermissionCacheService.instance
          .resetPermissionState(PermissionType.accessibility);

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('权限状态已重置'),
          backgroundColor: Colors.green,
        ),
      );

      await _runDiagnostic();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('重置失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 复制诊断报告
  void _copyReport() {
    Clipboard.setData(ClipboardData(text: _diagnosticReport));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('诊断报告已复制到剪贴板'),
        backgroundColor: Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('权限调试'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _copyReport,
            icon: const Icon(Icons.copy),
            tooltip: '复制报告',
          ),
          IconButton(
            onPressed: _runDiagnostic,
            icon: const Icon(Icons.refresh),
            tooltip: '重新诊断',
          ),
        ],
      ),
      body: Column(
        children: [
          // 状态指示器
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: _getStatusColor(),
            child: Row(
              children: [
                Icon(
                  _getStatusIcon(),
                  color: Colors.white,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _getStatusText(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 操作按钮
          Container(
            padding: const EdgeInsets.all(16),
            child: Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _forceRefresh,
                  icon: const Icon(Icons.refresh),
                  label: const Text('强制刷新'),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading || _lastResult?.isConsistent != false
                      ? null
                      : _fixInconsistency,
                  icon: const Icon(Icons.build),
                  label: const Text('修复不一致'),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _clearCache,
                  icon: const Icon(Icons.clear_all),
                  label: const Text('清除缓存'),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _resetPermissionState,
                  icon: const Icon(Icons.restore),
                  label: const Text('重置状态'),
                ),
              ],
            ),
          ),

          const Divider(),

          // 诊断报告
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.bug_report, color: Colors.blue),
                      const SizedBox(width: 8),
                      const Text(
                        '诊断报告',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      if (_isLoading)
                        const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: SingleChildScrollView(
                        child: Text(
                          _diagnosticReport,
                          style: const TextStyle(
                            fontFamily: 'monospace',
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    if (_isLoading) return Colors.blue;
    if (_lastResult == null) return Colors.grey;

    if (_lastResult!.systemStatus == PermissionStatus.granted) {
      return _lastResult!.isConsistent ? Colors.green : Colors.orange;
    } else {
      return Colors.red;
    }
  }

  IconData _getStatusIcon() {
    if (_isLoading) return Icons.hourglass_empty;
    if (_lastResult == null) return Icons.help;

    if (_lastResult!.systemStatus == PermissionStatus.granted) {
      return _lastResult!.isConsistent ? Icons.check_circle : Icons.warning;
    } else {
      return Icons.error;
    }
  }

  String _getStatusText() {
    if (_isLoading) return '正在检查权限状态...';
    if (_lastResult == null) return '等待诊断结果';

    if (_lastResult!.systemStatus == PermissionStatus.granted) {
      if (_lastResult!.isConsistent) {
        return '权限正常 - 无障碍服务已启用且状态一致';
      } else {
        return '权限异常 - 无障碍服务已启用但缓存状态不一致';
      }
    } else {
      return '权限缺失 - 无障碍服务未启用';
    }
  }
}
