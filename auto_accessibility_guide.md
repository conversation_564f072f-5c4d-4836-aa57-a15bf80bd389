# 无障碍服务自动控制功能指南

## 🎯 功能概述

新的自动控制功能可以在用户同意后：
- ✅ **专注开始时自动启用无障碍服务**
- ✅ **专注结束后自动关闭无障碍服务**
- ✅ **避免用户手动管理的麻烦**
- ✅ **确保正常使用不受影响**

## 🔧 实现原理

### 1. 权限机制
- 使用`WRITE_SECURE_SETTINGS`权限控制系统设置
- 通过Android Settings API启用/禁用无障碍服务
- 需要通过ADB手动授予系统级权限

### 2. 自动控制流程
```
用户选择深度锁定 → 显示权限说明 → 用户同意 → 自动启用无障碍服务 → 开始专注
                                                                    ↓
专注结束/取消 ← 自动关闭无障碍服务 ← 禁用锁屏功能 ← 专注进行中
```

### 3. 用户体验优化
- **友好的权限说明** - 清楚解释为什么需要权限
- **自动化处理** - 减少用户操作步骤
- **智能降级** - 自动启用失败时引导手动启用
- **状态恢复** - 专注结束后自动恢复正常状态

## 📱 使用步骤

### 1. 首次设置（仅需一次）

**Windows用户：**
```bash
# 连接Android设备，启用USB调试
# 运行权限授予脚本
grant_permissions.bat
```

**Linux/Mac用户：**
```bash
# 连接Android设备，启用USB调试
chmod +x grant_permissions.sh
./grant_permissions.sh
```

**手动授予权限：**
```bash
adb shell pm grant com.example.lockphone android.permission.WRITE_SECURE_SETTINGS
adb shell pm grant com.example.lockphone android.permission.WRITE_SETTINGS
```

### 2. 使用深度锁定

1. **选择深度锁定级别**
2. **阅读权限说明对话框**
3. **点击"同意并启用"**
4. **应用自动启用无障碍服务**
5. **开始专注**
6. **专注结束后自动关闭服务**

## 🔍 关键代码实现

### Android端自动控制
```kotlin
private fun enableAccessibilityServiceAutomatically(): Boolean {
    val serviceName = "com.example.lockphone/.YoYoAccessibilityService"
    
    // 获取当前启用的服务列表
    val enabledServices = Settings.Secure.getString(
        contentResolver,
        Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
    ) ?: ""
    
    // 添加服务到启用列表
    val newEnabledServices = if (enabledServices.isEmpty()) {
        serviceName
    } else {
        "$enabledServices:$serviceName"
    }
    
    // 写入设置
    Settings.Secure.putString(
        contentResolver,
        Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES,
        newEnabledServices
    )
    
    // 启用无障碍功能
    Settings.Secure.putString(
        contentResolver,
        Settings.Secure.ACCESSIBILITY_ENABLED,
        "1"
    )
    
    return isAccessibilityServiceEnabled()
}
```

### Flutter端集成
```dart
// 专注开始时自动启用
if (_lockLevel == LockLevel.deep) {
  final autoEnabled = await PermissionManager.instance
      .enableAccessibilityServiceAuto();
  if (autoEnabled) {
    canLock = await PermissionManager.instance.hasDeepLockPermissions();
  }
}

// 专注结束时自动禁用
if (_lockLevel == LockLevel.deep) {
  final autoDisabled = await PermissionManager.instance
      .disableAccessibilityServiceAuto();
}
```

## 🛡️ 安全性说明

### 权限使用范围
- **仅控制本应用的无障碍服务**
- **不访问其他应用的设置**
- **不修改系统其他配置**
- **专注结束后立即禁用**

### 隐私保护
- 无障碍服务仅在专注期间激活
- 不收集用户操作数据
- 不访问其他应用内容
- 完全本地化处理

## 🔧 故障排除

### 1. 自动启用失败
**原因：** 缺少WRITE_SECURE_SETTINGS权限
**解决：** 运行权限授予脚本

### 2. 权限脚本执行失败
**原因：** ADB未正确配置或设备未连接
**解决：** 
- 确保USB调试已启用
- 检查ADB驱动是否正确安装
- 重新连接设备

### 3. 无障碍服务未自动关闭
**原因：** 应用异常退出或系统限制
**解决：** 
- 手动关闭无障碍服务
- 重启应用重新测试

### 4. 设备兼容性问题
**某些设备可能有限制：**
- MIUI、EMUI等定制系统可能有额外限制
- 部分设备需要在开发者选项中允许
- 可能需要关闭电池优化

## 📊 预期效果

### 用户体验改进
- 🚀 **操作步骤减少70%** - 从手动5步变为自动1步
- ⚡ **启动速度提升** - 无需手动跳转设置页面
- 🛡️ **使用安全** - 专注结束后自动恢复正常状态
- 💡 **智能提示** - 清晰的权限说明和操作指导

### 技术优势
- 🔧 **自动化程度高** - 最大化减少用户操作
- 🎯 **精确控制** - 仅控制必要的服务
- 🔄 **状态同步** - 确保服务状态与专注状态一致
- 📱 **设备适配** - 兼容主流Android设备

这个优化大大提升了深度锁定功能的易用性，让用户可以专注于专注本身，而不是权限管理！
