import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'screens/home_screen.dart';
import 'pages/permission_test_page.dart';
import 'services/database_service.dart';
import 'services/focus_manager.dart' as yoyo;
import 'services/app_lifecycle_manager.dart';
import 'services/permission_cache_service.dart';
import 'services/permission_state_monitor.dart';
import 'providers/statistics_provider.dart';
import 'providers/achievement_provider.dart';
import 'utils/constants.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize database
  await DatabaseService.instance.init();

  // Initialize permission cache service
  await PermissionCacheService.instance.init();

  // Initialize app lifecycle manager
  AppLifecycleManager.instance.initialize();

  // Start permission state monitoring
  PermissionStateMonitor.instance.startMonitoring();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  runApp(const FocusPrisonApp());
}

class FocusPrisonApp extends StatelessWidget {
  const FocusPrisonApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => yoyo.FocusManager.instance),
        ChangeNotifierProvider(create: (_) => StatisticsProvider()),
        ChangeNotifierProvider(create: (_) => AchievementProvider()),
      ],
      child: MaterialApp(
        title: AppStrings.appName,
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          useMaterial3: true,
          brightness: Brightness.dark,
          scaffoldBackgroundColor: AppColors.background,
          colorScheme: const ColorScheme.dark(
            primary: AppColors.primary,
            secondary: AppColors.secondary,
            surface: AppColors.surface,
            onPrimary: Colors.black,
            onSecondary: Colors.black,
            onSurface: AppColors.onSurface,
          ),
          textTheme: const TextTheme(
            displayLarge: AppTextStyles.timerLarge,
            headlineLarge: AppTextStyles.titleLarge,
            headlineMedium: AppTextStyles.titleMedium,
            bodyLarge: AppTextStyles.bodyLarge,
            bodyMedium: AppTextStyles.bodyMedium,
            bodySmall: AppTextStyles.bodySmall,
            labelLarge: AppTextStyles.labelLarge,
          ),
          elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.black,
              minimumSize:
                  const Size(double.infinity, AppDimensions.buttonHeight),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
              ),
            ),
          ),
          inputDecorationTheme: InputDecorationTheme(
            filled: true,
            fillColor: AppColors.surface,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
              borderSide: BorderSide.none,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
              borderSide: BorderSide(
                color: AppColors.onSurface.withOpacity(0.2),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
              borderSide: const BorderSide(color: AppColors.primary),
            ),
          ),
        ),
        home: const HomeScreen(),
        routes: {
          '/permission_test': (context) => const PermissionTestPage(),
        },
      ),
    );
  }
}
