import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:lockphone/services/optimized_permission_flow.dart';
import 'package:lockphone/services/permission_cache_service.dart';
import 'package:lockphone/services/permission_manager.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('优化权限流程测试', () {
    late OptimizedPermissionFlow permissionFlow;

    setUp(() async {
      // 设置测试环境
      SharedPreferences.setMockInitialValues({});
      permissionFlow = OptimizedPermissionFlow.instance;

      await PermissionCacheService.instance.init();

      // 模拟方法通道
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        const MethodChannel('yoyo_lock_screen'),
        (MethodCall methodCall) async {
          switch (methodCall.method) {
            case 'checkPermission':
              return 'denied'; // 默认返回拒绝状态
            case 'testAccessibilityService':
              return {
                'accessibilityManager': false,
                'settings': false,
                'instance': false,
                'serviceReady': false,
                'enabledServices': <String>[],
                'targetServiceFound': false,
              };
            case 'enableAccessibilityServiceAuto':
              return false; // 默认自动启用失败
            default:
              return null;
          }
        },
      );
    });

    tearDown(() async {
      await PermissionCacheService.instance.clearAllPermissionCache();
    });

    testWidgets('权限已授予的情况', (WidgetTester tester) async {
      // 模拟权限已授予
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        const MethodChannel('com.example.lockphone/permissions'),
        (MethodCall methodCall) async {
          if (methodCall.method == 'checkPermission') {
            return 'granted';
          }
          return null;
        },
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              return Scaffold(
                body: ElevatedButton(
                  onPressed: () async {
                    final result = await permissionFlow.handlePermissionFlow(
                      context,
                      PermissionType.accessibility,
                      showDiagnosticOnFailure: false,
                    );

                    expect(result.success, true);
                    expect(result.finalStatus, PermissionStatus.granted);
                    expect(result.message, '权限已授予');
                  },
                  child: const Text('测试'),
                ),
              );
            },
          ),
        ),
      );

      await tester.tap(find.byType(ElevatedButton));
      await tester.pumpAndSettle();
    });

    testWidgets('权限未授予但可以自动启用', (WidgetTester tester) async {
      // 设置用户已同意但未尝试自动启用
      await PermissionCacheService.instance
          .recordUserConsent(PermissionType.accessibility);

      // 模拟自动启用成功
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        const MethodChannel('com.example.lockphone/permissions'),
        (MethodCall methodCall) async {
          switch (methodCall.method) {
            case 'checkPermission':
              return 'denied'; // 初始状态为拒绝
            case 'enableAccessibilityServiceAuto':
              return true; // 自动启用成功
            case 'testAccessibilityService':
              return {
                'accessibilityManager': false,
                'settings': false,
                'instance': false,
                'serviceReady': false,
                'enabledServices': <String>[],
                'targetServiceFound': false,
              };
            default:
              return null;
          }
        },
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              return Scaffold(
                body: ElevatedButton(
                  onPressed: () async {
                    final result = await permissionFlow.handlePermissionFlow(
                      context,
                      PermissionType.accessibility,
                      showDiagnosticOnFailure: false,
                    );

                    expect(result.autoEnableAttempted, true);
                    expect(result.autoEnableSuccess, true);
                  },
                  child: const Text('测试'),
                ),
              );
            },
          ),
        ),
      );

      await tester.tap(find.byType(ElevatedButton));
      await tester.pumpAndSettle();
    });

    test('快速权限检查', () async {
      // 测试权限已授予的情况
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        const MethodChannel('yoyo_lock_screen'),
        (MethodCall methodCall) async {
          if (methodCall.method == 'checkPermission') {
            return 'granted';
          }
          return null;
        },
      );

      final result = await permissionFlow
          .quickPermissionCheck(PermissionType.accessibility);
      expect(result, true);

      // 测试权限被拒绝的情况
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        const MethodChannel('yoyo_lock_screen'),
        (MethodCall methodCall) async {
          if (methodCall.method == 'checkPermission') {
            return 'denied';
          }
          return null;
        },
      );

      final result2 = await permissionFlow
          .quickPermissionCheck(PermissionType.accessibility);
      expect(result2, false);
    });

    test('批量权限检查', () async {
      // 模拟不同权限的不同状态
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        const MethodChannel('com.example.lockphone/permissions'),
        (MethodCall methodCall) async {
          if (methodCall.method == 'checkPermission') {
            final type = methodCall.arguments['type'];
            switch (type) {
              case 'accessibility':
                return 'granted';
              case 'overlay':
                return 'denied';
              default:
                return 'unknown';
            }
          }
          return null;
        },
      );

      final results = await permissionFlow.batchPermissionCheck([
        PermissionType.accessibility,
        PermissionType.deviceAdmin,
      ]);

      expect(results[PermissionType.accessibility], true);
      expect(results[PermissionType.deviceAdmin], false);
    });

    test('权限流程结果toString', () {
      final result = PermissionFlowResult();
      result.success = true;
      result.finalStatus = PermissionStatus.granted;
      result.message = '测试消息';
      result.autoEnableAttempted = true;
      result.autoEnableSuccess = false;
      result.userInteracted = true;
      result.userGranted = true;

      final str = result.toString();
      expect(str.contains('success: true'), true);
      expect(str.contains('finalStatus: granted'), true);
      expect(str.contains('message: 测试消息'), true);
      expect(str.contains('autoEnableAttempted: true'), true);
      expect(str.contains('autoEnableSuccess: false'), true);
      expect(str.contains('userInteracted: true'), true);
      expect(str.contains('userGranted: true'), true);
    });

    test('错误处理', () async {
      // 模拟方法通道错误
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        const MethodChannel('yoyo_lock_screen'),
        (MethodCall methodCall) async {
          throw PlatformException(code: 'ERROR', message: '模拟错误');
        },
      );

      final result = await permissionFlow
          .quickPermissionCheck(PermissionType.accessibility);
      expect(result, false);
    });
  });

  group('权限流程集成测试', () {
    testWidgets('完整的权限授权流程', (WidgetTester tester) async {
      await PermissionCacheService.instance.init();

      // 清除所有状态
      await PermissionCacheService.instance
          .resetPermissionState(PermissionType.accessibility);

      // 模拟权限检查和自动启用
      bool autoEnableCalled = false;
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        const MethodChannel('yoyo_lock_screen'),
        (MethodCall methodCall) async {
          switch (methodCall.method) {
            case 'checkPermission':
              return 'denied';
            case 'enableAccessibilityServiceAuto':
              autoEnableCalled = true;
              return false; // 自动启用失败
            case 'testAccessibilityService':
              return {
                'accessibilityManager': false,
                'settings': false,
                'instance': false,
                'serviceReady': false,
                'enabledServices': <String>[],
                'targetServiceFound': false,
              };
            default:
              return null;
          }
        },
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              return Scaffold(
                body: ElevatedButton(
                  onPressed: () async {
                    // 首先记录用户同意
                    await PermissionCacheService.instance
                        .recordUserConsent(PermissionType.accessibility);

                    final result = await OptimizedPermissionFlow.instance
                        .handlePermissionFlow(
                      context,
                      PermissionType.accessibility,
                      showDiagnosticOnFailure: false,
                    );

                    // 验证流程执行
                    expect(autoEnableCalled, true);
                    expect(result.autoEnableAttempted, true);
                    expect(result.autoEnableSuccess, false);
                  },
                  child: const Text('测试完整流程'),
                ),
              );
            },
          ),
        ),
      );

      await tester.tap(find.byType(ElevatedButton));
      await tester.pumpAndSettle();
    });
  });
}
