import 'package:flutter/material.dart';
import 'permission_cache_service.dart';
import 'permission_manager.dart';
import 'permission_state_monitor.dart';

/// 应用生命周期管理器
/// 负责在应用状态变化时智能管理权限状态
class AppLifecycleManager extends WidgetsBindingObserver {
  static AppLifecycleManager? _instance;
  static AppLifecycleManager get instance =>
      _instance ??= AppLifecycleManager._();

  AppLifecycleManager._();

  bool _isInitialized = false;
  AppLifecycleState? _lastState;
  DateTime? _lastPausedTime;

  // 应用暂停超过此时间后，恢复时会重新检查权限（毫秒）
  static const int _recheckThreshold = 30 * 60 * 1000; // 30分钟

  /// 初始化生命周期管理
  void initialize() {
    if (_isInitialized) return;

    WidgetsBinding.instance.addObserver(this);
    _isInitialized = true;
    debugPrint('应用生命周期管理器已初始化');
  }

  /// 销毁生命周期管理
  void dispose() {
    if (!_isInitialized) return;

    WidgetsBinding.instance.removeObserver(this);
    _isInitialized = false;
    debugPrint('应用生命周期管理器已销毁');
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    debugPrint('应用生命周期状态变化: ${_lastState?.name} -> ${state.name}');

    switch (state) {
      case AppLifecycleState.resumed:
        _handleAppResumed();
        break;
      case AppLifecycleState.paused:
        _handleAppPaused();
        break;
      case AppLifecycleState.inactive:
        _handleAppInactive();
        break;
      case AppLifecycleState.detached:
        _handleAppDetached();
        break;
      case AppLifecycleState.hidden:
        _handleAppHidden();
        break;
    }

    _lastState = state;
  }

  /// 处理应用恢复
  Future<void> _handleAppResumed() async {
    debugPrint('应用已恢复');

    // 启动权限状态监听
    PermissionStateMonitor.instance.startMonitoring();

    // 检查是否需要重新验证权限
    if (_shouldRecheckPermissions()) {
      await _recheckCriticalPermissions();
    }
  }

  /// 处理应用暂停
  void _handleAppPaused() {
    debugPrint('应用已暂停');
    _lastPausedTime = DateTime.now();

    // 停止权限状态监听以节省资源
    PermissionStateMonitor.instance.stopMonitoring();
  }

  /// 处理应用非活跃状态
  void _handleAppInactive() {
    debugPrint('应用进入非活跃状态');
  }

  /// 处理应用分离
  void _handleAppDetached() {
    debugPrint('应用已分离');
  }

  /// 处理应用隐藏
  void _handleAppHidden() {
    debugPrint('应用已隐藏');
  }

  /// 判断是否需要重新检查权限
  bool _shouldRecheckPermissions() {
    // 如果是首次恢复，不需要重新检查
    if (_lastState == null || _lastPausedTime == null) {
      return false;
    }

    // 如果暂停时间超过阈值，需要重新检查
    final pauseDuration = DateTime.now().millisecondsSinceEpoch -
        _lastPausedTime!.millisecondsSinceEpoch;
    if (pauseDuration > _recheckThreshold) {
      debugPrint('应用暂停时间过长（${pauseDuration}ms），需要重新检查权限');
      return true;
    }

    // 如果从后台恢复，进行轻量级检查
    if (_lastState == AppLifecycleState.paused) {
      debugPrint('从后台恢复，进行轻量级权限检查');
      return true;
    }

    return false;
  }

  /// 重新检查关键权限
  Future<void> _recheckCriticalPermissions() async {
    try {
      debugPrint('开始重新检查关键权限');

      // 检查无障碍服务权限（最重要的权限）
      await _recheckAccessibilityPermission();

      debugPrint('关键权限检查完成');
    } catch (e) {
      debugPrint('重新检查权限时发生错误: $e');
    }
  }

  /// 重新检查无障碍服务权限
  Future<void> _recheckAccessibilityPermission() async {
    try {
      // 获取当前缓存状态
      final cachedStatus = await PermissionCacheService.instance
          .getCachedPermissionStatus(PermissionType.accessibility);

      // 获取实际系统状态
      final actualStatus = await PermissionManager.instance
          .checkPermission(PermissionType.accessibility);

      debugPrint('无障碍权限 - 缓存: ${cachedStatus?.name}, 实际: ${actualStatus.name}');

      // 如果状态不一致，更新缓存
      if (cachedStatus != actualStatus) {
        debugPrint('无障碍权限状态不一致，更新缓存');
        await PermissionCacheService.instance
            .cachePermissionStatus(PermissionType.accessibility, actualStatus);

        // 如果权限被撤销，清除相关状态
        if (cachedStatus == PermissionStatus.granted &&
            actualStatus != PermissionStatus.granted) {
          debugPrint('无障碍权限被撤销，清除自动启用尝试记录');
          await _handlePermissionRevoked(PermissionType.accessibility);
        }
      }
    } catch (e) {
      debugPrint('检查无障碍权限时发生错误: $e');
    }
  }

  /// 处理权限被撤销的情况
  Future<void> _handlePermissionRevoked(PermissionType type) async {
    try {
      // 重置权限状态，清除所有相关记录
      await PermissionCacheService.instance.resetPermissionState(type);
      debugPrint('权限被撤销，已重置权限状态: ${type.name}');
    } catch (e) {
      debugPrint('处理权限撤销时发生错误: $e');
    }
  }

  /// 手动触发权限检查
  /// 用于特殊场景下的主动检查
  Future<void> manualRecheckPermissions() async {
    debugPrint('手动触发权限检查');
    await _recheckCriticalPermissions();
  }

  /// 获取应用暂停时长（毫秒）
  int? getPauseDuration() {
    if (_lastPausedTime == null) return null;
    return DateTime.now().millisecondsSinceEpoch -
        _lastPausedTime!.millisecondsSinceEpoch;
  }

  /// 检查应用是否长时间暂停
  bool isLongPause() {
    final duration = getPauseDuration();
    return duration != null && duration > _recheckThreshold;
  }

  /// 重置暂停时间
  void resetPauseTime() {
    _lastPausedTime = null;
    debugPrint('暂停时间已重置');
  }

  /// 获取当前应用状态
  AppLifecycleState? get currentState => _lastState;

  /// 获取最后暂停时间
  DateTime? get lastPausedTime => _lastPausedTime;

  /// 是否已初始化
  bool get isInitialized => _isInitialized;
}
