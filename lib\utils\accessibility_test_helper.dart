import 'package:flutter/foundation.dart';
import '../services/permission_manager.dart';
import '../services/accessibility_fallback_service.dart';

/// 无障碍服务测试辅助工具
class AccessibilityTestHelper {
  static const String _tag = 'AccessibilityTestHelper';

  /// 执行完整的功能测试
  static Future<TestResult> runFullTest() async {
    final result = TestResult();
    
    try {
      debugPrint('$_tag: 开始执行无障碍服务功能测试');
      
      // 1. 测试权限检查功能
      result.permissionCheckTest = await _testPermissionCheck();
      
      // 2. 测试设备信息收集
      result.deviceInfoTest = await _testDeviceInfo();
      
      // 3. 测试系统权限检查
      result.systemPermissionTest = await _testSystemPermissions();
      
      // 4. 测试自动启用功能
      result.autoEnableTest = await _testAutoEnable();
      
      // 5. 测试回退机制
      result.fallbackTest = await _testFallbackMechanism();
      
      // 6. 计算总体结果
      result.calculateOverallResult();
      
      debugPrint('$_tag: 测试完成，总体结果: ${result.overallSuccess ? "成功" : "失败"}');
      
    } catch (e) {
      debugPrint('$_tag: 测试过程中发生错误: $e');
      result.error = e.toString();
    }
    
    return result;
  }

  /// 测试权限检查功能
  static Future<bool> _testPermissionCheck() async {
    try {
      debugPrint('$_tag: 测试权限检查功能');
      
      final status = await PermissionManager.instance.checkPermission(PermissionType.accessibility);
      debugPrint('$_tag: 当前无障碍权限状态: $status');
      
      // 权限检查功能正常（无论结果如何）
      return true;
    } catch (e) {
      debugPrint('$_tag: 权限检查测试失败: $e');
      return false;
    }
  }

  /// 测试设备信息收集
  static Future<bool> _testDeviceInfo() async {
    try {
      debugPrint('$_tag: 测试设备信息收集');
      
      // 通过引导结果获取设备信息
      final guidanceResult = await PermissionManager.instance.requestAccessibilityServiceWithGuidance();
      
      if (guidanceResult.deviceInfo != null) {
        final deviceInfo = guidanceResult.deviceInfo!;
        debugPrint('$_tag: 设备信息收集成功');
        debugPrint('$_tag: 厂商: ${deviceInfo['manufacturer']}');
        debugPrint('$_tag: 品牌: ${deviceInfo['brand']}');
        debugPrint('$_tag: 型号: ${deviceInfo['model']}');
        debugPrint('$_tag: Android版本: ${deviceInfo['androidVersion']}');
        return true;
      } else {
        debugPrint('$_tag: 设备信息收集失败');
        return false;
      }
    } catch (e) {
      debugPrint('$_tag: 设备信息测试失败: $e');
      return false;
    }
  }

  /// 测试系统权限检查
  static Future<bool> _testSystemPermissions() async {
    try {
      debugPrint('$_tag: 测试系统权限检查');
      
      final systemPermissions = await PermissionManager.instance.checkSystemPermissions();
      
      debugPrint('$_tag: 系统权限检查结果:');
      systemPermissions.forEach((key, value) {
        debugPrint('$_tag: $key: $value');
      });
      
      // 系统权限检查功能正常
      return true;
    } catch (e) {
      debugPrint('$_tag: 系统权限测试失败: $e');
      return false;
    }
  }

  /// 测试自动启用功能
  static Future<bool> _testAutoEnable() async {
    try {
      debugPrint('$_tag: 测试自动启用功能');
      
      // 检查当前状态
      final currentStatus = await PermissionManager.instance.checkPermission(PermissionType.accessibility);
      
      if (currentStatus == PermissionStatus.granted) {
        debugPrint('$_tag: 无障碍服务已启用，跳过自动启用测试');
        return true;
      }
      
      // 尝试自动启用
      final autoEnableResult = await PermissionManager.instance.enableAccessibilityServiceAuto();
      debugPrint('$_tag: 自动启用结果: $autoEnableResult');
      
      // 检查启用后的状态
      final newStatus = await PermissionManager.instance.checkPermission(PermissionType.accessibility);
      debugPrint('$_tag: 启用后状态: $newStatus');
      
      return autoEnableResult && newStatus == PermissionStatus.granted;
    } catch (e) {
      debugPrint('$_tag: 自动启用测试失败: $e');
      return false;
    }
  }

  /// 测试回退机制
  static Future<bool> _testFallbackMechanism() async {
    try {
      debugPrint('$_tag: 测试回退机制');
      
      final fallbackResult = await AccessibilityFallbackService.instance.tryAllEnableMethods();
      
      debugPrint('$_tag: 回退机制测试结果:');
      debugPrint('$_tag: 成功: ${fallbackResult.success}');
      debugPrint('$_tag: 方法: ${fallbackResult.method}');
      debugPrint('$_tag: 消息: ${fallbackResult.message}');
      debugPrint('$_tag: 回退选项数量: ${fallbackResult.fallbackOptions.length}');
      
      // 回退机制能正常工作（提供选项或成功启用）
      return fallbackResult.success || fallbackResult.fallbackOptions.isNotEmpty;
    } catch (e) {
      debugPrint('$_tag: 回退机制测试失败: $e');
      return false;
    }
  }

  /// 生成测试报告
  static String generateTestReport(TestResult result) {
    final buffer = StringBuffer();
    
    buffer.writeln('📊 无障碍服务功能测试报告');
    buffer.writeln('=' * 40);
    buffer.writeln();
    
    buffer.writeln('🔍 测试结果概览:');
    buffer.writeln('总体结果: ${result.overallSuccess ? "✅ 通过" : "❌ 失败"}');
    buffer.writeln('成功率: ${result.successRate.toStringAsFixed(1)}%');
    buffer.writeln();
    
    buffer.writeln('📋 详细测试结果:');
    buffer.writeln('权限检查: ${result.permissionCheckTest ? "✅" : "❌"}');
    buffer.writeln('设备信息: ${result.deviceInfoTest ? "✅" : "❌"}');
    buffer.writeln('系统权限: ${result.systemPermissionTest ? "✅" : "❌"}');
    buffer.writeln('自动启用: ${result.autoEnableTest ? "✅" : "❌"}');
    buffer.writeln('回退机制: ${result.fallbackTest ? "✅" : "❌"}');
    buffer.writeln();
    
    if (result.error != null) {
      buffer.writeln('❌ 错误信息:');
      buffer.writeln(result.error);
      buffer.writeln();
    }
    
    buffer.writeln('📱 测试环境:');
    buffer.writeln('时间: ${DateTime.now()}');
    buffer.writeln('平台: ${defaultTargetPlatform.name}');
    buffer.writeln();
    
    buffer.writeln('💡 建议:');
    if (!result.overallSuccess) {
      buffer.writeln('- 检查失败的测试项目');
      buffer.writeln('- 确认设备权限配置');
      buffer.writeln('- 查看详细日志信息');
    } else {
      buffer.writeln('- 所有功能正常工作');
      buffer.writeln('- 可以正常使用无障碍服务功能');
    }
    
    return buffer.toString();
  }

  /// 快速健康检查
  static Future<bool> quickHealthCheck() async {
    try {
      // 检查核心服务是否可用
      final permissionStatus = await PermissionManager.instance.checkPermission(PermissionType.accessibility);
      final systemPermissions = await PermissionManager.instance.checkSystemPermissions();
      
      debugPrint('$_tag: 快速健康检查完成');
      debugPrint('$_tag: 权限状态: $permissionStatus');
      debugPrint('$_tag: 系统权限: ${systemPermissions['hasWriteSecureSettings']}');
      
      return true;
    } catch (e) {
      debugPrint('$_tag: 快速健康检查失败: $e');
      return false;
    }
  }
}

/// 测试结果类
class TestResult {
  bool permissionCheckTest = false;
  bool deviceInfoTest = false;
  bool systemPermissionTest = false;
  bool autoEnableTest = false;
  bool fallbackTest = false;
  
  bool overallSuccess = false;
  double successRate = 0.0;
  String? error;

  void calculateOverallResult() {
    final tests = [
      permissionCheckTest,
      deviceInfoTest,
      systemPermissionTest,
      autoEnableTest,
      fallbackTest,
    ];
    
    final successCount = tests.where((test) => test).length;
    successRate = (successCount / tests.length) * 100;
    overallSuccess = successRate >= 80.0; // 80%以上通过率认为成功
  }

  Map<String, dynamic> toJson() {
    return {
      'permissionCheckTest': permissionCheckTest,
      'deviceInfoTest': deviceInfoTest,
      'systemPermissionTest': systemPermissionTest,
      'autoEnableTest': autoEnableTest,
      'fallbackTest': fallbackTest,
      'overallSuccess': overallSuccess,
      'successRate': successRate,
      'error': error,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}
