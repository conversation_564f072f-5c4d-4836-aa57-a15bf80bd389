package com.example.lockphone

import android.accessibilityservice.AccessibilityServiceInfo
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import android.view.WindowManager
import android.view.accessibility.AccessibilityManager
import androidx.annotation.NonNull
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterActivity() {
    private val CHANNEL = "yoyo_lock_screen"

    companion object {
        private const val TAG = "YoYoLockScreen"
        private const val REQUEST_ACCESSIBILITY_PERMISSION = 1002
        private const val REQUEST_DEVICE_ADMIN_PERMISSION = 2001
    }

    private var lockScreenManager: LockScreenManager? = null
    private var pendingResult: MethodChannel.Result? = null

    // 现代化组件
    private lateinit var modernOverlayManager: ModernOverlayManager
    private lateinit var systemUIController: SystemUIController

    // 集成专注锁定管理器
    private lateinit var integratedFocusLockManager: IntegratedFocusLockManager

    // UX流程管理器
    private lateinit var uxFlowManager: UXFlowManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        Log.d(TAG, "🔒 MainActivity: 应用启动，初始化现代化锁屏组件")

        // 初始化现代化组件
        initializeModernComponents()

        // 启用基础手势防护
        enableBasicGestureProtection()
    }

    /**
     * 初始化现代化组件
     */
    private fun initializeModernComponents() {
        try {
            Log.d(TAG, "🔒 MainActivity: 初始化现代化组件")

            // 初始化集成专注锁定管理器
            integratedFocusLockManager = IntegratedFocusLockManager(this)
            Log.d(TAG, "✅ MainActivity: 集成专注锁定管理器初始化完成")

            // 初始化UX流程管理器
            uxFlowManager = UXFlowManager(this)
            Log.d(TAG, "✅ MainActivity: UX流程管理器初始化完成")

            // 初始化现代覆盖管理器
            modernOverlayManager = ModernOverlayManager.getInstance(this)

            // 初始化系统UI控制器
            systemUIController = SystemUIController(this)

            // 设置系统UI监听器
            systemUIController.setupSystemUIVisibilityListener()

            Log.d(TAG, "✅ MainActivity: 现代化组件初始化完成")
        } catch (e: Exception) {
            Log.e(TAG, "❌ MainActivity: 初始化现代化组件失败: ${e.message}")
        }
    }

    /**
     * 启用基础手势防护（无需权限）
     */
    private fun enableBasicGestureProtection() {
        try {
            Log.d(TAG, "🔒 MainActivity: 启用基础手势防护")

            // 设置窗口标志
            window.apply {
                // 隐藏系统UI
                decorView.systemUiVisibility = (
                    android.view.View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
                    android.view.View.SYSTEM_UI_FLAG_FULLSCREEN or
                    android.view.View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY or
                    android.view.View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or
                    android.view.View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
                    android.view.View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                )

                // 设置窗口标志
                addFlags(
                    WindowManager.LayoutParams.FLAG_FULLSCREEN or
                    WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
                )
            }

            Log.d(TAG, "✅ MainActivity: 基础手势防护已启用")
        } catch (e: Exception) {
            Log.e(TAG, "❌ MainActivity: 启用基础手势防护失败: ${e.message}")
        }
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)

        Log.d(TAG, "🔒 MainActivity: 窗口焦点变化 - hasFocus: $hasFocus")

        // 通知系统UI控制器
        if (::systemUIController.isInitialized) {
            systemUIController.onWindowFocusChanged(hasFocus)
        }
    }

    override fun configureFlutterEngine(@NonNull flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        lockScreenManager = LockScreenManager.initialize(this)

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)
            .setMethodCallHandler { call, result ->
                when (call.method) {
                    "checkPermission" -> {
                        val type = call.argument<String>("type")
                        val status = checkPermissionStatus(type)
                        result.success(status)
                    }
                    "requestPermission" -> {
                        val type = call.argument<String>("type")
                        pendingResult = result
                        requestPermissionByType(type)
                    }
                    "openPermissionSettings" -> {
                        val type = call.argument<String>("type")
                        openPermissionSettings(type)
                        result.success(true)
                    }
                    "testAccessibilityService" -> {
                        val status = testAccessibilityServiceDetailed()
                        result.success(status)
                    }
                    "enableAccessibilityServiceAuto" -> {
                        val success = enableAccessibilityServiceAutomatically()
                        result.success(success)
                    }
                    "requestAccessibilityServiceWithGuidance" -> {
                        val result_data = requestAccessibilityServiceWithGuidance()
                        result.success(result_data)
                    }
                    "disableAccessibilityServiceAuto" -> {
                        val success = disableAccessibilityServiceAutomatically()
                        result.success(success)
                    }
                    "checkSystemPermissions" -> {
                        val status = checkSystemPermissions()
                        result.success(status)
                    }
                    "grantPermissionsViaRoot" -> {
                        val success = grantPermissionsViaRoot()
                        result.success(success)
                    }
                    "checkRootStatus" -> {
                        val status = checkRootStatus()
                        result.success(status)
                    }
                    "generateAdbCommands" -> {
                        val commands = generateAdbCommands()
                        result.success(commands)
                    }
                    "enableLockScreen" -> {
                        val level = call.argument<String>("level") ?: "basic"
                        val success = enableLockScreen(level)
                        result.success(success)
                    }
                    "disableLockScreen" -> {
                        val success = disableLockScreen()
                        result.success(success)
                    }
                    "enableSuperEnhancedLock" -> {
                        val manager = lockScreenManager
                        val success = manager?.enableSuperEnhancedLock() ?: false
                        result.success(success)
                    }
                    "disableSuperEnhancedLock" -> {
                        val success = disableLockScreen()
                        result.success(success)
                    }
                    "reinforceLock" -> {
                        // 通过锁屏管理器强化锁定
                        try {
                            val manager = lockScreenManager
                            manager?.reinforceLock()
                            result.success(true)
                        } catch (e: Exception) {
                            result.success(false)
                        }
                    }
                    "blockGestures" -> {
                        // 阻止手势的占位方法
                        result.success(true)
                    }
                    "setScreenAlwaysOn" -> {
                        val alwaysOn = call.argument<Boolean>("alwaysOn") ?: false
                        try {
                            if (alwaysOn) {
                                window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                            } else {
                                window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                            }
                            result.success(true)
                        } catch (e: Exception) {
                            result.success(false)
                        }
                    }
                    // 新增：集成专注锁定方法
                    "enableIntegratedFocusLock" -> {
                        val level = call.argument<String>("level") ?: "ultimate"
                        val success = enableIntegratedFocusLock(level)
                        result.success(success)
                    }
                    "disableIntegratedFocusLock" -> {
                        val success = disableIntegratedFocusLock()
                        result.success(success)
                    }
                    "getIntegratedLockStatus" -> {
                        val status = getIntegratedLockStatus()
                        result.success(status)
                    }
                    "enableFocusSystemLock" -> {
                        val success = enableFocusSystemLock()
                        result.success(success)
                    }
                    "disableFocusSystemLock" -> {
                        val success = disableFocusSystemLock()
                        result.success(success)
                    }
                    // 新增：UX流程管理方法
                    "startOptimizedFocusFlow" -> {
                        val taskType = call.argument<String>("taskType") ?: "专注任务"
                        val taskDescription = call.argument<String>("taskDescription") ?: ""
                        val durationMinutes = call.argument<Int>("durationMinutes") ?: 25
                        val lockLevel = call.argument<String>("lockLevel") ?: "ultimate"
                        val success = startOptimizedFocusFlow(taskType, taskDescription, durationMinutes, lockLevel)
                        result.success(success)
                    }
                    "stopFocusFlow" -> {
                        val isEmergencyExit = call.argument<Boolean>("isEmergencyExit") ?: false
                        val success = stopFocusFlow(isEmergencyExit)
                        result.success(success)
                    }
                    "getFocusFlowStatus" -> {
                        val status = getFocusFlowStatus()
                        result.success(status)
                    }
                    // 新增：厂商适配相关方法
                    "getVendorAdaptationStatus" -> {
                        val status = getVendorAdaptationStatus()
                        result.success(status)
                    }
                    "requestVendorPermissions" -> {
                        val success = requestVendorPermissions()
                        result.success(success)
                    }
                    "checkVendorPermission" -> {
                        val permissionType = call.argument<String>("permissionType") ?: ""
                        val status = checkVendorPermission(permissionType)
                        result.success(status)
                    }
                    else -> result.notImplemented()
                }
            }
    }

    /**
     * 检查权限状态
     */
    private fun checkPermissionStatus(type: String?): String {
        return when (type) {
            "accessibility" -> {
                if (isAccessibilityServiceEnabled()) "granted" else "denied"
            }
            "deviceAdmin" -> {
                if (isDeviceAdminActive()) "granted" else "denied"
            }
            "notification" -> {
                // 简化处理，大部分情况下通知权限是默认授权的
                "granted"
            }
            "storage" -> {
                // 在现代Android版本中，应用默认有基础存储权限
                "granted"
            }
            else -> "unknown"
        }
    }

    /**
     * 检查无障碍服务是否启用 - 增强版
     */
    private fun isAccessibilityServiceEnabled(): Boolean {
        try {
            // 方法1: 通过AccessibilityManager检查
            val accessibilityEnabled = isAccessibilityServiceEnabledViaManager()

            // 方法2: 通过Settings检查
            val settingsEnabled = isAccessibilityServiceEnabledViaSettings()

            // 方法3: 通过服务实例检查
            val instanceEnabled = isAccessibilityServiceEnabledViaInstance()

            Log.d(TAG, "无障碍服务检查结果:")
            Log.d(TAG, "  AccessibilityManager: $accessibilityEnabled")
            Log.d(TAG, "  Settings: $settingsEnabled")
            Log.d(TAG, "  Instance: $instanceEnabled")

            // 任何一种方法检测到启用就认为已启用
            val isEnabled = accessibilityEnabled || settingsEnabled || instanceEnabled
            Log.d(TAG, "最终结果: $isEnabled")

            return isEnabled
        } catch (e: Exception) {
            Log.e(TAG, "检查无障碍服务失败: ${e.message}")
            return false
        }
    }

    /**
     * 检查设备管理员权限是否已激活
     */
    private fun isDeviceAdminActive(): Boolean {
        return try {
            val deviceAdminManager = DeviceAdminKioskManager(this)
            deviceAdminManager.isDeviceAdminActive()
        } catch (e: Exception) {
            Log.e(TAG, "检查设备管理员权限失败: ${e.message}")
            false
        }
    }

    /**
     * 通过AccessibilityManager检查
     */
    private fun isAccessibilityServiceEnabledViaManager(): Boolean {
        try {
            val accessibilityManager = getSystemService(Context.ACCESSIBILITY_SERVICE) as AccessibilityManager
            val enabledServices = accessibilityManager.getEnabledAccessibilityServiceList(AccessibilityServiceInfo.FEEDBACK_ALL_MASK)

            val targetServiceName = "com.example.lockphone/.YoYoAccessibilityService"
            val targetServiceClass = "com.example.lockphone.YoYoAccessibilityService"

            Log.d(TAG, "通过AccessibilityManager检查，目标服务: $targetServiceName")
            Log.d(TAG, "已启用的服务数量: ${enabledServices.size}")

            for (service in enabledServices) {
                val serviceName = service.resolveInfo.serviceInfo.name
                val packageName = service.resolveInfo.serviceInfo.packageName
                val fullName = "$packageName/$serviceName"
                val fullName2 = "$packageName/.$serviceName"

                Log.d(TAG, "已启用服务: $serviceName, 包名: $packageName")

                if (serviceName == targetServiceClass ||
                    fullName == targetServiceName ||
                    fullName2 == targetServiceName ||
                    serviceName.endsWith("YoYoAccessibilityService")) {
                    Log.d(TAG, "AccessibilityManager找到匹配的无障碍服务: $serviceName")
                    return true
                }
            }

            Log.d(TAG, "AccessibilityManager未找到YoYo无障碍服务")
            return false
        } catch (e: Exception) {
            Log.e(TAG, "通过AccessibilityManager检查失败: ${e.message}")
            return false
        }
    }

    /**
     * 通过Settings检查
     */
    private fun isAccessibilityServiceEnabledViaSettings(): Boolean {
        try {
            val accessibilityEnabled = Settings.Secure.getString(
                contentResolver,
                Settings.Secure.ACCESSIBILITY_ENABLED
            ) == "1"

            if (!accessibilityEnabled) {
                Log.d(TAG, "Settings显示无障碍功能未启用")
                return false
            }

            val enabledServices = Settings.Secure.getString(
                contentResolver,
                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
            ) ?: ""

            val targetServiceName = "com.example.lockphone/.YoYoAccessibilityService"
            val targetServiceName2 = "com.example.lockphone/com.example.lockphone.YoYoAccessibilityService"

            Log.d(TAG, "Settings中已启用的服务: $enabledServices")

            val isEnabled = enabledServices.contains(targetServiceName) ||
                           enabledServices.contains(targetServiceName2) ||
                           enabledServices.contains("YoYoAccessibilityService")

            Log.d(TAG, "Settings检查结果: $isEnabled")
            return isEnabled
        } catch (e: Exception) {
            Log.e(TAG, "通过Settings检查失败: ${e.message}")
            return false
        }
    }

    /**
     * 通过服务实例检查
     */
    private fun isAccessibilityServiceEnabledViaInstance(): Boolean {
        try {
            // 检查YoYoAccessibilityService的静态实例
            val serviceClass = Class.forName("com.example.lockphone.YoYoAccessibilityService")
            val getInstanceMethod = serviceClass.getMethod("getInstance")
            val instance = getInstanceMethod.invoke(null)

            if (instance != null) {
                val isServiceReadyMethod = serviceClass.getMethod("isServiceReady")
                val isReady = isServiceReadyMethod.invoke(null) as Boolean
                Log.d(TAG, "服务实例检查: instance=$instance, isReady=$isReady")
                return isReady
            }

            Log.d(TAG, "服务实例为null")
            return false
        } catch (e: Exception) {
            Log.d(TAG, "通过服务实例检查失败: ${e.message}")
            return false
        }
    }

    /**
     * 根据类型请求权限
     */
    private fun requestPermissionByType(type: String?) {
        when (type) {
            "accessibility" -> requestAccessibilityPermission()
            "deviceAdmin" -> requestDeviceAdminPermission()
            "notification" -> {
                // 通知权限通常不需要特殊处理
                pendingResult?.success(true)
                pendingResult = null
            }
            "storage" -> {
                // 存储权限通常不需要特殊处理
                pendingResult?.success(true)
                pendingResult = null
            }
            else -> {
                pendingResult?.success(false)
                pendingResult = null
            }
        }
    }

    /**
     * 请求设备管理员权限
     */
    private fun requestDeviceAdminPermission() {
        try {
            val deviceAdminManager = DeviceAdminKioskManager(this)
            val success = deviceAdminManager.requestDeviceAdminPermission()

            if (!success) {
                pendingResult?.success(false)
                pendingResult = null
            }
            // 如果成功发起请求，结果会在onActivityResult中处理
        } catch (e: Exception) {
            Log.e(TAG, "请求设备管理员权限失败: ${e.message}")
            pendingResult?.success(false)
            pendingResult = null
        }
    }

    /**
     * 请求无障碍服务权限
     */
    private fun requestAccessibilityPermission() {
        if (!isAccessibilityServiceEnabled()) {
            val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
            startActivityForResult(intent, REQUEST_ACCESSIBILITY_PERMISSION)
        } else {
            pendingResult?.success(true)
            pendingResult = null
        }
    }

    /**
     * 打开权限设置页面
     */
    private fun openPermissionSettings(type: String?) {
        when (type) {
            "accessibility" -> {
                val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
                startActivity(intent)
            }
            "deviceAdmin" -> {
                val intent = Intent(android.app.admin.DevicePolicyManager.ACTION_ADD_DEVICE_ADMIN)
                startActivity(intent)
            }
            "notification" -> {
                val intent = Intent(Settings.ACTION_APP_NOTIFICATION_SETTINGS).apply {
                    putExtra(Settings.EXTRA_APP_PACKAGE, packageName)
                }
                startActivity(intent)
            }
            else -> {
                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                    data = Uri.parse("package:$packageName")
                }
                startActivity(intent)
            }
        }
    }

    /**
     * 启用锁屏功能
     */
    private fun enableLockScreen(level: String): Boolean {
        return try {
            lockScreenManager?.enableLockScreen(level) ?: false
        } catch (e: Exception) {
            Log.e(TAG, "启用锁屏失败: ${e.message}")
            false
        }
    }

    /**
     * 禁用锁屏功能
     */
    private fun disableLockScreen(): Boolean {
        return try {
            lockScreenManager?.disableLockScreen() ?: false
        } catch (e: Exception) {
            Log.e(TAG, "禁用锁屏失败: ${e.message}")
            false
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        when (requestCode) {
            REQUEST_ACCESSIBILITY_PERMISSION -> {
                val granted = isAccessibilityServiceEnabled()
                pendingResult?.success(granted)
                pendingResult = null
            }
            REQUEST_DEVICE_ADMIN_PERMISSION -> {
                val granted = isDeviceAdminActive()
                Log.d(TAG, "设备管理员权限请求结果: $granted")
                pendingResult?.success(granted)
                pendingResult = null
            }
        }
    }

    /**
     * 详细测试无障碍服务状态
     */
    private fun testAccessibilityServiceDetailed(): Map<String, Any> {
        val result = mutableMapOf<String, Any>()

        try {
            val accessibilityManager = getSystemService(Context.ACCESSIBILITY_SERVICE) as AccessibilityManager
            val enabledServices = accessibilityManager.getEnabledAccessibilityServiceList(AccessibilityServiceInfo.FEEDBACK_ALL_MASK)

            result["totalEnabledServices"] = enabledServices.size
            result["packageName"] = packageName
            result["targetServiceClass"] = "com.example.lockphone.YoYoAccessibilityService"

            val servicesList = mutableListOf<Map<String, String>>()
            for (service in enabledServices) {
                val serviceInfo = mapOf(
                    "name" to service.resolveInfo.serviceInfo.name,
                    "packageName" to service.resolveInfo.serviceInfo.packageName,
                    "fullName" to "${service.resolveInfo.serviceInfo.packageName}/${service.resolveInfo.serviceInfo.name}"
                )
                servicesList.add(serviceInfo)
            }
            result["enabledServices"] = servicesList

            val isEnabled = isAccessibilityServiceEnabled()
            result["isYoYoServiceEnabled"] = isEnabled

            Log.d(TAG, "无障碍服务详细状态: $result")

        } catch (e: Exception) {
            result["error"] = e.message ?: "Unknown error"
            Log.e(TAG, "测试无障碍服务失败: ${e.message}")
        }

        return result
    }

    /**
     * 检查系统权限状态
     */
    private fun checkSystemPermissions(): Map<String, Any> {
        val result = mutableMapOf<String, Any>()

        try {
            // 检查WRITE_SECURE_SETTINGS权限
            val hasWriteSecureSettings = try {
                Settings.Secure.putString(contentResolver, "test_permission_check", "test")
                Settings.Secure.getString(contentResolver, "test_permission_check")
                true
            } catch (e: SecurityException) {
                false
            }

            // 检查WRITE_SETTINGS权限
            val hasWriteSettings = try {
                Settings.System.canWrite(this)
            } catch (e: Exception) {
                false
            }

            // 检查设备信息
            val deviceInfo = mapOf(
                "manufacturer" to android.os.Build.MANUFACTURER,
                "model" to android.os.Build.MODEL,
                "version" to android.os.Build.VERSION.RELEASE,
                "sdk" to android.os.Build.VERSION.SDK_INT,
                "brand" to android.os.Build.BRAND
            )

            // 检查当前无障碍服务状态
            val accessibilityEnabled = Settings.Secure.getString(
                contentResolver,
                Settings.Secure.ACCESSIBILITY_ENABLED
            ) == "1"

            val enabledServices = Settings.Secure.getString(
                contentResolver,
                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
            ) ?: ""

            result["hasWriteSecureSettings"] = hasWriteSecureSettings
            result["hasWriteSettings"] = hasWriteSettings
            result["deviceInfo"] = deviceInfo
            result["accessibilityEnabled"] = accessibilityEnabled
            result["enabledServices"] = enabledServices
            result["packageName"] = packageName
            result["targetService"] = "com.example.lockphone/.YoYoAccessibilityService"

            Log.d(TAG, "系统权限检查结果: $result")

        } catch (e: Exception) {
            result["error"] = e.message ?: "Unknown error"
            Log.e(TAG, "检查系统权限失败: ${e.message}")
        }

        return result
    }

    /**
     * 检查ROOT状态
     */
    private fun checkRootStatus(): Map<String, Any> {
        val result = mutableMapOf<String, Any>()

        try {
            // 检查ROOT可用性
            val isRootAvailable = isRootAvailable()
            result["isRootAvailable"] = isRootAvailable

            // 检查常见ROOT管理器
            val rootManagers = arrayOf(
                "com.topjohnwu.magisk",
                "eu.chainfire.supersu",
                "com.koushikdutta.superuser",
                "com.noshufou.android.su"
            )

            val installedRootManagers = mutableListOf<String>()
            for (manager in rootManagers) {
                try {
                    packageManager.getPackageInfo(manager, 0)
                    installedRootManagers.add(manager)
                } catch (e: Exception) {
                    // 包未安装
                }
            }

            result["installedRootManagers"] = installedRootManagers
            result["hasRootManager"] = installedRootManagers.isNotEmpty()

            // 检查su二进制文件
            val suPaths = arrayOf(
                "/system/bin/su",
                "/system/xbin/su",
                "/sbin/su",
                "/vendor/bin/su"
            )

            val availableSuPaths = mutableListOf<String>()
            for (path in suPaths) {
                if (java.io.File(path).exists()) {
                    availableSuPaths.add(path)
                }
            }

            result["availableSuPaths"] = availableSuPaths
            result["hasSuBinary"] = availableSuPaths.isNotEmpty()

            Log.d(TAG, "ROOT状态检查: $result")

        } catch (e: Exception) {
            result["error"] = e.message ?: "Unknown error"
            Log.e(TAG, "ROOT状态检查失败: ${e.message}")
        }

        return result
    }

    /**
     * 生成ADB命令
     */
    private fun generateAdbCommands(): Map<String, Any> {
        val result = mutableMapOf<String, Any>()

        try {
            val commands = mutableListOf<String>()

            // 基础权限授予命令
            commands.add("adb shell pm grant $packageName android.permission.WRITE_SECURE_SETTINGS")
            commands.add("adb shell pm grant $packageName android.permission.WRITE_SETTINGS")

            // 设备管理员命令
            commands.add("adb shell dpm set-device-owner $packageName/.DeviceAdminReceiver")

            // 电池优化白名单
            commands.add("adb shell dumpsys deviceidle whitelist +$packageName")

            // 无障碍服务测试命令
            commands.add("adb shell settings put secure enabled_accessibility_services $packageName/.YoYoAccessibilityService")
            commands.add("adb shell settings put secure accessibility_enabled 1")

            // 恢复命令
            commands.add("adb shell settings put secure enabled_accessibility_services \"\"")
            commands.add("adb shell settings put secure accessibility_enabled 0")

            result["commands"] = commands
            result["packageName"] = packageName
            result["serviceName"] = "$packageName/.YoYoAccessibilityService"

            // 生成批处理脚本内容
            val batchScript = StringBuilder()
            batchScript.appendLine("@echo off")
            batchScript.appendLine("echo 专注锁屏应用权限授予脚本")
            batchScript.appendLine("echo ================================")
            batchScript.appendLine("echo.")
            batchScript.appendLine("echo 请确保设备已连接并启用USB调试")
            batchScript.appendLine("echo.")

            for ((index, command) in commands.withIndex()) {
                if (index < commands.size - 2) { // 排除恢复命令
                    batchScript.appendLine("echo 执行: $command")
                    batchScript.appendLine(command)
                    batchScript.appendLine("echo.")
                }
            }

            batchScript.appendLine("echo 权限授予完成！")
            batchScript.appendLine("pause")

            result["batchScript"] = batchScript.toString()

            Log.d(TAG, "ADB命令生成完成")

        } catch (e: Exception) {
            result["error"] = e.message ?: "Unknown error"
            Log.e(TAG, "ADB命令生成失败: ${e.message}")
        }

        return result
    }

    /**
     * 检查ROOT权限是否可用
     */
    private fun isRootAvailable(): Boolean {
        return try {
            val process = Runtime.getRuntime().exec("su")
            val writer = java.io.OutputStreamWriter(process.outputStream)
            writer.write("id\n")
            writer.write("exit\n")
            writer.flush()
            writer.close()

            val exitCode = process.waitFor()
            Log.d(TAG, "ROOT检查退出码: $exitCode")
            exitCode == 0
        } catch (e: Exception) {
            Log.d(TAG, "ROOT不可用: ${e.message}")
            false
        }
    }

    /**
     * 通过Root权限授予系统权限
     */
    private fun grantPermissionsViaRoot(): Boolean {
        return try {
            // 首先检查ROOT是否可用
            if (!isRootAvailable()) {
                Log.w(TAG, "设备未ROOT或ROOT权限被拒绝")
                return false
            }

            val commands = arrayOf(
                "pm grant $packageName android.permission.WRITE_SECURE_SETTINGS",
                "pm grant $packageName android.permission.WRITE_SETTINGS"
            )

            for (command in commands) {
                val process = Runtime.getRuntime().exec("su")
                val writer = java.io.OutputStreamWriter(process.outputStream)
                writer.write("$command\n")
                writer.write("exit\n")
                writer.flush()
                writer.close()

                val exitCode = process.waitFor()
                Log.d(TAG, "Root命令执行: $command, 退出码: $exitCode")

                if (exitCode != 0) {
                    Log.w(TAG, "Root命令执行失败: $command")
                    return false
                }
            }

            Log.d(TAG, "Root权限授予成功")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Root权限授予失败: ${e.message}")
            false
        }
    }

    /**
     * 请求无障碍服务权限并提供引导 - 新的用户友好流程
     */
    private fun requestAccessibilityServiceWithGuidance(): Map<String, Any> {
        val result = mutableMapOf<String, Any>()

        try {
            // 检查当前状态
            val isEnabled = isAccessibilityServiceEnabled()
            result["isCurrentlyEnabled"] = isEnabled

            if (isEnabled) {
                result["success"] = true
                result["message"] = "无障碍服务已启用"
                return result
            }

            // 收集设备和权限信息
            val deviceInfo = getDeviceInfo()
            val systemPermissions = checkSystemPermissions()

            result["deviceInfo"] = deviceInfo
            result["systemPermissions"] = systemPermissions

            // 尝试自动启用
            val autoEnableSuccess = enableAccessibilityServiceAutomatically()
            result["autoEnableAttempted"] = true
            result["autoEnableSuccess"] = autoEnableSuccess

            if (autoEnableSuccess) {
                result["success"] = true
                result["message"] = "无障碍服务自动启用成功"
                return result
            }

            // 自动启用失败，提供详细的引导信息
            result["success"] = false
            result["requiresManualSetup"] = true
            result["guidanceInfo"] = generateAccessibilityGuidance(deviceInfo, systemPermissions)

            // 尝试打开特定的无障碍设置页面
            val intentSuccess = openAccessibilitySettingsWithGuidance()
            result["settingsIntentSuccess"] = intentSuccess

        } catch (e: Exception) {
            Log.e(TAG, "请求无障碍服务权限失败: ${e.message}")
            result["success"] = false
            result["error"] = e.message ?: "未知错误"
        }

        return result
    }

    /**
     * 生成无障碍服务设置引导信息
     */
    private fun generateAccessibilityGuidance(deviceInfo: Map<String, String>, systemPermissions: Map<String, Any>): Map<String, Any> {
        val guidance = mutableMapOf<String, Any>()

        val manufacturer = deviceInfo["manufacturer"]?.lowercase() ?: ""
        val brand = deviceInfo["brand"]?.lowercase() ?: ""
        val hasWriteSecure = systemPermissions["hasWriteSecureSettings"] as? Boolean ?: false
        val isRootAvailable = systemPermissions["isRootAvailable"] as? Boolean ?: false

        // 基础步骤
        guidance["basicSteps"] = listOf(
            "在无障碍设置中找到\"专注锁屏服务\"",
            "点击进入服务详情页面",
            "开启服务开关",
            "在弹出的对话框中点击\"确定\""
        )

        // 厂商特定引导
        when {
            manufacturer.contains("xiaomi") || brand.contains("xiaomi") || brand.contains("redmi") -> {
                guidance["vendorSpecific"] = mapOf(
                    "vendor" to "小米",
                    "additionalSteps" to listOf(
                        "可能需要在MIUI优化中关闭\"内存优化\"",
                        "在\"自启动管理\"中允许应用自启动",
                        "在\"省电策略\"中设置为\"无限制\""
                    )
                )
            }
            manufacturer.contains("oppo") || brand.contains("oppo") || brand.contains("oneplus") -> {
                guidance["vendorSpecific"] = mapOf(
                    "vendor" to "OPPO",
                    "additionalSteps" to listOf(
                        "在\"应用管理\"中找到应用",
                        "设置\"允许后台运行\"",
                        "关闭\"智能省电\""
                    )
                )
            }
            manufacturer.contains("huawei") || brand.contains("huawei") || brand.contains("honor") -> {
                guidance["vendorSpecific"] = mapOf(
                    "vendor" to "华为",
                    "additionalSteps" to listOf(
                        "在\"应用启动管理\"中设置为手动管理",
                        "开启\"允许自启动\"、\"允许关联启动\"、\"允许后台活动\"",
                        "在\"省电模式\"中添加到白名单"
                    )
                )
            }
        }

        // 权限状态说明
        guidance["permissionStatus"] = mapOf(
            "hasWriteSecureSettings" to hasWriteSecure,
            "isRootAvailable" to isRootAvailable,
            "canAutoEnable" to (hasWriteSecure || isRootAvailable)
        )

        // 替代方案
        if (!hasWriteSecure && !isRootAvailable) {
            guidance["alternatives"] = listOf(
                "使用ADB命令授予权限（需要电脑）",
                "Root设备后重试自动启用",
                "手动启用无障碍服务"
            )
        }

        return guidance
    }

    /**
     * 打开无障碍设置并提供引导
     */
    private fun openAccessibilitySettingsWithGuidance(): Boolean {
        return try {
            val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            startActivity(intent)
            true
        } catch (e: Exception) {
            Log.e(TAG, "打开无障碍设置失败: ${e.message}")
            false
        }
    }

    /**
     * 自动启用无障碍服务 - 增强版
     */
    private fun enableAccessibilityServiceAutomatically(): Boolean {
        return try {
            if (isAccessibilityServiceEnabled()) {
                Log.d(TAG, "无障碍服务已启用")
                return true
            }

            val serviceName = "com.example.lockphone/.YoYoAccessibilityService"
            Log.d(TAG, "开始自动启用无障碍服务: $serviceName")

            // 检查设备信息和权限状态
            val deviceInfo = getDeviceInfo()
            val permissionStatus = checkSystemPermissions()

            Log.d(TAG, "设备信息: $deviceInfo")
            Log.d(TAG, "权限状态: $permissionStatus")

            // 方法1: 通过Settings API启用（需要WRITE_SECURE_SETTINGS权限）
            if (enableViaSettingsAPI(serviceName)) {
                return true
            }

            // 方法2: 通过Root权限启用（如果设备已Root）
            if (enableViaRoot(serviceName)) {
                return true
            }

            // 方法3: 通过Shell命令启用（备用方法）
            if (enableViaShell(serviceName)) {
                return true
            }

            // 方法4: 尝试厂商特定的启用方法
            if (enableViaVendorSpecific(serviceName, deviceInfo)) {
                return true
            }

            Log.w(TAG, "所有自动启用方法都失败")
            false

        } catch (e: Exception) {
            Log.e(TAG, "启用无障碍服务异常: ${e.message}")
            false
        }
    }

    /**
     * 通过Settings API启用
     */
    private fun enableViaSettingsAPI(serviceName: String): Boolean {
        return try {
            Log.d(TAG, "尝试通过Settings API启用")

            // 获取当前启用的服务列表
            val enabledServices = Settings.Secure.getString(
                contentResolver,
                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
            ) ?: ""

            // 检查服务是否已在列表中
            if (!enabledServices.contains(serviceName)) {
                // 添加服务到启用列表
                val newEnabledServices = if (enabledServices.isEmpty()) {
                    serviceName
                } else {
                    "$enabledServices:$serviceName"
                }

                // 尝试写入设置
                Settings.Secure.putString(
                    contentResolver,
                    Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES,
                    newEnabledServices
                )

                // 启用无障碍功能
                Settings.Secure.putString(
                    contentResolver,
                    Settings.Secure.ACCESSIBILITY_ENABLED,
                    "1"
                )

                Log.d(TAG, "Settings API写入完成，等待服务启动")
                return waitForServiceEnabled(10)
            }

            false
        } catch (e: SecurityException) {
            Log.w(TAG, "Settings API失败，缺少WRITE_SECURE_SETTINGS权限: ${e.message}")
            false
        } catch (e: Exception) {
            Log.e(TAG, "Settings API启用失败: ${e.message}")
            false
        }
    }

    /**
     * 通过Root权限启用
     */
    private fun enableViaRoot(serviceName: String): Boolean {
        return try {
            Log.d(TAG, "尝试通过Root权限启用")

            val commands = arrayOf(
                "settings put secure enabled_accessibility_services $serviceName",
                "settings put secure accessibility_enabled 1"
            )

            for (command in commands) {
                val process = Runtime.getRuntime().exec(arrayOf("su", "-c", command))
                val exitCode = process.waitFor()

                if (exitCode != 0) {
                    Log.w(TAG, "Root命令失败: $command")
                    return false
                }
            }

            Log.d(TAG, "Root命令执行完成，等待服务启动")
            return waitForServiceEnabled(10)

        } catch (e: Exception) {
            Log.w(TAG, "Root权限启用失败: ${e.message}")
            false
        }
    }

    /**
     * 通过Shell命令启用
     */
    private fun enableViaShell(serviceName: String): Boolean {
        return try {
            Log.d(TAG, "尝试通过Shell命令启用")

            val commands = arrayOf(
                "settings put secure enabled_accessibility_services $serviceName",
                "settings put secure accessibility_enabled 1"
            )

            for (command in commands) {
                val process = Runtime.getRuntime().exec(command)
                val exitCode = process.waitFor()

                if (exitCode != 0) {
                    Log.w(TAG, "Shell命令失败: $command")
                    return false
                }
            }

            Log.d(TAG, "Shell命令执行完成，等待服务启动")
            return waitForServiceEnabled(10)

        } catch (e: Exception) {
            Log.w(TAG, "Shell命令启用失败: ${e.message}")
            false
        }
    }

    /**
     * 等待服务启用
     */
    private fun waitForServiceEnabled(maxRetries: Int): Boolean {
        for (i in 1..maxRetries) {
            Thread.sleep(500)
            val isEnabled = isAccessibilityServiceEnabled()
            Log.d(TAG, "第${i}次检查无障碍服务状态: $isEnabled")

            if (isEnabled) {
                Log.d(TAG, "无障碍服务启用成功")
                return true
            }
        }

        Log.w(TAG, "等待${maxRetries * 0.5}秒后无障碍服务仍未启用")
        return false
    }

    /**
     * 获取设备信息
     */
    private fun getDeviceInfo(): Map<String, String> {
        return mapOf(
            "manufacturer" to android.os.Build.MANUFACTURER,
            "brand" to android.os.Build.BRAND,
            "model" to android.os.Build.MODEL,
            "device" to android.os.Build.DEVICE,
            "product" to android.os.Build.PRODUCT,
            "androidVersion" to android.os.Build.VERSION.RELEASE,
            "sdkInt" to android.os.Build.VERSION.SDK_INT.toString(),
            "securityPatch" to android.os.Build.VERSION.SECURITY_PATCH
        )
    }

    /**
     * 厂商特定的启用方法
     */
    private fun enableViaVendorSpecific(serviceName: String, deviceInfo: Map<String, String>): Boolean {
        val manufacturer = deviceInfo["manufacturer"]?.lowercase() ?: ""
        val brand = deviceInfo["brand"]?.lowercase() ?: ""

        Log.d(TAG, "尝试厂商特定启用方法: $manufacturer / $brand")

        return when {
            manufacturer.contains("xiaomi") || brand.contains("xiaomi") || brand.contains("redmi") -> {
                enableViaXiaomi(serviceName)
            }
            manufacturer.contains("oppo") || brand.contains("oppo") || brand.contains("oneplus") -> {
                enableViaOppo(serviceName)
            }
            manufacturer.contains("huawei") || brand.contains("huawei") || brand.contains("honor") -> {
                enableViaHuawei(serviceName)
            }
            manufacturer.contains("vivo") -> {
                enableViaVivo(serviceName)
            }
            manufacturer.contains("samsung") -> {
                enableViaSamsung(serviceName)
            }
            else -> {
                Log.d(TAG, "未知厂商，跳过厂商特定方法")
                false
            }
        }
    }

    /**
     * 小米设备特定启用方法
     */
    private fun enableViaXiaomi(serviceName: String): Boolean {
        return try {
            Log.d(TAG, "尝试小米设备特定方法")

            // 小米设备可能需要特殊的权限处理
            val commands = arrayOf(
                "settings put secure enabled_accessibility_services $serviceName",
                "settings put secure accessibility_enabled 1",
                // 小米特有的设置
                "settings put global accessibility_shortcut_enabled 1"
            )

            for (command in commands) {
                try {
                    val process = Runtime.getRuntime().exec(command)
                    process.waitFor()
                } catch (e: Exception) {
                    Log.w(TAG, "小米命令执行失败: $command - ${e.message}")
                }
            }

            waitForServiceEnabled(5)
        } catch (e: Exception) {
            Log.w(TAG, "小米特定方法失败: ${e.message}")
            false
        }
    }

    /**
     * OPPO设备特定启用方法
     */
    private fun enableViaOppo(serviceName: String): Boolean {
        return try {
            Log.d(TAG, "尝试OPPO设备特定方法")

            // OPPO设备的特殊处理
            val commands = arrayOf(
                "settings put secure enabled_accessibility_services $serviceName",
                "settings put secure accessibility_enabled 1"
            )

            for (command in commands) {
                try {
                    val process = Runtime.getRuntime().exec(command)
                    process.waitFor()
                } catch (e: Exception) {
                    Log.w(TAG, "OPPO命令执行失败: $command - ${e.message}")
                }
            }

            waitForServiceEnabled(5)
        } catch (e: Exception) {
            Log.w(TAG, "OPPO特定方法失败: ${e.message}")
            false
        }
    }

    /**
     * 华为设备特定启用方法
     */
    private fun enableViaHuawei(serviceName: String): Boolean {
        return try {
            Log.d(TAG, "尝试华为设备特定方法")

            // 华为设备的特殊处理
            val commands = arrayOf(
                "settings put secure enabled_accessibility_services $serviceName",
                "settings put secure accessibility_enabled 1"
            )

            for (command in commands) {
                try {
                    val process = Runtime.getRuntime().exec(command)
                    process.waitFor()
                } catch (e: Exception) {
                    Log.w(TAG, "华为命令执行失败: $command - ${e.message}")
                }
            }

            waitForServiceEnabled(5)
        } catch (e: Exception) {
            Log.w(TAG, "华为特定方法失败: ${e.message}")
            false
        }
    }

    /**
     * Vivo设备特定启用方法
     */
    private fun enableViaVivo(serviceName: String): Boolean {
        return try {
            Log.d(TAG, "尝试Vivo设备特定方法")

            val commands = arrayOf(
                "settings put secure enabled_accessibility_services $serviceName",
                "settings put secure accessibility_enabled 1"
            )

            for (command in commands) {
                try {
                    val process = Runtime.getRuntime().exec(command)
                    process.waitFor()
                } catch (e: Exception) {
                    Log.w(TAG, "Vivo命令执行失败: $command - ${e.message}")
                }
            }

            waitForServiceEnabled(5)
        } catch (e: Exception) {
            Log.w(TAG, "Vivo特定方法失败: ${e.message}")
            false
        }
    }

    /**
     * 三星设备特定启用方法
     */
    private fun enableViaSamsung(serviceName: String): Boolean {
        return try {
            Log.d(TAG, "尝试三星设备特定方法")

            val commands = arrayOf(
                "settings put secure enabled_accessibility_services $serviceName",
                "settings put secure accessibility_enabled 1"
            )

            for (command in commands) {
                try {
                    val process = Runtime.getRuntime().exec(command)
                    process.waitFor()
                } catch (e: Exception) {
                    Log.w(TAG, "三星命令执行失败: $command - ${e.message}")
                }
            }

            waitForServiceEnabled(5)
        } catch (e: Exception) {
            Log.w(TAG, "三星特定方法失败: ${e.message}")
            false
        }
    }

    /**
     * 自动禁用无障碍服务
     */
    private fun disableAccessibilityServiceAutomatically(): Boolean {
        return try {
            if (!isAccessibilityServiceEnabled()) {
                Log.d(TAG, "无障碍服务已禁用")
                return true
            }

            val serviceName = "com.example.lockphone/.YoYoAccessibilityService"

            try {
                // 获取当前启用的服务列表
                val enabledServices = Settings.Secure.getString(
                    contentResolver,
                    Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
                ) ?: ""

                // 从列表中移除我们的服务
                val servicesList = enabledServices.split(":").toMutableList()
                servicesList.removeAll { it == serviceName }

                val newEnabledServices = servicesList.joinToString(":")

                // 更新设置
                Settings.Secure.putString(
                    contentResolver,
                    Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES,
                    newEnabledServices
                )

                // 如果没有其他无障碍服务，禁用整个无障碍功能
                if (newEnabledServices.isEmpty()) {
                    Settings.Secure.putString(
                        contentResolver,
                        Settings.Secure.ACCESSIBILITY_ENABLED,
                        "0"
                    )
                }

                Log.d(TAG, "尝试自动禁用无障碍服务")

                // 等待一下让系统处理
                Thread.sleep(1000)

                return !isAccessibilityServiceEnabled()

            } catch (e: SecurityException) {
                Log.w(TAG, "没有WRITE_SECURE_SETTINGS权限，无法自动禁用: ${e.message}")
            } catch (e: Exception) {
                Log.e(TAG, "自动禁用无障碍服务失败: ${e.message}")
            }

            Log.d(TAG, "自动禁用失败")
            false

        } catch (e: Exception) {
            Log.e(TAG, "禁用无障碍服务异常: ${e.message}")
            false
        }
    }

    /**
     * 启用集成专注锁定模式
     */
    private fun enableIntegratedFocusLock(level: String): Boolean {
        return try {
            Log.d(TAG, "🔒 MainActivity: 启用集成专注锁定模式 - 级别: $level")
            val success = integratedFocusLockManager.enableIntegratedFocusLock(level)

            if (success) {
                Log.d(TAG, "✅ MainActivity: 集成专注锁定模式启用成功")
            } else {
                Log.e(TAG, "❌ MainActivity: 集成专注锁定模式启用失败")
            }

            success
        } catch (e: Exception) {
            Log.e(TAG, "❌ MainActivity: 启用集成专注锁定模式异常: ${e.message}")
            false
        }
    }

    /**
     * 禁用集成专注锁定模式
     */
    private fun disableIntegratedFocusLock(): Boolean {
        return try {
            Log.d(TAG, "🔒 MainActivity: 禁用集成专注锁定模式")
            val success = integratedFocusLockManager.disableIntegratedFocusLock()

            if (success) {
                Log.d(TAG, "✅ MainActivity: 集成专注锁定模式禁用成功")
            } else {
                Log.e(TAG, "❌ MainActivity: 集成专注锁定模式禁用失败")
            }

            success
        } catch (e: Exception) {
            Log.e(TAG, "❌ MainActivity: 禁用集成专注锁定模式异常: ${e.message}")
            false
        }
    }

    /**
     * 获取集成锁定状态
     */
    private fun getIntegratedLockStatus(): Map<String, Any> {
        return try {
            val status = integratedFocusLockManager.getCurrentLockStatus()
            mapOf(
                "isActive" to status.isActive,
                "level" to status.level,
                "systemLockActive" to status.systemLockActive,
                "overlayActive" to status.overlayActive,
                "kioskActive" to status.kioskActive,
                "accessibilityActive" to status.accessibilityActive
            )
        } catch (e: Exception) {
            Log.e(TAG, "❌ MainActivity: 获取集成锁定状态异常: ${e.message}")
            mapOf(
                "isActive" to false,
                "level" to "none",
                "error" to (e.message ?: "Unknown error")
            )
        }
    }

    /**
     * 启用专注模式系统锁屏
     */
    private fun enableFocusSystemLock(): Boolean {
        return try {
            Log.d(TAG, "🔒 MainActivity: 启用专注模式系统锁屏")
            // 这里可以直接调用集成管理器的系统锁屏功能
            // 或者提供单独的系统锁屏控制
            enableIntegratedFocusLock("basic")
        } catch (e: Exception) {
            Log.e(TAG, "❌ MainActivity: 启用专注模式系统锁屏异常: ${e.message}")
            false
        }
    }

    /**
     * 禁用专注模式系统锁屏
     */
    private fun disableFocusSystemLock(): Boolean {
        return try {
            Log.d(TAG, "🔒 MainActivity: 禁用专注模式系统锁屏")
            disableIntegratedFocusLock()
        } catch (e: Exception) {
            Log.e(TAG, "❌ MainActivity: 禁用专注模式系统锁屏异常: ${e.message}")
            false
        }
    }

    /**
     * 启动优化的专注流程
     */
    private fun startOptimizedFocusFlow(
        taskType: String,
        taskDescription: String,
        durationMinutes: Int,
        lockLevel: String
    ): Boolean {
        return try {
            Log.d(TAG, "🚀 MainActivity: 启动优化的专注流程")

            val success = uxFlowManager.startOptimizedFocusFlow(
                taskType = taskType,
                taskDescription = taskDescription,
                durationMinutes = durationMinutes,
                lockLevel = lockLevel,
                callbacks = createUXFlowCallbacks()
            )

            if (success) {
                Log.d(TAG, "✅ MainActivity: 优化的专注流程启动成功")
            } else {
                Log.e(TAG, "❌ MainActivity: 优化的专注流程启动失败")
            }

            success
        } catch (e: Exception) {
            Log.e(TAG, "❌ MainActivity: 启动优化的专注流程异常: ${e.message}")
            false
        }
    }

    /**
     * 停止专注流程
     */
    private fun stopFocusFlow(isEmergencyExit: Boolean): Boolean {
        return try {
            Log.d(TAG, "🛑 MainActivity: 停止专注流程")
            val success = uxFlowManager.stopFocusFlow(isEmergencyExit)

            if (success) {
                Log.d(TAG, "✅ MainActivity: 专注流程停止成功")
            } else {
                Log.e(TAG, "❌ MainActivity: 专注流程停止失败")
            }

            success
        } catch (e: Exception) {
            Log.e(TAG, "❌ MainActivity: 停止专注流程异常: ${e.message}")
            false
        }
    }

    /**
     * 获取专注流程状态
     */
    private fun getFocusFlowStatus(): Map<String, Any> {
        return try {
            val flowState = uxFlowManager.getCurrentFlowState()
            val sessionData = uxFlowManager.getCurrentSessionData()

            mapOf(
                "flowState" to flowState.name,
                "isInFocusMode" to uxFlowManager.isInFocusMode(),
                "sessionData" to (sessionData?.let {
                    mapOf(
                        "taskType" to it.taskType,
                        "taskDescription" to it.taskDescription,
                        "durationMinutes" to it.durationMinutes,
                        "startTime" to it.startTime,
                        "lockLevel" to it.lockLevel,
                        "dailyFocusCount" to it.dailyFocusCount,
                        "totalFocusMinutes" to it.totalFocusMinutes
                    )
                } ?: emptyMap<String, Any>())
            )
        } catch (e: Exception) {
            Log.e(TAG, "❌ MainActivity: 获取专注流程状态异常: ${e.message}")
            mapOf(
                "flowState" to "ERROR",
                "isInFocusMode" to false,
                "error" to (e.message ?: "Unknown error")
            )
        }
    }

    /**
     * 创建UX流程回调
     */
    private fun createUXFlowCallbacks(): UXFlowCallbacks {
        return object : UXFlowCallbacks {
            override fun onPreparationStart() {
                Log.d(TAG, "📋 UX回调: 准备阶段开始")
                // 可以通过MethodChannel通知Flutter
            }

            override fun onShowPreparationUI(sessionData: FocusSessionData?) {
                Log.d(TAG, "📱 UX回调: 显示准备界面")
                // 通知Flutter显示准备界面
            }

            override fun onCountdownStart() {
                Log.d(TAG, "⏰ UX回调: 倒计时开始")
            }

            override fun onCountdownTick(number: Int) {
                Log.d(TAG, "⏰ UX回调: 倒计时 $number")
                // 可以通过MethodChannel通知Flutter更新倒计时显示
            }

            override fun onCountdownComplete() {
                Log.d(TAG, "⏰ UX回调: 倒计时完成")
            }

            override fun onLockActivationStart() {
                Log.d(TAG, "🔒 UX回调: 锁定激活开始")
            }

            override fun onLockActivated() {
                Log.d(TAG, "🔒 UX回调: 锁定已激活")
            }

            override fun onFocusInterfaceShown() {
                Log.d(TAG, "🎯 UX回调: 专注界面已显示")
            }

            override fun onFocusComplete(sessionData: FocusSessionData?) {
                Log.d(TAG, "🎉 UX回调: 专注完成")
                // 可以通过MethodChannel通知Flutter专注完成
            }

            override fun onEmergencyExit() {
                Log.w(TAG, "🚨 UX回调: 紧急退出")
                // 可以通过MethodChannel通知Flutter紧急退出
            }

            override fun onFlowError(error: String) {
                Log.e(TAG, "❌ UX回调: 流程错误 - $error")
                // 可以通过MethodChannel通知Flutter错误信息
            }
        }
    }

    /**
     * 获取厂商适配状态
     */
    private fun getVendorAdaptationStatus(): Map<String, Any> {
        return try {
            Log.d(TAG, "📱 MainActivity: 获取厂商适配状态")

            val adaptationStatus = integratedFocusLockManager.getVendorAdaptationStatus()

            mapOf(
                "vendor" to adaptationStatus.romInfo.vendor.name,
                "romName" to adaptationStatus.romInfo.romName,
                "romVersion" to adaptationStatus.romInfo.version,
                "androidVersion" to adaptationStatus.romInfo.androidVersion,
                "apiLevel" to adaptationStatus.romInfo.apiLevel,
                "brand" to adaptationStatus.romInfo.brand,
                "model" to adaptationStatus.romInfo.model,
                "adaptationLevel" to adaptationStatus.adaptationLevel.name,
                "requiredPermissions" to adaptationStatus.requiredPermissions.map { permission ->
                    mapOf(
                        "type" to permission.type.name,
                        "permission" to permission.permission,
                        "displayName" to permission.displayName,
                        "description" to permission.description,
                        "isRequired" to permission.isRequired,
                        "requestMethod" to permission.requestMethod.name
                    )
                },
                "grantedPermissions" to adaptationStatus.grantedPermissions.map { it.name },
                "missingPermissions" to adaptationStatus.missingPermissions.map { it.name },
                "recommendations" to adaptationStatus.recommendations
            )
        } catch (e: Exception) {
            Log.e(TAG, "❌ MainActivity: 获取厂商适配状态异常: ${e.message}")
            mapOf(
                "vendor" to "UNKNOWN",
                "error" to (e.message ?: "Unknown error")
            )
        }
    }

    /**
     * 申请厂商权限
     */
    private fun requestVendorPermissions(): Boolean {
        return try {
            Log.d(TAG, "🔐 MainActivity: 申请厂商权限")

            val success = integratedFocusLockManager.requestVendorPermissions()

            if (success) {
                Log.d(TAG, "✅ MainActivity: 厂商权限申请成功")
            } else {
                Log.e(TAG, "❌ MainActivity: 厂商权限申请失败")
            }

            success
        } catch (e: Exception) {
            Log.e(TAG, "❌ MainActivity: 申请厂商权限异常: ${e.message}")
            false
        }
    }

    /**
     * 检查特定厂商权限
     */
    private fun checkVendorPermission(permissionType: String): Map<String, Any> {
        return try {
            Log.d(TAG, "🔍 MainActivity: 检查厂商权限 - $permissionType")

            // 这里可以添加具体的权限检查逻辑
            // 暂时返回基本信息
            mapOf(
                "permissionType" to permissionType,
                "status" to "NEED_MANUAL",
                "message" to "需要手动设置此权限"
            )
        } catch (e: Exception) {
            Log.e(TAG, "❌ MainActivity: 检查厂商权限异常: ${e.message}")
            mapOf(
                "permissionType" to permissionType,
                "status" to "ERROR",
                "error" to (e.message ?: "Unknown error")
            )
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        lockScreenManager?.cleanup()

        // 清理集成专注锁定管理器
        if (::integratedFocusLockManager.isInitialized) {
            integratedFocusLockManager.cleanup()
        }

        // 清理UX流程管理器
        if (::uxFlowManager.isInitialized) {
            uxFlowManager.cleanup()
        }
    }
}
