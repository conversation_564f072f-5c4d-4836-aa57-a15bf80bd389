import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// 权限测试页面
/// 用于测试和验证权限申请流程
class PermissionTestPage extends StatefulWidget {
  const PermissionTestPage({super.key});

  @override
  State<PermissionTestPage> createState() => _PermissionTestPageState();
}

class _PermissionTestPageState extends State<PermissionTestPage> {
  static const platform = MethodChannel('yoyo_lock_screen');
  
  Map<String, dynamic> _permissionStatus = {};
  bool _isLoading = false;
  String _lastTestResult = '';

  @override
  void initState() {
    super.initState();
    _checkPermissionStatus();
  }

  /// 检查权限状态
  Future<void> _checkPermissionStatus() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await platform.invokeMethod('checkPermissionStatus');
      setState(() {
        _permissionStatus = Map<String, dynamic>.from(result);
        _isLoading = false;
      });
      debugPrint('📊 权限状态: $_permissionStatus');
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      debugPrint('❌ 检查权限状态失败: $e');
    }
  }

  /// 申请权限
  Future<void> _requestPermissions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await platform.invokeMethod('requestPermissions');
      setState(() {
        _isLoading = false;
      });
      
      // 延迟检查权限状态，给用户时间授权
      await Future.delayed(const Duration(seconds: 2));
      await _checkPermissionStatus();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      debugPrint('❌ 申请权限失败: $e');
    }
  }

  /// 测试手势阻止功能
  Future<void> _testGestureBlocking() async {
    setState(() {
      _isLoading = true;
      _lastTestResult = '正在测试手势阻止功能...';
    });

    try {
      // 检查权限状态
      await _checkPermissionStatus();
      
      final hasOverlay = _permissionStatus['hasOverlayPermission'] ?? false;
      final hasAccessibility = _permissionStatus['hasAccessibilityService'] ?? false;
      
      if (!hasOverlay) {
        setState(() {
          _lastTestResult = '❌ 缺少悬浮窗权限，无法启用手势阻止';
          _isLoading = false;
        });
        return;
      }

      // 尝试启用集成专注锁定
      final result = await platform.invokeMethod('enableIntegratedFocusLock', {
        'level': 'ultimate',
      });

      setState(() {
        _lastTestResult = result 
          ? '✅ 手势阻止功能启用成功！请尝试各种手势操作'
          : '❌ 手势阻止功能启用失败';
        _isLoading = false;
      });

      if (result) {
        // 显示测试指导
        _showGestureTestDialog();
      }
    } catch (e) {
      setState(() {
        _lastTestResult = '❌ 测试失败: $e';
        _isLoading = false;
      });
    }
  }

  /// 显示手势测试指导对话框
  void _showGestureTestDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('手势阻止测试'),
        content: const Text(
          '手势阻止功能已启用！\n\n'
          '请尝试以下操作来测试效果：\n'
          '1. 从底部向上滑动（Home手势）\n'
          '2. 从左边缘向右滑动（Back手势）\n'
          '3. 从顶部向下滑动（通知栏）\n'
          '4. 按Home键或Back键\n\n'
          '如果这些操作被阻止，说明功能正常工作。'
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _disableGestureBlocking();
            },
            child: const Text('停止测试'),
          ),
        ],
      ),
    );
  }

  /// 禁用手势阻止
  Future<void> _disableGestureBlocking() async {
    try {
      await platform.invokeMethod('disableIntegratedFocusLock');
      setState(() {
        _lastTestResult = '✅ 手势阻止功能已禁用';
      });
    } catch (e) {
      setState(() {
        _lastTestResult = '❌ 禁用失败: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('权限测试'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 权限状态卡片
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '权限状态',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    if (_isLoading)
                      const Center(child: CircularProgressIndicator())
                    else ...[
                      _buildPermissionItem(
                        '悬浮窗权限',
                        _permissionStatus['hasOverlayPermission'] ?? false,
                        '用于创建覆盖层阻止手势',
                        isRequired: true,
                      ),
                      _buildPermissionItem(
                        '无障碍服务',
                        _permissionStatus['hasAccessibilityService'] ?? false,
                        '用于增强手势拦截效果',
                        isRequired: false,
                      ),
                      _buildPermissionItem(
                        '设备管理员',
                        _permissionStatus['hasDeviceAdmin'] ?? false,
                        '可选功能，用于系统级锁定',
                        isRequired: false,
                      ),
                    ],
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // 操作按钮
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _checkPermissionStatus,
                child: const Text('刷新权限状态'),
              ),
            ),
            
            const SizedBox(height: 12),
            
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _requestPermissions,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                ),
                child: const Text('申请权限'),
              ),
            ),
            
            const SizedBox(height: 12),
            
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _testGestureBlocking,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                ),
                child: const Text('测试手势阻止'),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // 测试结果
            if (_lastTestResult.isNotEmpty)
              Card(
                color: _lastTestResult.startsWith('✅') 
                  ? Colors.green.shade50 
                  : Colors.red.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '测试结果',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(_lastTestResult),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionItem(
    String name, 
    bool granted, 
    String description, 
    {bool isRequired = false}
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Icon(
            granted ? Icons.check_circle : Icons.cancel,
            color: granted ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      name,
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                    if (isRequired) ...[
                      const SizedBox(width: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Text(
                          '必需',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
