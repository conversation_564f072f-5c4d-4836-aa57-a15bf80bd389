import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'streamlined_permission_flow.dart';
import 'enhanced_basic_lock.dart';
import 'super_enhanced_lock.dart';
import 'focus_manager.dart' show LockLevel;

/// 统一锁定管理器
/// 根据权限状态自动选择最佳的锁定方案
class UnifiedLockManager {
  static UnifiedLockManager? _instance;
  static UnifiedLockManager get instance =>
      _instance ??= UnifiedLockManager._();

  UnifiedLockManager._();

  static const MethodChannel _channel = MethodChannel('yoyo_lock_screen');

  LockLevel? _currentLockLevel;
  bool _isLockActive = false;

  /// 启用锁定
  Future<LockResult> enableLock(
      BuildContext context, LockLevel requestedLevel) async {
    try {
      debugPrint('请求启用锁定级别: ${requestedLevel.name}');

      // 1. 检查权限并获取实际可用的锁定级别
      final permissionResult = await StreamlinedPermissionFlow.instance
          .requestPermissionsForLevel(context, requestedLevel);

      if (!permissionResult.success) {
        return LockResult(
          success: false,
          actualLevel: LockLevel.basic,
          message: permissionResult.message,
        );
      }

      var actualLevel = permissionResult.finalLevel;
      debugPrint('实际可用锁定级别: ${actualLevel.name}');

      // 2. 根据实际级别启用相应的锁定
      bool lockSuccess = false;
      String message = '';

      switch (actualLevel) {
        case LockLevel.basic:
          lockSuccess = await _enableBasicLock();
          message = lockSuccess ? '基础锁定已启用' : '基础锁定启用失败';
          break;

        case LockLevel.enhanced:
          // 优先尝试超级增强锁定
          lockSuccess = await _enableSuperEnhancedLock();
          if (lockSuccess) {
            message = '超级增强锁定已启用（无需无障碍服务）';
          } else {
            // 降级到普通增强锁定
            lockSuccess = await _enableEnhancedLock();
            message = lockSuccess ? '增强锁定已启用' : '增强锁定启用失败，降级到基础锁定';
            if (!lockSuccess) {
              lockSuccess = await _enableBasicLock();
              actualLevel = LockLevel.basic;
            }
          }
          break;

        case LockLevel.deep:
          lockSuccess = await _enableDeepLock();
          message = lockSuccess ? '深度锁定已启用' : '深度锁定启用失败，降级处理';
          if (!lockSuccess) {
            // 尝试增强锁定
            lockSuccess = await _enableEnhancedLock();
            if (lockSuccess) {
              actualLevel = LockLevel.enhanced;
              message = '深度锁定失败，已降级到增强锁定';
            } else {
              // 最后降级到基础锁定
              lockSuccess = await _enableBasicLock();
              actualLevel = LockLevel.basic;
              message = '深度锁定失败，已降级到基础锁定';
            }
          }
          break;
      }

      if (lockSuccess) {
        _currentLockLevel = actualLevel;
        _isLockActive = true;
      }

      return LockResult(
        success: lockSuccess,
        requestedLevel: requestedLevel,
        actualLevel: actualLevel,
        message: message,
      );
    } catch (e) {
      debugPrint('启用锁定失败: $e');
      return LockResult(
        success: false,
        actualLevel: LockLevel.basic,
        message: '锁定启用失败: $e',
      );
    }
  }

  /// 禁用锁定
  Future<void> disableLock() async {
    if (!_isLockActive || _currentLockLevel == null) return;

    debugPrint('禁用锁定级别: ${_currentLockLevel!.name}');

    try {
      switch (_currentLockLevel!) {
        case LockLevel.basic:
          await _disableBasicLock();
          break;
        case LockLevel.enhanced:
          // 检查是否使用了超级增强锁定
          if (SuperEnhancedLock.instance.isLockActive) {
            await _disableSuperEnhancedLock();
          } else {
            await _disableEnhancedLock();
          }
          break;
        case LockLevel.deep:
          await _disableDeepLock();
          break;
      }
    } catch (e) {
      debugPrint('禁用锁定失败: $e');
    } finally {
      _currentLockLevel = null;
      _isLockActive = false;
    }
  }

  /// 启用基础锁定
  Future<bool> _enableBasicLock() async {
    debugPrint('启用基础锁定');
    return await EnhancedBasicLock.instance.enableLock();
  }

  /// 启用增强锁定
  Future<bool> _enableEnhancedLock() async {
    debugPrint('启用增强锁定');
    try {
      // 首先启用基础锁定
      final basicSuccess = await _enableBasicLock();
      if (!basicSuccess) return false;

      // 然后启用悬浮窗锁定
      final result = await _channel.invokeMethod('enableLockScreen', {
        'level': 'enhanced',
      });

      return result as bool? ?? false;
    } catch (e) {
      debugPrint('启用增强锁定失败: $e');
      return false;
    }
  }

  /// 启用超级增强锁定（不需要无障碍服务）
  Future<bool> _enableSuperEnhancedLock() async {
    debugPrint('启用超级增强锁定');
    try {
      // 首先启用Flutter层的超级增强锁定
      final flutterSuccess = await SuperEnhancedLock.instance.enableLock();
      if (!flutterSuccess) {
        debugPrint('Flutter层超级增强锁定启用失败');
        return false;
      }

      // 然后启用原生层的超级增强锁定
      final result = await _channel.invokeMethod('enableSuperEnhancedLock');
      final nativeSuccess = result as bool? ?? false;

      if (!nativeSuccess) {
        debugPrint('原生层超级增强锁定启用失败，禁用Flutter层锁定');
        await SuperEnhancedLock.instance.disableLock();
        return false;
      }

      debugPrint('超级增强锁定启用成功');
      return true;
    } catch (e) {
      debugPrint('启用超级增强锁定失败: $e');
      // 确保清理Flutter层锁定
      try {
        await SuperEnhancedLock.instance.disableLock();
      } catch (cleanupError) {
        debugPrint('清理Flutter层锁定失败: $cleanupError');
      }
      return false;
    }
  }

  /// 启用深度锁定
  Future<bool> _enableDeepLock() async {
    debugPrint('启用深度锁定');
    try {
      // 首先启用增强锁定
      final enhancedSuccess = await _enableEnhancedLock();
      if (!enhancedSuccess) return false;

      // 然后启用无障碍服务锁定
      final result = await _channel.invokeMethod('enableLockScreen', {
        'level': 'deep',
      });

      return result as bool? ?? false;
    } catch (e) {
      debugPrint('启用深度锁定失败: $e');
      return false;
    }
  }

  /// 禁用基础锁定
  Future<void> _disableBasicLock() async {
    debugPrint('禁用基础锁定');
    await EnhancedBasicLock.instance.disableLock();
  }

  /// 禁用增强锁定
  Future<void> _disableEnhancedLock() async {
    debugPrint('禁用增强锁定');
    try {
      await _channel.invokeMethod('disableLockScreen');
      await _disableBasicLock();
    } catch (e) {
      debugPrint('禁用增强锁定失败: $e');
    }
  }

  /// 禁用超级增强锁定
  Future<void> _disableSuperEnhancedLock() async {
    debugPrint('禁用超级增强锁定');
    try {
      // 先禁用原生层锁定
      await _channel.invokeMethod('disableSuperEnhancedLock');

      // 再禁用Flutter层锁定
      await SuperEnhancedLock.instance.disableLock();

      debugPrint('超级增强锁定已禁用');
    } catch (e) {
      debugPrint('禁用超级增强锁定失败: $e');
    }
  }

  /// 禁用深度锁定
  Future<void> _disableDeepLock() async {
    debugPrint('禁用深度锁定');
    try {
      await _channel.invokeMethod('disableLockScreen');
      await _disableBasicLock();
    } catch (e) {
      debugPrint('禁用深度锁定失败: $e');
    }
  }

  /// 处理返回键
  bool handleBackPress() {
    if (!_isLockActive) return false;

    // 基础锁定和增强锁定都使用增强基础锁定的返回键处理
    if (_currentLockLevel == LockLevel.basic ||
        _currentLockLevel == LockLevel.enhanced) {
      return EnhancedBasicLock.instance.handleBackPress();
    }

    // 深度锁定完全阻止返回键
    if (_currentLockLevel == LockLevel.deep) {
      debugPrint('深度锁定模式，完全阻止返回键');
      return true;
    }

    return false;
  }

  /// 获取锁定状态信息
  LockStatusInfo getLockStatus() {
    return LockStatusInfo(
      isActive: _isLockActive,
      currentLevel: _currentLockLevel,
      backPressCount:
          _isLockActive ? EnhancedBasicLock.instance.backPressCount : 0,
      backPressThreshold: EnhancedBasicLock.instance.backPressThreshold,
    );
  }

  /// 重置返回键计数
  void resetBackPressCount() {
    EnhancedBasicLock.instance.resetBackPressCount();
  }

  /// 获取锁定级别描述
  String getLockLevelDescription(LockLevel level) {
    switch (level) {
      case LockLevel.basic:
        return '基础锁定 - 阻止返回键和应用切换，连续按返回键${EnhancedBasicLock.instance.backPressThreshold}次可退出';
      case LockLevel.enhanced:
        return '增强锁定 - 阻止手势操作和通知栏，连续按返回键${EnhancedBasicLock.instance.backPressThreshold}次可退出';
      case LockLevel.deep:
        return '深度锁定 - 完全阻止系统操作，只能等待时间结束或紧急退出';
    }
  }

  /// 获取锁定级别图标
  IconData getLockLevelIcon(LockLevel level) {
    switch (level) {
      case LockLevel.basic:
        return Icons.lock_outline;
      case LockLevel.enhanced:
        return Icons.lock;
      case LockLevel.deep:
        return Icons.security;
    }
  }

  /// 检查是否可以使用指定锁定级别
  Future<bool> canUseLockLevel(LockLevel level) async {
    final checkResult = await StreamlinedPermissionFlow.instance
        .checkLockLevelPermissions(level);
    return checkResult.canProceed;
  }
}

/// 锁定结果
class LockResult {
  final bool success;
  final LockLevel? requestedLevel;
  final LockLevel actualLevel;
  final String message;

  LockResult({
    required this.success,
    this.requestedLevel,
    required this.actualLevel,
    required this.message,
  });

  @override
  String toString() {
    return 'LockResult(success: $success, requested: ${requestedLevel?.name}, actual: ${actualLevel.name}, message: $message)';
  }
}

/// 锁定状态信息
class LockStatusInfo {
  final bool isActive;
  final LockLevel? currentLevel;
  final int backPressCount;
  final int backPressThreshold;

  LockStatusInfo({
    required this.isActive,
    this.currentLevel,
    required this.backPressCount,
    required this.backPressThreshold,
  });

  @override
  String toString() {
    return 'LockStatusInfo(active: $isActive, level: ${currentLevel?.name}, backPress: $backPressCount/$backPressThreshold)';
  }
}
