import 'package:flutter/foundation.dart';
import '../models/focus_session.dart';
import '../services/database_service.dart';

class StatisticsProvider extends ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService.instance;

  // Statistics data
  List<FocusSession> _allSessions = [];
  Map<String, dynamic> _statistics = {};
  bool _isLoading = false;

  // Getters
  List<FocusSession> get allSessions => _allSessions;
  Map<String, dynamic> get statistics => _statistics;
  bool get isLoading => _isLoading;

  // Quick access to common stats
  int get totalSessions => _statistics['totalSessions'] ?? 0;
  int get totalMinutes => _statistics['totalMinutes'] ?? 0;
  int get todaySessionCount => _statistics['todaySessionCount'] ?? 0;
  int get todayMinutes => _statistics['todayMinutes'] ?? 0;
  int get currentStreak => _statistics['currentStreak'] ?? 0;
  int get completionRate => _statistics['completionRate'] ?? 0;

  /// Load all statistics from database
  Future<void> loadStatistics() async {
    _isLoading = true;
    notifyListeners();

    try {
      _allSessions = await _databaseService.getFocusSessions();

      // 计算统计数据
      final totalSessions = await _databaseService.getTotalCompletedSessions();
      final totalMinutes = await _databaseService.getTotalFocusTime();
      final sessionsByType = await _databaseService.getSessionsByTaskType();

      _statistics = {
        'totalSessions': totalSessions,
        'totalMinutes': totalMinutes,
        'sessionsByType': sessionsByType,
        'todaySessionCount': 0, // TODO: 实现今日会话计算
        'todayMinutes': 0, // TODO: 实现今日时间计算
        'completionRate': 0, // TODO: 实现完成率计算
        'currentStreak': 0, // TODO: 实现连续天数计算
      };
    } catch (e) {
      debugPrint('加载统计数据失败: $e');
      // Initialize empty data on error
      _allSessions = [];
      _statistics = {
        'totalSessions': 0,
        'totalMinutes': 0,
        'todaySessionCount': 0,
        'todayMinutes': 0,
        'currentStreak': 0,
        'completionRate': 0,
      };
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Get sessions for a specific date
  List<FocusSession> getSessionsForDate(DateTime date) {
    final targetDate = DateTime(date.year, date.month, date.day);
    final nextDay = targetDate.add(const Duration(days: 1));

    return _allSessions.where((session) {
      return session.startTime.isAfter(targetDate) &&
          session.startTime.isBefore(nextDay);
    }).toList();
  }

  /// Get sessions for current week
  List<FocusSession> getThisWeekSessions() {
    final now = DateTime.now();
    final weekStart = now.subtract(Duration(days: now.weekday - 1));
    final weekStartDate =
        DateTime(weekStart.year, weekStart.month, weekStart.day);

    return _allSessions.where((session) {
      return session.startTime.isAfter(weekStartDate);
    }).toList();
  }

  /// Get sessions for current month
  List<FocusSession> getThisMonthSessions() {
    final now = DateTime.now();
    final monthStart = DateTime(now.year, now.month, 1);

    return _allSessions.where((session) {
      return session.startTime.isAfter(monthStart);
    }).toList();
  }

  /// Get completed sessions only
  List<FocusSession> get completedSessions {
    return _allSessions.where((session) => session.completed).toList();
  }

  /// Get escaped sessions only
  List<FocusSession> get escapedSessions {
    return _allSessions.where((session) => session.isEscaped).toList();
  }

  /// Get sessions by task type
  List<FocusSession> getSessionsByTaskType(String taskType) {
    return _allSessions
        .where((session) => session.taskType == taskType)
        .toList();
  }

  /// Get daily statistics for the past week
  List<Map<String, dynamic>> getWeeklyStats() {
    final now = DateTime.now();
    final weeklyStats = <Map<String, dynamic>>[];

    for (int i = 6; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final dayStart = DateTime(date.year, date.month, date.day);
      final dayEnd = dayStart.add(const Duration(days: 1));

      final daySessions = _allSessions.where((session) {
        return session.startTime.isAfter(dayStart) &&
            session.startTime.isBefore(dayEnd) &&
            session.completed;
      }).toList();

      final totalMinutes = daySessions.fold<int>(
        0,
        (sum, session) => sum + session.durationMinutes,
      );

      weeklyStats.add({
        'date': date,
        'sessionCount': daySessions.length,
        'totalMinutes': totalMinutes,
        'dayName': _getDayName(date.weekday),
      });
    }

    return weeklyStats;
  }

  /// Get task type distribution
  Map<String, int> getTaskTypeDistribution() {
    final distribution = <String, int>{};

    for (final session in completedSessions) {
      distribution[session.taskType] =
          (distribution[session.taskType] ?? 0) + 1;
    }

    return distribution;
  }

  /// Get average session duration
  double get averageSessionDuration {
    if (completedSessions.isEmpty) return 0.0;

    final totalMinutes = completedSessions.fold<int>(
      0,
      (sum, session) => sum + session.durationMinutes,
    );

    return totalMinutes / completedSessions.length;
  }

  /// Get longest session
  FocusSession? get longestSession {
    if (completedSessions.isEmpty) return null;

    return completedSessions
        .reduce((a, b) => a.durationMinutes > b.durationMinutes ? a : b);
  }

  /// Get most productive day of week
  String get mostProductiveDay {
    final dayStats = <int, int>{};

    for (final session in completedSessions) {
      final weekday = session.startTime.weekday;
      dayStats[weekday] = (dayStats[weekday] ?? 0) + session.durationMinutes;
    }

    if (dayStats.isEmpty) return '暂无数据';

    final mostProductiveWeekday =
        dayStats.entries.reduce((a, b) => a.value > b.value ? a : b).key;

    return _getDayName(mostProductiveWeekday);
  }

  /// Get focus time by hour of day
  Map<int, int> getFocusTimeByHour() {
    final hourStats = <int, int>{};

    for (final session in completedSessions) {
      final hour = session.startTime.hour;
      hourStats[hour] = (hourStats[hour] ?? 0) + session.durationMinutes;
    }

    return hourStats;
  }

  /// Check if user has achieved any new milestones
  List<String> checkNewAchievements() {
    final achievements = <String>[];

    // First session
    if (totalSessions == 1) {
      achievements.add('首次专注');
    }

    // Session count milestones
    if ([5, 10, 25, 50, 100].contains(totalSessions)) {
      achievements.add('专注达人 - $totalSessions次专注');
    }

    // Time milestones (in hours)
    final totalHours = totalMinutes ~/ 60;
    if ([1, 5, 10, 25, 50, 100].contains(totalHours)) {
      achievements.add('时间大师 - $totalHours小时专注');
    }

    // Streak milestones
    if ([3, 7, 14, 30].contains(currentStreak)) {
      achievements.add('坚持不懈 - 连续$currentStreak天');
    }

    // Long session achievements
    final longestDuration = longestSession?.durationMinutes ?? 0;
    if ([45, 90, 120, 180].contains(longestDuration)) {
      achievements.add('深度专注 - $longestDuration分钟专注');
    }

    return achievements;
  }

  String _getDayName(int weekday) {
    const dayNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    return dayNames[weekday - 1];
  }

  /// Refresh statistics (call after new session is added)
  Future<void> refresh() async {
    await loadStatistics();
  }
}
