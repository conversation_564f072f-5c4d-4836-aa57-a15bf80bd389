package com.example.lockphone

import android.app.Activity
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.util.Log
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

/**
 * 厂商权限管理器
 * 处理各厂商特有的权限申请流程
 * 
 * 主要功能：
 * 1. 检测和申请厂商特定权限
 * 2. 提供权限申请引导
 * 3. 处理权限申请结果
 * 4. 提供降级方案
 */
class VendorPermissionManager(
    private val activity: Activity,
    private val vendorROMAdapter: VendorROMAdapter
) {
    
    companion object {
        private const val TAG = "VendorPermissionManager"
        private const val REQUEST_CODE_BASE = 10000
    }
    
    private val romInfo = vendorROMAdapter.detectROMType()
    private val lockStrategy = vendorROMAdapter.createOptimizedLockStrategy()
    
    /**
     * 获取所需的厂商权限列表
     */
    fun getRequiredVendorPermissions(): List<PermissionRequestInfo> {
        Log.d(TAG, "📋 获取${romInfo.vendor}所需的厂商权限列表")
        
        return when (romInfo.vendor) {
            ROMVendor.OPPO -> getOPPORequiredPermissions()
            ROMVendor.XIAOMI -> getXiaomiRequiredPermissions()
            ROMVendor.HUAWEI -> getHuaweiRequiredPermissions()
            ROMVendor.SAMSUNG -> getSamsungRequiredPermissions()
            ROMVendor.VIVO -> getVivoRequiredPermissions()
            ROMVendor.REALME -> getRealmeRequiredPermissions()
            else -> getStandardRequiredPermissions()
        }
    }
    
    /**
     * 检查权限状态
     */
    fun checkPermissionStatus(permissionType: VendorPermissionType): PermissionRequestResult {
        return when (permissionType) {
            VendorPermissionType.AUTO_START -> checkAutoStartPermission()
            VendorPermissionType.FLOATING_WINDOW -> checkFloatingWindowPermission()
            VendorPermissionType.BACKGROUND_RUN -> checkBackgroundRunPermission()
            VendorPermissionType.NOTIFICATION -> checkNotificationPermission()
            VendorPermissionType.DEVICE_ADMIN -> checkDeviceAdminPermission()
            VendorPermissionType.ACCESSIBILITY -> checkAccessibilityPermission()
            VendorPermissionType.BATTERY_OPTIMIZATION -> checkBatteryOptimizationPermission()
            VendorPermissionType.APP_OPS -> checkAppOpsPermission()
            VendorPermissionType.VENDOR_SPECIFIC -> checkVendorSpecificPermission()
        }
    }
    
    /**
     * 申请厂商特定权限
     */
    fun requestVendorSpecificPermissions(): Boolean {
        Log.d(TAG, "🔐 申请${romInfo.vendor}特定权限")
        
        return when (romInfo.vendor) {
            ROMVendor.OPPO -> requestOPPOPermissions()
            ROMVendor.XIAOMI -> requestXiaomiPermissions()
            ROMVendor.HUAWEI -> requestHuaweiPermissions()
            ROMVendor.SAMSUNG -> requestSamsungPermissions()
            ROMVendor.VIVO -> requestVivoPermissions()
            ROMVendor.REALME -> requestRealmePermissions()
            else -> requestStandardPermissions()
        }
    }
    
    /**
     * 获取OPPO所需权限
     */
    private fun getOPPORequiredPermissions(): List<PermissionRequestInfo> {
        return listOf(
            PermissionRequestInfo(
                type = VendorPermissionType.AUTO_START,
                permission = "oppo.permission.OPPO_COMPONENT_SAFE",
                displayName = "自启动管理",
                description = "允许应用在后台自动启动，确保专注模式正常工作",
                isRequired = true,
                requestMethod = PermissionRequestMethod.SETTINGS_INTENT,
                settingsIntent = "com.coloros.safecenter/.startupapp.StartupAppListActivity"
            ),
            PermissionRequestInfo(
                type = VendorPermissionType.FLOATING_WINDOW,
                permission = "android.permission.SYSTEM_ALERT_WINDOW",
                displayName = "悬浮窗权限",
                description = "允许应用显示悬浮窗，用于专注界面覆盖",
                isRequired = true,
                requestMethod = PermissionRequestMethod.STANDARD_REQUEST
            ),
            PermissionRequestInfo(
                type = VendorPermissionType.BACKGROUND_RUN,
                permission = "com.coloros.safecenter.permission.startup",
                displayName = "后台运行",
                description = "允许应用在后台持续运行，维持专注锁定状态",
                isRequired = true,
                requestMethod = PermissionRequestMethod.SETTINGS_INTENT,
                settingsIntent = "com.coloros.safecenter/.permission.startup.FakeActivity"
            ),
            PermissionRequestInfo(
                type = VendorPermissionType.BATTERY_OPTIMIZATION,
                permission = "android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS",
                displayName = "电池优化白名单",
                description = "将应用加入电池优化白名单，防止被系统杀死",
                isRequired = false,
                requestMethod = PermissionRequestMethod.STANDARD_REQUEST
            )
        )
    }
    
    /**
     * 获取小米所需权限
     */
    private fun getXiaomiRequiredPermissions(): List<PermissionRequestInfo> {
        return listOf(
            PermissionRequestInfo(
                type = VendorPermissionType.AUTO_START,
                permission = "miui.permission.USE_INTERNAL_GENERAL_API",
                displayName = "自启动管理",
                description = "允许应用自动启动，确保专注功能正常",
                isRequired = true,
                requestMethod = PermissionRequestMethod.SETTINGS_INTENT,
                settingsIntent = "com.miui.securitycenter/com.miui.permcenter.autostart.AutoStartManagementActivity"
            ),
            PermissionRequestInfo(
                type = VendorPermissionType.FLOATING_WINDOW,
                permission = "android.permission.SYSTEM_ALERT_WINDOW",
                displayName = "悬浮窗权限",
                description = "允许应用显示悬浮窗",
                isRequired = true,
                requestMethod = PermissionRequestMethod.STANDARD_REQUEST
            ),
            PermissionRequestInfo(
                type = VendorPermissionType.BACKGROUND_RUN,
                permission = "miui.permission.BACKGROUND_START_ACTIVITY",
                displayName = "后台弹出界面",
                description = "允许应用在后台弹出界面",
                isRequired = true,
                requestMethod = PermissionRequestMethod.SETTINGS_INTENT,
                settingsIntent = "com.miui.securitycenter/com.miui.permcenter.permissions.PermissionsEditorActivity"
            ),
            PermissionRequestInfo(
                type = VendorPermissionType.NOTIFICATION,
                permission = "android.permission.POST_NOTIFICATIONS",
                displayName = "通知权限",
                description = "允许应用发送通知",
                isRequired = false,
                requestMethod = PermissionRequestMethod.STANDARD_REQUEST
            )
        )
    }
    
    /**
     * 获取华为所需权限
     */
    private fun getHuaweiRequiredPermissions(): List<PermissionRequestInfo> {
        return listOf(
            PermissionRequestInfo(
                type = VendorPermissionType.AUTO_START,
                permission = "com.huawei.permission.external_app_settings.USE_COMPONENT",
                displayName = "自启动管理",
                description = "允许应用自动启动",
                isRequired = true,
                requestMethod = PermissionRequestMethod.SETTINGS_INTENT,
                settingsIntent = "com.huawei.systemmanager/.startupmgr.ui.StartupNormalAppListActivity"
            ),
            PermissionRequestInfo(
                type = VendorPermissionType.FLOATING_WINDOW,
                permission = "android.permission.SYSTEM_ALERT_WINDOW",
                displayName = "悬浮窗权限",
                description = "允许应用显示悬浮窗",
                isRequired = true,
                requestMethod = PermissionRequestMethod.STANDARD_REQUEST
            ),
            PermissionRequestInfo(
                type = VendorPermissionType.BATTERY_OPTIMIZATION,
                permission = "com.huawei.android.launcher.permission.CHANGE_BADGE",
                displayName = "电池优化",
                description = "防止应用被电池优化杀死",
                isRequired = true,
                requestMethod = PermissionRequestMethod.SETTINGS_INTENT,
                settingsIntent = "com.huawei.systemmanager/.power.ui.HwPowerManagerActivity"
            )
        )
    }
    
    /**
     * 获取三星所需权限
     */
    private fun getSamsungRequiredPermissions(): List<PermissionRequestInfo> {
        return listOf(
            PermissionRequestInfo(
                type = VendorPermissionType.FLOATING_WINDOW,
                permission = "android.permission.SYSTEM_ALERT_WINDOW",
                displayName = "悬浮窗权限",
                description = "允许应用显示悬浮窗",
                isRequired = true,
                requestMethod = PermissionRequestMethod.STANDARD_REQUEST
            ),
            PermissionRequestInfo(
                type = VendorPermissionType.BATTERY_OPTIMIZATION,
                permission = "android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS",
                displayName = "电池优化白名单",
                description = "将应用加入电池优化白名单",
                isRequired = false,
                requestMethod = PermissionRequestMethod.STANDARD_REQUEST
            )
        )
    }
    
    /**
     * 获取Vivo所需权限
     */
    private fun getVivoRequiredPermissions(): List<PermissionRequestInfo> {
        return listOf(
            PermissionRequestInfo(
                type = VendorPermissionType.AUTO_START,
                permission = "com.vivo.permissionmanager.permission.ACCESS_PERMISSION_MANAGER",
                displayName = "自启动管理",
                description = "允许应用自动启动",
                isRequired = true,
                requestMethod = PermissionRequestMethod.SETTINGS_INTENT,
                settingsIntent = "com.vivo.permissionmanager/.activity.BgStartUpManagerActivity"
            ),
            PermissionRequestInfo(
                type = VendorPermissionType.FLOATING_WINDOW,
                permission = "android.permission.SYSTEM_ALERT_WINDOW",
                displayName = "悬浮窗权限",
                description = "允许应用显示悬浮窗",
                isRequired = true,
                requestMethod = PermissionRequestMethod.STANDARD_REQUEST
            )
        )
    }
    
    /**
     * 获取Realme所需权限
     */
    private fun getRealmeRequiredPermissions(): List<PermissionRequestInfo> {
        return listOf(
            PermissionRequestInfo(
                type = VendorPermissionType.FLOATING_WINDOW,
                permission = "android.permission.SYSTEM_ALERT_WINDOW",
                displayName = "悬浮窗权限",
                description = "允许应用显示悬浮窗",
                isRequired = true,
                requestMethod = PermissionRequestMethod.STANDARD_REQUEST
            ),
            PermissionRequestInfo(
                type = VendorPermissionType.BATTERY_OPTIMIZATION,
                permission = "android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS",
                displayName = "电池优化白名单",
                description = "将应用加入电池优化白名单",
                isRequired = false,
                requestMethod = PermissionRequestMethod.STANDARD_REQUEST
            )
        )
    }
    
    /**
     * 获取标准Android所需权限
     */
    private fun getStandardRequiredPermissions(): List<PermissionRequestInfo> {
        return listOf(
            PermissionRequestInfo(
                type = VendorPermissionType.FLOATING_WINDOW,
                permission = "android.permission.SYSTEM_ALERT_WINDOW",
                displayName = "悬浮窗权限",
                description = "允许应用显示悬浮窗",
                isRequired = true,
                requestMethod = PermissionRequestMethod.STANDARD_REQUEST
            ),
            PermissionRequestInfo(
                type = VendorPermissionType.DEVICE_ADMIN,
                permission = "android.permission.BIND_DEVICE_ADMIN",
                displayName = "设备管理员权限",
                description = "允许应用作为设备管理员",
                isRequired = true,
                requestMethod = PermissionRequestMethod.SETTINGS_INTENT
            ),
            PermissionRequestInfo(
                type = VendorPermissionType.ACCESSIBILITY,
                permission = "android.permission.BIND_ACCESSIBILITY_SERVICE",
                displayName = "无障碍服务权限",
                description = "允许应用使用无障碍服务",
                isRequired = true,
                requestMethod = PermissionRequestMethod.SETTINGS_INTENT
            )
        )
    }

    /**
     * 检查自启动权限
     */
    private fun checkAutoStartPermission(): PermissionRequestResult {
        return when (romInfo.vendor) {
            ROMVendor.OPPO -> checkOPPOAutoStartPermission()
            ROMVendor.XIAOMI -> checkXiaomiAutoStartPermission()
            ROMVendor.HUAWEI -> checkHuaweiAutoStartPermission()
            ROMVendor.VIVO -> checkVivoAutoStartPermission()
            else -> PermissionRequestResult.NOT_SUPPORTED
        }
    }

    /**
     * 检查悬浮窗权限
     */
    private fun checkFloatingWindowPermission(): PermissionRequestResult {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (Settings.canDrawOverlays(activity)) {
                PermissionRequestResult.GRANTED
            } else {
                PermissionRequestResult.DENIED
            }
        } else {
            PermissionRequestResult.GRANTED
        }
    }

    /**
     * 检查后台运行权限
     */
    private fun checkBackgroundRunPermission(): PermissionRequestResult {
        return when (romInfo.vendor) {
            ROMVendor.OPPO -> checkOPPOBackgroundRunPermission()
            ROMVendor.XIAOMI -> checkXiaomiBackgroundRunPermission()
            ROMVendor.HUAWEI -> checkHuaweiBackgroundRunPermission()
            else -> PermissionRequestResult.GRANTED
        }
    }

    /**
     * 检查通知权限
     */
    private fun checkNotificationPermission(): PermissionRequestResult {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            val permission = android.Manifest.permission.POST_NOTIFICATIONS
            if (ContextCompat.checkSelfPermission(activity, permission) == PackageManager.PERMISSION_GRANTED) {
                PermissionRequestResult.GRANTED
            } else {
                PermissionRequestResult.DENIED
            }
        } else {
            PermissionRequestResult.GRANTED
        }
    }

    /**
     * 检查设备管理员权限
     */
    private fun checkDeviceAdminPermission(): PermissionRequestResult {
        val deviceAdminKioskManager = DeviceAdminKioskManager(activity)
        return if (deviceAdminKioskManager.isDeviceAdminActive()) {
            PermissionRequestResult.GRANTED
        } else {
            PermissionRequestResult.DENIED
        }
    }

    /**
     * 检查无障碍权限
     */
    private fun checkAccessibilityPermission(): PermissionRequestResult {
        return if (YoYoAccessibilityService.getInstance() != null) {
            PermissionRequestResult.GRANTED
        } else {
            PermissionRequestResult.DENIED
        }
    }

    /**
     * 检查电池优化权限
     */
    private fun checkBatteryOptimizationPermission(): PermissionRequestResult {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val powerManager = activity.getSystemService(Context.POWER_SERVICE) as android.os.PowerManager
            if (powerManager.isIgnoringBatteryOptimizations(activity.packageName)) {
                PermissionRequestResult.GRANTED
            } else {
                PermissionRequestResult.DENIED
            }
        } else {
            PermissionRequestResult.GRANTED
        }
    }

    /**
     * 检查应用操作权限
     */
    private fun checkAppOpsPermission(): PermissionRequestResult {
        // 这里可以检查具体的AppOps权限
        return PermissionRequestResult.GRANTED
    }

    /**
     * 检查厂商特定权限
     */
    private fun checkVendorSpecificPermission(): PermissionRequestResult {
        return when (romInfo.vendor) {
            ROMVendor.OPPO -> checkOPPOSpecificPermissions()
            ROMVendor.XIAOMI -> checkXiaomiSpecificPermissions()
            ROMVendor.HUAWEI -> checkHuaweiSpecificPermissions()
            else -> PermissionRequestResult.GRANTED
        }
    }

    /**
     * 检查OPPO自启动权限
     */
    private fun checkOPPOAutoStartPermission(): PermissionRequestResult {
        return try {
            // OPPO的自启动权限检查比较复杂，通常需要通过Intent跳转到设置页面
            PermissionRequestResult.NEED_MANUAL
        } catch (e: Exception) {
            Log.e(TAG, "检查OPPO自启动权限失败: ${e.message}")
            PermissionRequestResult.ERROR
        }
    }

    /**
     * 检查小米自启动权限
     */
    private fun checkXiaomiAutoStartPermission(): PermissionRequestResult {
        return try {
            // 小米的自启动权限检查
            PermissionRequestResult.NEED_MANUAL
        } catch (e: Exception) {
            Log.e(TAG, "检查小米自启动权限失败: ${e.message}")
            PermissionRequestResult.ERROR
        }
    }

    /**
     * 检查华为自启动权限
     */
    private fun checkHuaweiAutoStartPermission(): PermissionRequestResult {
        return try {
            // 华为的自启动权限检查
            PermissionRequestResult.NEED_MANUAL
        } catch (e: Exception) {
            Log.e(TAG, "检查华为自启动权限失败: ${e.message}")
            PermissionRequestResult.ERROR
        }
    }

    /**
     * 检查Vivo自启动权限
     */
    private fun checkVivoAutoStartPermission(): PermissionRequestResult {
        return try {
            // Vivo的自启动权限检查
            PermissionRequestResult.NEED_MANUAL
        } catch (e: Exception) {
            Log.e(TAG, "检查Vivo自启动权限失败: ${e.message}")
            PermissionRequestResult.ERROR
        }
    }

    /**
     * 申请OPPO权限
     */
    private fun requestOPPOPermissions(): Boolean {
        return try {
            Log.d(TAG, "🔐 申请OPPO ColorOS权限")

            var allGranted = true

            // 1. 申请悬浮窗权限
            if (checkFloatingWindowPermission() != PermissionRequestResult.GRANTED) {
                requestFloatingWindowPermission()
                allGranted = false
            }

            // 2. 引导用户设置自启动权限
            if (checkAutoStartPermission() != PermissionRequestResult.GRANTED) {
                requestOPPOAutoStartPermission()
                allGranted = false
            }

            // 3. 引导用户设置后台运行权限
            if (checkBackgroundRunPermission() != PermissionRequestResult.GRANTED) {
                requestOPPOBackgroundRunPermission()
                allGranted = false
            }

            // 4. 申请电池优化白名单
            if (checkBatteryOptimizationPermission() != PermissionRequestResult.GRANTED) {
                requestBatteryOptimizationPermission()
            }

            allGranted
        } catch (e: Exception) {
            Log.e(TAG, "申请OPPO权限失败: ${e.message}")
            false
        }
    }

    /**
     * 申请小米权限
     */
    private fun requestXiaomiPermissions(): Boolean {
        return try {
            Log.d(TAG, "🔐 申请小米MIUI权限")

            var allGranted = true

            // 1. 申请悬浮窗权限
            if (checkFloatingWindowPermission() != PermissionRequestResult.GRANTED) {
                requestFloatingWindowPermission()
                allGranted = false
            }

            // 2. 引导用户设置自启动权限
            if (checkAutoStartPermission() != PermissionRequestResult.GRANTED) {
                requestXiaomiAutoStartPermission()
                allGranted = false
            }

            // 3. 引导用户设置后台弹出界面权限
            if (checkBackgroundRunPermission() != PermissionRequestResult.GRANTED) {
                requestXiaomiBackgroundRunPermission()
                allGranted = false
            }

            // 4. 申请通知权限
            if (checkNotificationPermission() != PermissionRequestResult.GRANTED) {
                requestNotificationPermission()
            }

            allGranted
        } catch (e: Exception) {
            Log.e(TAG, "申请小米权限失败: ${e.message}")
            false
        }
    }

    /**
     * 申请华为权限
     */
    private fun requestHuaweiPermissions(): Boolean {
        return try {
            Log.d(TAG, "🔐 申请华为EMUI/HarmonyOS权限")

            var allGranted = true

            // 1. 申请悬浮窗权限
            if (checkFloatingWindowPermission() != PermissionRequestResult.GRANTED) {
                requestFloatingWindowPermission()
                allGranted = false
            }

            // 2. 引导用户设置自启动权限
            if (checkAutoStartPermission() != PermissionRequestResult.GRANTED) {
                requestHuaweiAutoStartPermission()
                allGranted = false
            }

            // 3. 引导用户设置电池优化
            if (checkBatteryOptimizationPermission() != PermissionRequestResult.GRANTED) {
                requestHuaweiBatteryOptimization()
                allGranted = false
            }

            allGranted
        } catch (e: Exception) {
            Log.e(TAG, "申请华为权限失败: ${e.message}")
            false
        }
    }

    /**
     * 申请三星权限
     */
    private fun requestSamsungPermissions(): Boolean {
        return try {
            Log.d(TAG, "🔐 申请三星OneUI权限")

            var allGranted = true

            // 1. 申请悬浮窗权限
            if (checkFloatingWindowPermission() != PermissionRequestResult.GRANTED) {
                requestFloatingWindowPermission()
                allGranted = false
            }

            // 2. 申请电池优化白名单
            if (checkBatteryOptimizationPermission() != PermissionRequestResult.GRANTED) {
                requestBatteryOptimizationPermission()
            }

            allGranted
        } catch (e: Exception) {
            Log.e(TAG, "申请三星权限失败: ${e.message}")
            false
        }
    }

    /**
     * 申请Vivo权限
     */
    private fun requestVivoPermissions(): Boolean {
        return try {
            Log.d(TAG, "🔐 申请Vivo Funtouch权限")

            var allGranted = true

            // 1. 申请悬浮窗权限
            if (checkFloatingWindowPermission() != PermissionRequestResult.GRANTED) {
                requestFloatingWindowPermission()
                allGranted = false
            }

            // 2. 引导用户设置自启动权限
            if (checkAutoStartPermission() != PermissionRequestResult.GRANTED) {
                requestVivoAutoStartPermission()
                allGranted = false
            }

            allGranted
        } catch (e: Exception) {
            Log.e(TAG, "申请Vivo权限失败: ${e.message}")
            false
        }
    }

    /**
     * 申请Realme权限
     */
    private fun requestRealmePermissions(): Boolean {
        return try {
            Log.d(TAG, "🔐 申请Realme UI权限")

            var allGranted = true

            // 1. 申请悬浮窗权限
            if (checkFloatingWindowPermission() != PermissionRequestResult.GRANTED) {
                requestFloatingWindowPermission()
                allGranted = false
            }

            // 2. 申请电池优化白名单
            if (checkBatteryOptimizationPermission() != PermissionRequestResult.GRANTED) {
                requestBatteryOptimizationPermission()
            }

            allGranted
        } catch (e: Exception) {
            Log.e(TAG, "申请Realme权限失败: ${e.message}")
            false
        }
    }

    /**
     * 申请标准权限
     */
    private fun requestStandardPermissions(): Boolean {
        return try {
            Log.d(TAG, "🔐 申请标准Android权限")

            var allGranted = true

            // 1. 申请悬浮窗权限
            if (checkFloatingWindowPermission() != PermissionRequestResult.GRANTED) {
                requestFloatingWindowPermission()
                allGranted = false
            }

            // 2. 申请设备管理员权限
            if (checkDeviceAdminPermission() != PermissionRequestResult.GRANTED) {
                requestDeviceAdminPermission()
                allGranted = false
            }

            // 3. 申请无障碍权限
            if (checkAccessibilityPermission() != PermissionRequestResult.GRANTED) {
                requestAccessibilityPermission()
                allGranted = false
            }

            allGranted
        } catch (e: Exception) {
            Log.e(TAG, "申请标准权限失败: ${e.message}")
            false
        }
    }

    /**
     * 申请悬浮窗权限
     */
    private fun requestFloatingWindowPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION)
            intent.data = Uri.parse("package:${activity.packageName}")
            activity.startActivityForResult(intent, REQUEST_CODE_BASE + 1)
        }
    }

    /**
     * 申请通知权限
     */
    private fun requestNotificationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ActivityCompat.requestPermissions(
                activity,
                arrayOf(android.Manifest.permission.POST_NOTIFICATIONS),
                REQUEST_CODE_BASE + 2
            )
        }
    }

    /**
     * 申请设备管理员权限
     */
    private fun requestDeviceAdminPermission() {
        val deviceAdminKioskManager = DeviceAdminKioskManager(activity)
        deviceAdminKioskManager.requestDeviceAdminPermission()
    }

    /**
     * 申请无障碍权限
     */
    private fun requestAccessibilityPermission() {
        val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
        activity.startActivity(intent)
    }

    /**
     * 申请电池优化白名单
     */
    private fun requestBatteryOptimizationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)
            intent.data = Uri.parse("package:${activity.packageName}")
            activity.startActivityForResult(intent, REQUEST_CODE_BASE + 3)
        }
    }

    /**
     * 申请OPPO自启动权限
     */
    private fun requestOPPOAutoStartPermission() {
        try {
            val intent = Intent()
            intent.setClassName(
                "com.coloros.safecenter",
                "com.coloros.safecenter.startupapp.StartupAppListActivity"
            )
            activity.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "跳转OPPO自启动设置失败: ${e.message}")
            // 备用方案：跳转到应用详情页
            openAppDetailsSettings()
        }
    }

    /**
     * 申请OPPO后台运行权限
     */
    private fun requestOPPOBackgroundRunPermission() {
        try {
            val intent = Intent()
            intent.setClassName(
                "com.coloros.safecenter",
                "com.coloros.safecenter.permission.startup.FakeActivity"
            )
            activity.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "跳转OPPO后台运行设置失败: ${e.message}")
            openAppDetailsSettings()
        }
    }

    /**
     * 申请小米自启动权限
     */
    private fun requestXiaomiAutoStartPermission() {
        try {
            val intent = Intent()
            intent.setClassName(
                "com.miui.securitycenter",
                "com.miui.permcenter.autostart.AutoStartManagementActivity"
            )
            activity.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "跳转小米自启动设置失败: ${e.message}")
            openAppDetailsSettings()
        }
    }

    /**
     * 申请小米后台运行权限
     */
    private fun requestXiaomiBackgroundRunPermission() {
        try {
            val intent = Intent()
            intent.setClassName(
                "com.miui.securitycenter",
                "com.miui.permcenter.permissions.PermissionsEditorActivity"
            )
            intent.putExtra("extra_pkgname", activity.packageName)
            activity.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "跳转小米后台弹出界面设置失败: ${e.message}")
            openAppDetailsSettings()
        }
    }

    /**
     * 申请华为自启动权限
     */
    private fun requestHuaweiAutoStartPermission() {
        try {
            val intent = Intent()
            intent.setClassName(
                "com.huawei.systemmanager",
                "com.huawei.systemmanager.startupmgr.ui.StartupNormalAppListActivity"
            )
            activity.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "跳转华为自启动设置失败: ${e.message}")
            openAppDetailsSettings()
        }
    }

    /**
     * 申请华为电池优化
     */
    private fun requestHuaweiBatteryOptimization() {
        try {
            val intent = Intent()
            intent.setClassName(
                "com.huawei.systemmanager",
                "com.huawei.systemmanager.power.ui.HwPowerManagerActivity"
            )
            activity.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "跳转华为电池优化设置失败: ${e.message}")
            requestBatteryOptimizationPermission()
        }
    }

    /**
     * 申请Vivo自启动权限
     */
    private fun requestVivoAutoStartPermission() {
        try {
            val intent = Intent()
            intent.setClassName(
                "com.vivo.permissionmanager",
                "com.vivo.permissionmanager.activity.BgStartUpManagerActivity"
            )
            activity.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "跳转Vivo自启动设置失败: ${e.message}")
            openAppDetailsSettings()
        }
    }

    /**
     * 打开应用详情设置页面（备用方案）
     */
    private fun openAppDetailsSettings() {
        try {
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
            intent.data = Uri.parse("package:${activity.packageName}")
            activity.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "打开应用详情设置失败: ${e.message}")
        }
    }

    /**
     * 检查厂商特定权限的具体实现
     */
    private fun checkOPPOBackgroundRunPermission(): PermissionRequestResult {
        // OPPO后台运行权限检查逻辑
        return PermissionRequestResult.NEED_MANUAL
    }

    private fun checkXiaomiBackgroundRunPermission(): PermissionRequestResult {
        // 小米后台弹出界面权限检查逻辑
        return PermissionRequestResult.NEED_MANUAL
    }

    private fun checkHuaweiBackgroundRunPermission(): PermissionRequestResult {
        // 华为后台运行权限检查逻辑
        return PermissionRequestResult.NEED_MANUAL
    }

    private fun checkOPPOSpecificPermissions(): PermissionRequestResult {
        // OPPO特定权限检查
        return PermissionRequestResult.GRANTED
    }

    private fun checkXiaomiSpecificPermissions(): PermissionRequestResult {
        // 小米特定权限检查
        return PermissionRequestResult.GRANTED
    }

    private fun checkHuaweiSpecificPermissions(): PermissionRequestResult {
        // 华为特定权限检查
        return PermissionRequestResult.GRANTED
    }

    /**
     * 获取厂商适配状态
     */
    fun getVendorAdaptationStatus(): VendorAdaptationStatus {
        val requiredPermissions = getRequiredVendorPermissions()
        val grantedPermissions = mutableListOf<VendorPermissionType>()
        val missingPermissions = mutableListOf<VendorPermissionType>()

        // 检查每个权限的状态
        for (permission in requiredPermissions) {
            val status = checkPermissionStatus(permission.type)
            if (status == PermissionRequestResult.GRANTED) {
                grantedPermissions.add(permission.type)
            } else {
                missingPermissions.add(permission.type)
            }
        }

        // 计算适配级别
        val adaptationLevel = calculateAdaptationLevel(grantedPermissions.size, requiredPermissions.size)

        // 生成建议
        val recommendations = generateRecommendations(missingPermissions)

        return VendorAdaptationStatus(
            romInfo = romInfo,
            lockStrategy = lockStrategy,
            requiredPermissions = requiredPermissions,
            grantedPermissions = grantedPermissions,
            missingPermissions = missingPermissions,
            adaptationLevel = adaptationLevel,
            recommendations = recommendations
        )
    }

    /**
     * 计算适配级别
     */
    private fun calculateAdaptationLevel(grantedCount: Int, totalCount: Int): AdaptationLevel {
        val percentage = if (totalCount > 0) (grantedCount.toFloat() / totalCount) else 0f

        return when {
            percentage >= 0.9f -> AdaptationLevel.FULL
            percentage >= 0.7f -> AdaptationLevel.PARTIAL
            percentage >= 0.5f -> AdaptationLevel.BASIC
            else -> AdaptationLevel.LIMITED
        }
    }

    /**
     * 生成权限建议
     */
    private fun generateRecommendations(missingPermissions: List<VendorPermissionType>): List<String> {
        val recommendations = mutableListOf<String>()

        for (permission in missingPermissions) {
            when (permission) {
                VendorPermissionType.AUTO_START -> {
                    recommendations.add("建议开启自启动权限，确保专注模式能够正常启动")
                }
                VendorPermissionType.FLOATING_WINDOW -> {
                    recommendations.add("必须开启悬浮窗权限，这是专注界面显示的基础")
                }
                VendorPermissionType.BACKGROUND_RUN -> {
                    recommendations.add("建议开启后台运行权限，防止专注模式被系统杀死")
                }
                VendorPermissionType.BATTERY_OPTIMIZATION -> {
                    recommendations.add("建议将应用加入电池优化白名单，提高稳定性")
                }
                else -> {
                    recommendations.add("建议开启${permission.name}权限，提升专注模式效果")
                }
            }
        }

        return recommendations
    }
}
