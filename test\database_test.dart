import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:lockphone/services/database_service.dart';
import 'package:lockphone/models/focus_session.dart';
import 'package:lockphone/models/user_settings.dart';

void main() {
  group('Database Service Tests', () {
    late DatabaseService databaseService;

    setUpAll(() {
      // Initialize FFI for testing
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    });

    setUp(() async {
      databaseService = DatabaseService.instance;
      await databaseService.init();
    });

    tearDown(() async {
      await databaseService.clearAllData();
    });

    test('should initialize database with default settings', () async {
      final setting =
          await databaseService.getSetting(AppSettings.hasCompletedOnboarding);
      expect(setting, 'false');

      final defaultDuration =
          await databaseService.getSetting(AppSettings.defaultFocusDuration);
      expect(defaultDuration, AppSettings.defaultFocusDurationValue.toString());
    });

    test('should insert and retrieve focus session', () async {
      final session = FocusSession(
        id: '1',
        taskType: '深度工作',
        durationMinutes: 25,
        completed: true,
        startTime: DateTime.now(),
        endTime: DateTime.now().add(const Duration(minutes: 25)),
      );

      final id = await databaseService.insertFocusSession(session);
      expect(id, greaterThan(0));

      final sessions = await databaseService.getFocusSessions();
      expect(sessions.length, 1);
      expect(sessions.first.taskType, '深度工作');
      expect(sessions.first.durationMinutes, 25);
      expect(sessions.first.completed, true);
    });

    test('should update user settings', () async {
      await databaseService.setSetting('test_key', 'test_value');

      final value = await databaseService.getSetting('test_key');
      expect(value, 'test_value');

      await databaseService.setSetting('test_key', 'updated_value');
      final updatedValue = await databaseService.getSetting('test_key');
      expect(updatedValue, 'updated_value');
    });

    test('should calculate statistics correctly', () async {
      // Insert test sessions
      final session1 = FocusSession(
        id: '1',
        taskType: '深度工作',
        durationMinutes: 25,
        completed: true,
        startTime: DateTime.now(),
        endTime: DateTime.now().add(const Duration(minutes: 25)),
      );

      final session2 = FocusSession(
        id: '2',
        taskType: '学习充电',
        durationMinutes: 45,
        completed: true,
        startTime: DateTime.now().subtract(const Duration(hours: 1)),
        endTime: DateTime.now()
            .subtract(const Duration(hours: 1))
            .add(const Duration(minutes: 45)),
      );

      final session3 = FocusSession(
        id: '3',
        taskType: '冥想修行',
        durationMinutes: 30,
        completed: false,
        startTime: DateTime.now().subtract(const Duration(hours: 2)),
        isEscaped: true,
      );

      await databaseService.insertFocusSession(session1);
      await databaseService.insertFocusSession(session2);
      await databaseService.insertFocusSession(session3);

      final totalSessions = await databaseService.getTotalCompletedSessions();
      final totalMinutes = await databaseService.getTotalFocusTime();

      expect(totalSessions, 2); // Only completed sessions
      expect(totalMinutes, 70); // 25 + 45
    });

    test('should handle recent sessions limit', () async {
      // Insert multiple sessions
      for (int i = 0; i < 15; i++) {
        final session = FocusSession(
          id: i.toString(),
          taskType: '测试任务',
          durationMinutes: 25,
          completed: true,
          startTime: DateTime.now().subtract(Duration(hours: i)),
          endTime: DateTime.now()
              .subtract(Duration(hours: i))
              .add(const Duration(minutes: 25)),
        );
        await databaseService.insertFocusSession(session);
      }

      final recentSessions = await databaseService.getRecentSessions(limit: 5);
      expect(recentSessions.length, 5);

      // Should be ordered by start_time DESC
      for (int i = 0; i < recentSessions.length - 1; i++) {
        expect(
          recentSessions[i].startTime.isAfter(recentSessions[i + 1].startTime),
          true,
        );
      }
    });
  });
}
