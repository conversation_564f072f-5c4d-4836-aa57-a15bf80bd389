import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../utils/constants.dart';
import '../utils/time_formatter.dart';
import '../providers/statistics_provider.dart';

class StatisticsScreen extends StatefulWidget {
  const StatisticsScreen({super.key});

  @override
  State<StatisticsScreen> createState() => _StatisticsScreenState();
}

class _StatisticsScreenState extends State<StatisticsScreen> {
  @override
  void initState() {
    super.initState();
    // Load statistics when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<StatisticsProvider>().loadStatistics();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              _buildHeader(),

              // Content
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(AppDimensions.paddingLarge),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Main stats grid
                      _buildMainStatsGrid(),

                      const SizedBox(height: AppDimensions.paddingXLarge),

                      // Achievements section
                      _buildAchievementsSection(),

                      const SizedBox(height: AppDimensions.paddingXLarge),

                      // Weekly overview (placeholder)
                      _buildWeeklyOverview(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(
              Icons.arrow_back,
              color: AppColors.onSurface,
            ),
          ),
          const SizedBox(width: AppDimensions.paddingMedium),
          const Text(
            '我的锁机记录',
            style: AppTextStyles.titleMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildMainStatsGrid() {
    return Consumer<StatisticsProvider>(
      builder: (context, statsProvider, child) {
        if (statsProvider.isLoading) {
          return const Center(
            child: CircularProgressIndicator(color: AppColors.primary),
          );
        }

        return GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: AppDimensions.paddingMedium,
          mainAxisSpacing: AppDimensions.paddingMedium,
          childAspectRatio: 1.1,
          children: [
            _buildStatCard(
              value: statsProvider.totalSessions.toString(),
              label: '总锁机次数',
              icon: '🔒',
              color: AppColors.primary,
            ),
            _buildStatCard(
              value: TimeFormatter.formatTotalTime(statsProvider.totalMinutes),
              label: '总专注时长',
              icon: '⏱️',
              color: AppColors.secondary,
            ),
            _buildStatCard(
              value: statsProvider.currentStreak.toString(),
              label: '连续天数',
              icon: '🔥',
              color: AppColors.success,
            ),
            _buildStatCard(
              value: '${statsProvider.completionRate}%',
              label: '完成率',
              icon: '📈',
              color: AppColors.primary,
            ),
          ],
        );
      },
    );
  }

  Widget _buildStatCard({
    required String value,
    required String label,
    required String icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            icon,
            style: const TextStyle(fontSize: 32),
          ),
          const SizedBox(height: AppDimensions.paddingSmall),
          Text(
            value,
            style: AppTextStyles.titleLarge.copyWith(
              color: color,
              fontSize: 24,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: AppTextStyles.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAchievementsSection() {
    // Mock achievements for now - will be replaced with real achievement provider
    final mockAchievements = [
      '首次锁机 - 迈出第一步',
      '连续3天 - 习惯养成',
      '单次45分钟 - 深度专注',
      '总计5小时 - 自律达人',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Row(
          children: [
            Text(
              '🏆',
              style: TextStyle(fontSize: 24),
            ),
            SizedBox(width: AppDimensions.paddingSmall),
            Text(
              '解锁成就',
              style: AppTextStyles.titleMedium,
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.paddingMedium),
        Container(
          padding: const EdgeInsets.all(AppDimensions.paddingLarge),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
            border: Border.all(
              color: AppColors.onSurface.withOpacity(0.1),
              width: 1,
            ),
          ),
          child: Column(
            children: mockAchievements.map((achievement) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Row(
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: const BoxDecoration(
                        color: AppColors.primary,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: AppDimensions.paddingMedium),
                    Expanded(
                      child: Text(
                        achievement,
                        style: AppTextStyles.bodyMedium,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildWeeklyOverview() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Row(
          children: [
            Text(
              '📊',
              style: TextStyle(fontSize: 24),
            ),
            SizedBox(width: AppDimensions.paddingSmall),
            Text(
              '本周概览',
              style: AppTextStyles.titleMedium,
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.paddingMedium),
        Container(
          height: 120,
          padding: const EdgeInsets.all(AppDimensions.paddingLarge),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
            border: Border.all(
              color: AppColors.onSurface.withOpacity(0.1),
              width: 1,
            ),
          ),
          child: const Center(
            child: Text(
              '图表功能开发中...',
              style: AppTextStyles.bodyMedium,
            ),
          ),
        ),
      ],
    );
  }
}
