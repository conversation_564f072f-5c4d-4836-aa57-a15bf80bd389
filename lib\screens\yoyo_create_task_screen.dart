import 'package:flutter/material.dart';
import '../utils/constants.dart';
import '../models/focus_session.dart';
import '../services/permission_manager.dart';
import '../services/focus_manager.dart' as focus_service;
import '../services/accessibility_fallback_service.dart';
import '../widgets/accessibility_fallback_dialog.dart';
import '../widgets/simplified_permission_dialog.dart';
import 'yoyo_focus_screen.dart';

/// YoYo日常风格的任务创建界面
/// 简洁、直观的专注任务设置
class YoYoCreateTaskScreen extends StatefulWidget {
  const YoYoCreateTaskScreen({super.key});

  @override
  State<YoYoCreateTaskScreen> createState() => _YoYoCreateTaskScreenState();
}

class _YoYoCreateTaskScreenState extends State<YoYoCreateTaskScreen> {
  TaskType? selectedTaskType;
  int selectedDuration = 25; // 默认25分钟
  focus_service.LockLevel selectedLockLevel = focus_service.LockLevel.basic;

  final List<int> durationOptions = [15, 25, 45, 60, 90];
  bool isCustomDuration = false;
  final TextEditingController customDurationController =
      TextEditingController();

  @override
  void dispose() {
    customDurationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingLarge),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // 头部
                _buildHeader(),

                const SizedBox(height: AppDimensions.paddingXLarge),

                // 内容区域
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // 任务类型选择
                        _buildTaskTypeSection(),

                        const SizedBox(height: AppDimensions.paddingXLarge),

                        // 专注时长选择
                        _buildDurationSection(),

                        const SizedBox(height: AppDimensions.paddingXLarge),

                        // 锁定级别选择
                        _buildLockLevelSection(),
                      ],
                    ),
                  ),
                ),

                // 开始按钮
                _buildStartButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(
            Icons.arrow_back,
            color: AppColors.onSurface,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          '创建专注任务',
          style: AppTextStyles.titleLarge.copyWith(
            color: AppColors.onSurface,
          ),
        ),
      ],
    );
  }

  Widget _buildTaskTypeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '📝 选择任务类型',
          style: AppTextStyles.titleMedium.copyWith(
            color: AppColors.onSurface,
          ),
        ),
        const SizedBox(height: AppDimensions.paddingMedium),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: TaskType.values.map((type) {
            final isSelected = selectedTaskType == type;
            return GestureDetector(
              onTap: () {
                setState(() {
                  selectedTaskType = type;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  color: isSelected
                      ? AppColors.primary
                      : AppColors.surface.withOpacity(0.3),
                  borderRadius:
                      BorderRadius.circular(AppDimensions.radiusMedium),
                  border: Border.all(
                    color: isSelected ? AppColors.primary : Colors.transparent,
                    width: 2,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      type.icon,
                      style: const TextStyle(fontSize: 20),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      type.displayName,
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: isSelected ? Colors.black : AppColors.onSurface,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildDurationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '⏰ 专注时长',
          style: AppTextStyles.titleMedium.copyWith(
            color: AppColors.onSurface,
          ),
        ),
        const SizedBox(height: AppDimensions.paddingMedium),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: [
            ...durationOptions.map((duration) {
              final isSelected =
                  selectedDuration == duration && !isCustomDuration;
              return _buildDurationOption(
                '$duration分钟',
                isSelected,
                () {
                  setState(() {
                    selectedDuration = duration;
                    isCustomDuration = false;
                  });
                },
              );
            }),
            _buildDurationOption(
              '自定义',
              isCustomDuration,
              () {
                setState(() {
                  isCustomDuration = true;
                });
                _showCustomDurationDialog();
              },
            ),
          ],
        ),
        if (isCustomDuration) ...[
          const SizedBox(height: 12),
          Text(
            '自定义时长: $selectedDuration分钟',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.primary,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDurationOption(
      String text, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.primary
              : AppColors.surface.withOpacity(0.3),
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          border: Border.all(
            color: isSelected ? AppColors.primary : Colors.transparent,
            width: 2,
          ),
        ),
        child: Text(
          text,
          style: AppTextStyles.bodyMedium.copyWith(
            color: isSelected ? Colors.black : AppColors.onSurface,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildLockLevelSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '🔒 锁定级别',
          style: AppTextStyles.titleMedium.copyWith(
            color: AppColors.onSurface,
          ),
        ),
        const SizedBox(height: AppDimensions.paddingMedium),
        ...focus_service.LockLevel.values.map((level) {
          final isSelected = selectedLockLevel == level;
          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            child: GestureDetector(
              onTap: () {
                setState(() {
                  selectedLockLevel = level;
                });
              },
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isSelected
                      ? AppColors.primary.withOpacity(0.1)
                      : AppColors.surface.withOpacity(0.3),
                  borderRadius:
                      BorderRadius.circular(AppDimensions.radiusMedium),
                  border: Border.all(
                    color: isSelected ? AppColors.primary : Colors.transparent,
                    width: 2,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      _getLockIcon(level),
                      color: isSelected
                          ? AppColors.primary
                          : AppColors.onSurface.withOpacity(0.7),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _getLockLevelName(level),
                            style: AppTextStyles.bodyLarge.copyWith(
                              color: isSelected
                                  ? AppColors.primary
                                  : AppColors.onSurface,
                              fontWeight: isSelected
                                  ? FontWeight.w600
                                  : FontWeight.normal,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _getLockLevelDescription(level),
                            style: AppTextStyles.bodySmall.copyWith(
                              color: AppColors.onSurface.withOpacity(0.6),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }),
      ],
    );
  }

  IconData _getLockIcon(focus_service.LockLevel level) {
    switch (level) {
      case focus_service.LockLevel.basic:
        return Icons.lock_outline;
      case focus_service.LockLevel.enhanced:
        return Icons.lock;
      case focus_service.LockLevel.deep:
        return Icons.security;
    }
  }

  String _getLockLevelName(focus_service.LockLevel level) {
    switch (level) {
      case focus_service.LockLevel.basic:
        return '基础锁定';
      case focus_service.LockLevel.enhanced:
        return '增强锁定';
      case focus_service.LockLevel.deep:
        return '深度锁定';
    }
  }

  String _getLockLevelDescription(focus_service.LockLevel level) {
    switch (level) {
      case focus_service.LockLevel.basic:
        return '应用级锁定，阻止返回键';
      case focus_service.LockLevel.enhanced:
        return '系统级锁定，需要悬浮窗权限';
      case focus_service.LockLevel.deep:
        return '深度锁定，需要无障碍服务权限';
    }
  }

  Widget _buildStartButton() {
    final canStart = selectedTaskType != null;

    return ElevatedButton(
      onPressed: canStart ? _startFocus : null,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.black,
        minimumSize: const Size(double.infinity, AppDimensions.buttonHeight),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
        ),
      ),
      child: Text(
        '开始专注',
        style: AppTextStyles.labelLarge.copyWith(
          color: Colors.black,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  void _showCustomDurationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('自定义时长'),
        content: TextField(
          controller: customDurationController,
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(
            labelText: '分钟',
            hintText: '请输入专注时长',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              final duration = int.tryParse(customDurationController.text);
              if (duration != null && duration > 0 && duration <= 300) {
                setState(() {
                  selectedDuration = duration;
                });
                Navigator.of(context).pop();
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('请输入1-300之间的数字')),
                );
              }
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _startFocus() async {
    if (selectedTaskType == null) return;

    // 设置锁定级别
    focus_service.FocusManager.instance.setLockLevel(selectedLockLevel);

    // 使用简化的权限检查流程
    await _checkPermissionsAndStart();
  }

  /// 检查权限并启动专注会话
  Future<void> _checkPermissionsAndStart() async {
    final permissionManager = PermissionManager.instance;

    // 根据锁定级别检查所需权限
    bool hasRequiredPermissions = false;

    switch (selectedLockLevel) {
      case focus_service.LockLevel.basic:
        hasRequiredPermissions = await permissionManager.hasBasicPermissions();
        break;
      case focus_service.LockLevel.enhanced:
        hasRequiredPermissions =
            await permissionManager.hasBasicPermissions() &&
                await permissionManager.hasEnhancedPermissions();
        break;
      case focus_service.LockLevel.deep:
        // 深度锁定优化：不强制要求无障碍权限，可以降级到超级增强锁定
        hasRequiredPermissions =
            await permissionManager.hasBasicPermissions() &&
                await permissionManager.hasEnhancedPermissions();
        // 注意：无障碍权限不再是必需的，系统会自动降级
        break;
    }

    if (hasRequiredPermissions) {
      // 权限满足，直接启动专注
      await _launchFocusSession();
    } else {
      // 权限不满足，显示简化的权限授权对话框
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => SimplifiedPermissionDialog(
            lockLevel: selectedLockLevel,
            onPermissionsGranted: () async {
              // 权限授权成功，启动专注会话
              await _launchFocusSession();
            },
            onPermissionsDenied: () {
              // 权限授权失败，显示提示
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content:
                      Text('${_getLockLevelName(selectedLockLevel)}需要相应权限才能使用'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
          ),
        );
      }
    }
  }

  /// 启动专注会话
  Future<void> _launchFocusSession() async {
    // 导航到专注界面
    if (mounted) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => YoYoFocusScreen(
            taskType: selectedTaskType!.displayName,
            durationMinutes: selectedDuration,
          ),
        ),
      );
    }
  }

  /// 增强版无障碍权限处理
  Future<bool> _handleEnhancedAccessibilityPermission() async {
    try {
      // 使用回退服务尝试所有可能的启用方法
      final result =
          await AccessibilityFallbackService.instance.tryAllEnableMethods();

      if (result.success) {
        // 成功启用
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('✅ ${result.message}'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        }
        return true;
      } else {
        // 启用失败，显示回退选项
        if (mounted && result.fallbackOptions.isNotEmpty) {
          await _showFallbackOptionsDialog(result);
        }
        return false;
      }
    } catch (e) {
      // 发生错误，回退到传统方法
      if (mounted) {
        return await _showAccessibilityPermissionDialog();
      }
      return false;
    }
  }

  /// 显示回退选项对话框
  Future<void> _showFallbackOptionsDialog(
      AccessibilityEnableResult result) async {
    await showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AccessibilityFallbackDialog(
        result: result,
        onOptionSelected: () {
          // 用户选择了某个选项，可能需要重新检查权限状态
          _recheckAccessibilityPermission();
        },
        onCancel: () {
          // 用户取消了设置
        },
      ),
    );
  }

  /// 重新检查无障碍权限状态
  Future<void> _recheckAccessibilityPermission() async {
    // 延迟一下再检查，给用户时间完成设置
    await Future.delayed(const Duration(milliseconds: 500));

    final status = await PermissionManager.instance
        .checkPermission(PermissionType.accessibility);
    if (status == PermissionStatus.granted) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ 无障碍服务已启用，现在可以开始专注'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('请完成无障碍服务设置，然后重新点击"开始专注"'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 3),
          ),
        );
      }
    }
  }

  /// 显示无障碍权限说明对话框（传统方法）
  Future<bool> _showAccessibilityPermissionDialog() async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.security, color: Colors.blue),
            SizedBox(width: 8),
            Text('深度锁定权限'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '深度锁定模式需要启用无障碍服务来确保专注效果：',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
            SizedBox(height: 12),
            Text('✅ 阻止所有系统手势和按键'),
            Text('✅ 防止切换到其他应用'),
            Text('✅ 确保真正的专注体验'),
            SizedBox(height: 12),
            Text(
              '启用后不会立即进入锁屏，您需要主动点击"开始专注"按钮。专注结束后我们会自动关闭此服务。',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('同意并启用'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  /// 显示详细诊断对话框
  Future<void> _showDetailedDiagnosisDialog(Map<String, dynamic> systemStatus,
      Map<String, dynamic> rootStatus) async {
    final buffer = StringBuffer();
    buffer.writeln('🔍 系统诊断结果：\n');

    // 权限状态
    final hasWriteSecure = systemStatus['hasWriteSecureSettings'] == true;
    final hasWriteSettings = systemStatus['hasWriteSettings'] == true;

    buffer.writeln('📋 权限状态：');
    buffer.writeln('${hasWriteSecure ? '✅' : '❌'} WRITE_SECURE_SETTINGS权限');
    buffer.writeln('${hasWriteSettings ? '✅' : '❌'} WRITE_SETTINGS权限\n');

    // ROOT状态
    final isRootAvailable = rootStatus['isRootAvailable'] == true;
    final hasRootManager = rootStatus['hasRootManager'] == true;
    final hasSuBinary = rootStatus['hasSuBinary'] == true;

    buffer.writeln('🔐 ROOT状态：');
    buffer.writeln('${isRootAvailable ? '✅' : '❌'} ROOT权限可用');
    buffer.writeln('${hasRootManager ? '✅' : '❌'} ROOT管理器已安装');
    buffer.writeln('${hasSuBinary ? '✅' : '❌'} SU二进制文件存在\n');

    if (rootStatus['installedRootManagers'] is List) {
      final managers = rootStatus['installedRootManagers'] as List;
      if (managers.isNotEmpty) {
        buffer.writeln('已安装的ROOT管理器：');
        for (var manager in managers) {
          buffer.writeln('• $manager');
        }
        buffer.writeln('');
      }
    }

    // 设备信息
    if (systemStatus['deviceInfo'] is Map) {
      final deviceInfo = systemStatus['deviceInfo'] as Map;
      buffer.writeln('📱 设备信息：');
      buffer.writeln('厂商: ${deviceInfo['manufacturer']}');
      buffer.writeln('型号: ${deviceInfo['model']}');
      buffer.writeln(
          '系统: Android ${deviceInfo['version']} (API ${deviceInfo['sdk']})\n');
    }

    // 解决方案
    buffer.writeln('🛠️ 解决方案：\n');

    if (!hasWriteSecure) {
      if (isRootAvailable) {
        buffer.writeln('1. ROOT设备解决方案：');
        buffer.writeln('   • 点击"尝试Root授权"按钮');
        buffer.writeln('   • 在ROOT管理器中授予权限\n');
      } else {
        buffer.writeln('1. 非ROOT设备解决方案：');
        buffer.writeln('   • 运行ADB权限脚本');
        buffer.writeln('   • 点击"生成ADB命令"获取脚本\n');
      }

      buffer.writeln('2. 手动ADB命令：');
      buffer.writeln('   adb shell pm grant com.example.lockphone \\');
      buffer.writeln('   android.permission.WRITE_SECURE_SETTINGS\n');
    }

    buffer.writeln('3. 手动启用无障碍服务：');
    buffer.writeln('   设置 → 无障碍 → 专注锁屏服务');

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.bug_report, color: Colors.red),
            SizedBox(width: 8),
            Text('自动启用失败'),
          ],
        ),
        content: SingleChildScrollView(
          child: Text(
            buffer.toString(),
            style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          if (!hasWriteSecure && isRootAvailable)
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                // 尝试Root授予权限
                final rootGranted =
                    await PermissionManager.instance.grantPermissionsViaRoot();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content:
                          Text(rootGranted ? 'Root权限授予成功，请重试' : 'Root权限授予失败'),
                      backgroundColor: rootGranted ? Colors.green : Colors.red,
                    ),
                  );
                }
              },
              child: const Text('尝试Root授权'),
            ),
          if (!hasWriteSecure && !isRootAvailable)
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _showAdbCommandsDialog();
              },
              child: const Text('生成ADB命令'),
            ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              PermissionManager.instance
                  .openPermissionSettings(PermissionType.accessibility);
            },
            child: const Text('手动设置'),
          ),
        ],
      ),
    );
  }

  /// 显示ADB命令对话框
  Future<void> _showAdbCommandsDialog() async {
    final adbCommands = await PermissionManager.instance.generateAdbCommands();

    if (adbCommands['error'] != null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('生成ADB命令失败: ${adbCommands['error']}'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    final commands = adbCommands['commands'] as List? ?? [];
    // final batchScript = adbCommands['batchScript'] as String? ?? ''; // 暂时未使用

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.terminal, color: Colors.blue),
            SizedBox(width: 8),
            Text('ADB权限授予命令'),
          ],
        ),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '请在电脑上执行以下命令：',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 12),
              const Text('1. 确保设备已连接并启用USB调试'),
              const Text('2. 在电脑上打开命令行/终端'),
              const Text('3. 逐行执行以下命令：'),
              const SizedBox(height: 12),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: SingleChildScrollView(
                    child: SelectableText(
                      commands.take(4).join('\n\n'), // 只显示前4个命令
                      style: const TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                '💡 提示：也可以保存为.bat文件直接运行',
                style: TextStyle(color: Colors.blue, fontSize: 12),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
          ElevatedButton(
            onPressed: () {
              // 复制命令到剪贴板
              // final commandText = commands.take(4).join('\n\n'); // 暂时未使用
              // 这里可以添加复制到剪贴板的功能
              Navigator.of(context).pop();
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('命令已准备好，请在电脑上执行'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            child: const Text('我知道了'),
          ),
        ],
      ),
    );
  }

  /// 显示手动启用引导对话框
  Future<void> _showManualEnableDialog() async {
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.settings_accessibility, color: Colors.orange),
            SizedBox(width: 8),
            Text('需要手动启用'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '自动启用失败，可能是因为：',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
            SizedBox(height: 8),
            Text('• 缺少系统权限（需要运行权限脚本）'),
            Text('• 设备安全策略限制'),
            Text('• 系统版本兼容性问题'),
            SizedBox(height: 12),
            Text(
              '请手动启用无障碍服务：',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
            SizedBox(height: 8),
            Text('1. 点击"去设置"按钮'),
            Text('2. 找到"专注锁屏服务"'),
            Text('3. 开启服务开关'),
            Text('4. 立即按返回键回到应用'),
            Text('5. 重新点击"开始专注"'),
            SizedBox(height: 12),
            Text(
              '⚠️ 重要：启用服务后请立即返回应用，不要停留在设置页面',
              style: TextStyle(
                  color: Colors.red, fontSize: 12, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 4),
            Text(
              '💡 提示：启用后不会立即锁屏，需要您主动点击开始按钮',
              style: TextStyle(color: Colors.blue, fontSize: 12),
            ),
            Text(
              '🔄 专注结束后我们会自动关闭此服务',
              style: TextStyle(color: Colors.green, fontSize: 12),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              PermissionManager.instance
                  .openPermissionSettings(PermissionType.accessibility);
            },
            child: const Text('去设置'),
          ),
        ],
      ),
    );
  }
}
