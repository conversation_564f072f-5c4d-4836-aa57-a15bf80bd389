import 'package:flutter/material.dart';
import '../services/permission_manager.dart';
import '../services/focus_manager.dart' as focus_service;
import '../utils/constants.dart';

/// 简化的权限检查和授权对话框
/// 专为"开始专注"按钮优化，减少用户操作步骤
class SimplifiedPermissionDialog extends StatefulWidget {
  final focus_service.LockLevel lockLevel;
  final VoidCallback? onPermissionsGranted;
  final VoidCallback? onPermissionsDenied;

  const SimplifiedPermissionDialog({
    super.key,
    required this.lockLevel,
    this.onPermissionsGranted,
    this.onPermissionsDenied,
  });

  @override
  State<SimplifiedPermissionDialog> createState() =>
      _SimplifiedPermissionDialogState();
}

class _SimplifiedPermissionDialogState
    extends State<SimplifiedPermissionDialog> {
  bool _isProcessing = false;
  List<PermissionType> _requiredPermissions = [];
  final Map<PermissionType, PermissionStatus> _permissionStatus = {};
  String _statusMessage = '';

  @override
  void initState() {
    super.initState();
    _initializePermissions();
  }

  void _initializePermissions() {
    // 根据锁定级别确定需要的权限
    switch (widget.lockLevel) {
      case focus_service.LockLevel.basic:
        _requiredPermissions = [PermissionType.storage];
        break;
      case focus_service.LockLevel.enhanced:
        _requiredPermissions = [PermissionType.storage];
        break;
      case focus_service.LockLevel.deep:
        // 深度锁定基于五层防护体系，只需要基础存储权限
        _requiredPermissions = [
          PermissionType.storage,
          // 可选增强权限（不强制要求）：
          // PermissionType.accessibility, // 无障碍服务
          // PermissionType.deviceAdmin,   // 设备管理员
        ];
        break;
    }
    _checkCurrentPermissions();
  }

  Future<void> _checkCurrentPermissions() async {
    setState(() {
      _isProcessing = true;
      _statusMessage = '正在检查权限状态...';
    });

    final permissionManager = PermissionManager.instance;

    for (final permission in _requiredPermissions) {
      final status = await permissionManager.checkPermission(permission);
      _permissionStatus[permission] = status;
    }

    final missingPermissions = _permissionStatus.entries
        .where((entry) => entry.value != PermissionStatus.granted)
        .map((entry) => entry.key)
        .toList();

    if (missingPermissions.isEmpty) {
      // 所有权限都已授权
      setState(() {
        _isProcessing = false;
        _statusMessage = '权限检查完成，可以开始专注！';
      });

      // 延迟一下让用户看到成功消息
      await Future.delayed(const Duration(milliseconds: 500));

      if (mounted) {
        Navigator.of(context).pop();
        widget.onPermissionsGranted?.call();
      }
    } else {
      setState(() {
        _isProcessing = false;
        _statusMessage = _buildMissingPermissionsMessage(missingPermissions);
      });
    }
  }

  String _buildMissingPermissionsMessage(
      List<PermissionType> missingPermissions) {
    final lockLevelName = _getLockLevelName(widget.lockLevel);
    final permissionNames =
        missingPermissions.map(_getPermissionName).join('、');

    return '使用$lockLevelName需要以下权限：\n$permissionNames\n\n点击"一键授权"开始权限设置';
  }

  String _getLockLevelName(focus_service.LockLevel level) {
    switch (level) {
      case focus_service.LockLevel.basic:
        return '基础锁定';
      case focus_service.LockLevel.enhanced:
        return '增强锁定';
      case focus_service.LockLevel.deep:
        return '深度锁定';
    }
  }

  String _getPermissionName(PermissionType type) {
    switch (type) {
      case PermissionType.storage:
        return '存储权限';
      case PermissionType.accessibility:
        return '无障碍服务';
      case PermissionType.deviceAdmin:
        return '设备管理员权限';
      case PermissionType.notification:
        return '通知权限';
    }
  }

  Future<void> _requestAllPermissions() async {
    setState(() {
      _isProcessing = true;
      _statusMessage = '正在申请权限，请按照系统提示操作...';
    });

    final permissionManager = PermissionManager.instance;

    // 逐个请求权限
    for (final permission in _requiredPermissions) {
      if (_permissionStatus[permission] != PermissionStatus.granted) {
        setState(() {
          _statusMessage = '正在申请${_getPermissionName(permission)}...';
        });

        bool granted = false;

        // 对于无障碍服务，使用特殊处理
        if (permission == PermissionType.accessibility) {
          granted = await _handleAccessibilityPermission();
        } else {
          granted = await permissionManager.requestPermission(permission);
        }

        if (granted) {
          _permissionStatus[permission] = PermissionStatus.granted;
        }

        // 授权后等待一下，然后重新检查权限状态
        await Future.delayed(const Duration(milliseconds: 500));
        final actualStatus =
            await permissionManager.checkPermission(permission);
        _permissionStatus[permission] = actualStatus;
      }
    }

    // 权限申请完成后，重新检查所有权限状态
    setState(() {
      _statusMessage = '正在验证权限状态...';
    });

    await Future.delayed(const Duration(milliseconds: 500));
    await _checkCurrentPermissions();

    // 检查是否所有权限都已授权
    final finalMissingPermissions = _permissionStatus.entries
        .where((entry) => entry.value != PermissionStatus.granted)
        .map((entry) => entry.key)
        .toList();

    if (finalMissingPermissions.isEmpty) {
      setState(() {
        _statusMessage = '所有权限已授权，可以开始专注！';
      });

      await Future.delayed(const Duration(milliseconds: 1000));

      if (mounted) {
        Navigator.of(context).pop();
        widget.onPermissionsGranted?.call();
      }
    } else {
      setState(() {
        _isProcessing = false;
        _statusMessage = '部分权限未授权，您可以：\n1. 重试授权\n2. 降低锁定级别\n3. 手动设置权限';
      });
    }
  }

  Future<bool> _handleAccessibilityPermission() async {
    final permissionManager = PermissionManager.instance;

    // 首先尝试自动启用
    final autoEnabled =
        await permissionManager.enableAccessibilityServiceAuto();

    if (autoEnabled) {
      return true;
    }

    // 如果自动启用失败，引导用户手动设置
    if (mounted) {
      final shouldOpenSettings = await showDialog<bool>(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: const Text('需要开启无障碍服务'),
          content: const Text(
            '深度锁定需要无障碍服务支持。\n\n'
            '点击"去设置"将跳转到系统设置页面，'
            '请找到"专注锁屏服务"并开启。',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('去设置'),
            ),
          ],
        ),
      );

      if (shouldOpenSettings == true) {
        await permissionManager
            .openPermissionSettings(PermissionType.accessibility);

        // 等待用户设置完成
        await Future.delayed(const Duration(seconds: 2));

        // 重新检查权限状态
        final status = await permissionManager
            .checkPermission(PermissionType.accessibility);
        return status == PermissionStatus.granted;
      }
    }

    return false;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Row(
        children: [
          Icon(
            Icons.security,
            color: AppColors.primary,
            size: 24,
          ),
          SizedBox(width: 8),
          Text('权限检查'),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_isProcessing)
            const Center(
              child: CircularProgressIndicator(color: AppColors.primary),
            ),
          const SizedBox(height: 16),
          Text(
            _statusMessage,
            style: AppTextStyles.bodyMedium,
          ),
        ],
      ),
      actions: [
        if (!_isProcessing) ...[
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              widget.onPermissionsDenied?.call();
            },
            child: const Text('取消'),
          ),
          if (_permissionStatus.values
              .any((status) => status != PermissionStatus.granted))
            ElevatedButton(
              onPressed: _requestAllPermissions,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.black,
              ),
              child: const Text('一键授权'),
            ),
        ],
      ],
    );
  }
}
